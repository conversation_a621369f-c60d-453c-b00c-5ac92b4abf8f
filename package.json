{"name": "nolk-v4", "version": "0.1.0", "private": true, "scripts": {"dev": "kill-port 6699 || true && next dev --turbopack --port 6699", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start --port 6699", "start:prod": "NODE_ENV=production next start --port 6699", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:performance": "jest tests/performance", "test:load": "jest tests/load", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:comprehensive": "playwright test tests/e2e/comprehensive-*.spec.ts", "test:e2e:working": "playwright test tests/e2e/working-comprehensive.spec.ts", "test:e2e:smoke": "playwright test tests/e2e/comprehensive-test-runner.spec.ts", "test:e2e:dashboard": "playwright test tests/e2e/comprehensive-dashboard.spec.ts", "test:e2e:marketing": "playwright test tests/e2e/comprehensive-marketing-dashboard.spec.ts", "test:e2e:admin": "playwright test tests/e2e/comprehensive-admin-dashboard.spec.ts", "test:e2e:api": "playwright test tests/e2e/comprehensive-api.spec.ts", "test:e2e:performance": "playwright test tests/e2e/comprehensive-performance.spec.ts", "test:e2e:errors": "playwright test tests/e2e/comprehensive-error-handling.spec.ts", "test:everything": "node scripts/test-everything.mjs", "test:performance:full": "node tests/scripts/run-performance-tests.js", "test:ci": "jest --ci --coverage --watchAll=false", "test:all": "npm run test && npm run test:e2e", "monitor:performance": "node -r ts-node/register tests/scripts/performance-monitor.ts", "report:performance": "node tests/scripts/run-performance-tests.js --report-only", "health-check": "curl -f http://localhost:6699/api/monitoring/health || exit 1", "db:migrate": "node scripts/migrate-database.js", "db:seed": "node scripts/add-admin-users.mjs", "security:audit": "npm audit --audit-level moderate", "clean": "rm -rf .next out dist", "postinstall": "npm run type-check"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@tabler/icons-react": "^3.31.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-table": "^8.21.3", "@types/bcrypt": "^5.0.2", "@types/pg": "^8.15.1", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "csv-parse": "^5.6.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.510.0", "next": "15.3.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "pg": "^8.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "tailwind-merge": "^3.3.0", "vaul": "^1.1.2", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.52.0", "@rushstack/eslint-patch": "^1.11.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/supertest": "^6.0.3", "eslint": "^9", "eslint-config-next": "15.3.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "kill-port": "^2.0.1", "node-fetch": "^2.7.0", "supertest": "^7.1.1", "tailwindcss": "^4", "ts-jest": "^29.3.4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}