import { Page, expect } from '@playwright/test';

/**
 * Helper functions for E2E tests
 */

/**
 * Wait for the page to be fully loaded
 */
export async function waitForPageLoad(page: Page) {
  await page.waitForLoadState('networkidle');
  await page.waitForLoadState('domcontentloaded');
}

/**
 * Sign in with Google (mock for testing)
 */
export async function signInWithGoogle(page: Page) {
  // Navigate to sign-in page
  await page.goto('/auth/signin');
  await waitForPageLoad(page);

  // Click the Google sign-in button
  await page.click('button:has-text("Continue with Google")');

  // In a real test environment, you would handle OAuth flow
  // For now, we'll assume the sign-in is successful and we're redirected
  await page.waitForURL('/dashboard');
  await waitForPageLoad(page);
}

/**
 * Sign in with username and password credentials
 */
export async function signInWithCredentials(page: Page, username: string, password: string) {
  // Navigate to sign-in page
  await page.goto('/auth/signin');
  await waitForPageLoad(page);

  // Switch to credentials login
  const credentialsTab = page.locator('button:has-text("Username/Password")');
  await credentialsTab.click();
  await page.waitForTimeout(1000); // Wait for tab switch animation

  // Wait for the form to be visible
  await page.waitForSelector('#username', { state: 'visible', timeout: 5000 });

  // Fill in credentials using id selectors
  await page.fill('#username', username);
  await page.fill('#password', password);

  // Click sign in button
  await page.click('button[type="submit"]:has-text("Sign In")');

  // Wait for redirect to dashboard
  await page.waitForURL('/dashboard', { timeout: 15000 });
  await waitForPageLoad(page);
}

/**
 * Sign in with the test admin user (farbour/admin)
 */
export async function signInAsAdmin(page: Page) {
  await signInWithCredentials(page, 'farbour', 'admin');
}

/**
 * Sign out from the application
 */
export async function signOut(page: Page) {
  // Look for user menu or sign out button
  const userMenu = page.locator('[data-testid="user-menu"]').or(
    page.locator('button:has-text("Sign out")')
  );

  if (await userMenu.isVisible()) {
    await userMenu.click();

    // Look for sign out option
    const signOutButton = page.locator('button:has-text("Sign out")').or(
      page.locator('[data-testid="sign-out"]')
    );

    if (await signOutButton.isVisible()) {
      await signOutButton.click();
    }
  }

  // Wait for redirect to sign-in page
  await page.waitForURL('/auth/signin');
}

/**
 * Navigate to a specific page and wait for it to load
 */
export async function navigateToPage(page: Page, path: string) {
  await page.goto(path);
  await waitForPageLoad(page);
}

/**
 * Check if user is authenticated
 */
export async function isAuthenticated(page: Page): Promise<boolean> {
  try {
    // Check if we're on the dashboard or any authenticated page
    const currentUrl = page.url();
    return !currentUrl.includes('/auth/signin');
  } catch {
    return false;
  }
}

/**
 * Wait for a specific element to be visible
 */
export async function waitForElement(page: Page, selector: string, timeout = 10000) {
  await page.waitForSelector(selector, { state: 'visible', timeout });
}

/**
 * Take a screenshot with a descriptive name
 */
export async function takeScreenshot(page: Page, name: string) {
  await page.screenshot({
    path: `tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`,
    fullPage: true
  });
}

/**
 * Check if the sidebar navigation is working
 */
export async function testSidebarNavigation(page: Page) {
  // Test main navigation items
  const navItems = [
    { text: 'Dashboard', url: '/dashboard' },
    { text: 'Brand Deep Dive', url: '/brand-deep-dive' },
    { text: 'Marketing Dashboard', url: '/marketing-dashboard' },
    { text: 'Executive Summary', url: '/executive-summary' },
    { text: 'Budget', url: '/budget' },
    { text: 'AI Assistant', url: '/ai-assistant' }
  ];

  for (const item of navItems) {
    const navLink = page.locator(`nav a:has-text("${item.text}")`);
    if (await navLink.isVisible()) {
      await navLink.click();
      await page.waitForURL(`**${item.url}`);
      await waitForPageLoad(page);

      // Verify we're on the correct page
      expect(page.url()).toContain(item.url);
    }
  }
}

/**
 * Test responsive design by changing viewport
 */
export async function testResponsiveDesign(page: Page) {
  const viewports = [
    { width: 1920, height: 1080, name: 'Desktop' },
    { width: 768, height: 1024, name: 'Tablet' },
    { width: 375, height: 667, name: 'Mobile' }
  ];

  for (const viewport of viewports) {
    await page.setViewportSize({ width: viewport.width, height: viewport.height });
    await page.waitForTimeout(1000); // Allow time for responsive changes

    // Take screenshot for visual verification
    await takeScreenshot(page, `responsive-${viewport.name.toLowerCase()}`);
  }
}

/**
 * Check for console errors
 */
export async function checkConsoleErrors(page: Page): Promise<string[]> {
  const errors: string[] = [];

  page.on('console', (msg) => {
    if (msg.type() === 'error') {
      errors.push(msg.text());
    }
  });

  return errors;
}

/**
 * Mock authentication for testing
 */
export async function mockAuthentication(page: Page) {
  // Add authentication cookies or local storage
  await page.addInitScript(() => {
    // Mock session data
    window.localStorage.setItem('test-auth', 'true');
  });
}

/**
 * Navigate to admin dashboard and verify access
 */
export async function navigateToAdminDashboard(page: Page) {
  await page.goto('/admin');
  await waitForPageLoad(page);

  // Verify we're on the admin page and not redirected to sign-in
  const currentUrl = page.url();
  expect(currentUrl).toContain('/admin');
}

/**
 * Verify user has admin privileges
 */
export async function verifyAdminAccess(page: Page): Promise<boolean> {
  try {
    await page.goto('/admin');
    await waitForPageLoad(page);

    // Check if we're still on admin page (not redirected to sign-in)
    const currentUrl = page.url();
    return currentUrl.includes('/admin') && !currentUrl.includes('/auth/signin');
  } catch {
    return false;
  }
}

/**
 * Test navigation through all main sections
 */
export async function testMainNavigation(page: Page) {
  const sections = [
    { path: '/dashboard', name: 'Dashboard' },
    { path: '/marketing', name: 'Marketing' },
    { path: '/admin', name: 'Admin' }
  ];

  for (const section of sections) {
    await page.goto(section.path);
    await waitForPageLoad(page);

    // Verify we reached the intended page
    const currentUrl = page.url();
    expect(currentUrl).toContain(section.path);

    // Take screenshot for visual verification
    await takeScreenshot(page, `navigation-${section.name.toLowerCase()}`);
  }
}

/**
 * Verify all expected UI elements are present on a page
 */
export async function verifyPageElements(page: Page, expectedElements: string[]) {
  for (const element of expectedElements) {
    const locator = page.locator(element).first();
    await expect(locator).toBeVisible({ timeout: 5000 });
  }
}

/**
 * Test API endpoint response
 */
export async function testApiEndpoint(page: Page, endpoint: string, expectedStatus = 200) {
  const response = await page.request.get(endpoint);
  expect(response.status()).toBe(expectedStatus);
  return response;
}

/**
 * Test form submission
 */
export async function testFormSubmission(page: Page, formSelector: string, formData: Record<string, string>) {
  const form = page.locator(formSelector);
  await expect(form).toBeVisible();

  // Fill form fields
  for (const [field, value] of Object.entries(formData)) {
    await page.fill(`${formSelector} input[name="${field}"], ${formSelector} select[name="${field}"]`, value);
  }

  // Submit form
  await page.click(`${formSelector} button[type="submit"]`);
}

/**
 * Test data table functionality
 */
export async function testDataTable(page: Page, tableSelector = 'table') {
  const table = page.locator(tableSelector);
  await expect(table).toBeVisible();

  // Check for headers
  const headers = table.locator('thead th');
  const headerCount = await headers.count();
  expect(headerCount).toBeGreaterThan(0);

  // Check for data rows
  const rows = table.locator('tbody tr');
  const rowCount = await rows.count();

  return { headerCount, rowCount };
}

/**
 * Test chart rendering
 */
export async function testChartRendering(page: Page, chartSelector = 'svg, canvas, .recharts-wrapper') {
  const chart = page.locator(chartSelector);
  await expect(chart).toBeVisible({ timeout: 10000 });

  // Wait for chart to fully render
  await page.waitForTimeout(2000);

  return chart;
}

/**
 * Test filter functionality
 */
export async function testFilters(page: Page) {
  // Look for common filter elements
  const filterElements = [
    'select[name*="brand"]',
    'input[type="date"]',
    'select[name*="currency"]',
    'select[name*="country"]',
    '[data-testid*="filter"]'
  ];

  const activeFilters = [];

  for (const selector of filterElements) {
    const element = page.locator(selector);
    if (await element.isVisible()) {
      activeFilters.push(selector);
    }
  }

  return activeFilters;
}

/**
 * Test pagination if present
 */
export async function testPagination(page: Page) {
  const pagination = page.locator('[data-testid="pagination"], .pagination, nav[aria-label*="pagination"]');

  if (await pagination.isVisible()) {
    const nextButton = pagination.locator('button:has-text("Next"), button[aria-label*="next"]');
    const prevButton = pagination.locator('button:has-text("Previous"), button[aria-label*="previous"]');

    return {
      hasPagination: true,
      hasNext: await nextButton.isVisible(),
      hasPrevious: await prevButton.isVisible()
    };
  }

  return { hasPagination: false };
}

/**
 * Test search functionality
 */
export async function testSearch(page: Page, searchTerm: string) {
  const searchInput = page.locator('input[type="search"], input[placeholder*="search"], input[name*="search"]');

  if (await searchInput.isVisible()) {
    await searchInput.fill(searchTerm);
    await page.keyboard.press('Enter');
    await page.waitForTimeout(1000);
    return true;
  }

  return false;
}

/**
 * Test modal/dialog functionality
 */
export async function testModal(page: Page, triggerSelector: string) {
  await page.click(triggerSelector);

  const modal = page.locator('[role="dialog"], .modal, [data-testid*="modal"]');
  await expect(modal).toBeVisible();

  // Test close functionality
  const closeButton = modal.locator('button[aria-label*="close"], button:has-text("Close"), [data-testid*="close"]');
  if (await closeButton.isVisible()) {
    await closeButton.click();
    await expect(modal).not.toBeVisible();
  }
}

/**
 * Test loading states
 */
export async function waitForLoadingToComplete(page: Page) {
  // Wait for common loading indicators to disappear
  const loadingSelectors = [
    '.loading',
    '.spinner',
    '[data-testid*="loading"]',
    '.animate-spin',
    'text="Loading"'
  ];

  for (const selector of loadingSelectors) {
    try {
      await page.waitForSelector(selector, { state: 'hidden', timeout: 5000 });
    } catch {
      // Ignore if selector doesn't exist
    }
  }
}

/**
 * Test accessibility features
 */
export async function testAccessibility(page: Page) {
  // Check for basic accessibility attributes
  const elementsWithAriaLabels = await page.locator('[aria-label]').count();
  const elementsWithRoles = await page.locator('[role]').count();
  const headings = await page.locator('h1, h2, h3, h4, h5, h6').count();
  const landmarks = await page.locator('[role="main"], [role="navigation"], [role="banner"], [role="contentinfo"]').count();

  return {
    ariaLabels: elementsWithAriaLabels,
    roles: elementsWithRoles,
    headings,
    landmarks
  };
}
