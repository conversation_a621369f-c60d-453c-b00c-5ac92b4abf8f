import { test, expect } from '@playwright/test';
import {
  waitForPageLoad,
  signInAsAdmin,
  takeScreenshot,
  testChartRendering,
  testFilters,
  testDataTable,
  waitForLoadingToComplete,
  testAccessibility,
  verifyPageElements
} from './utils/test-helpers';

test.describe('Comprehensive Dashboard Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Sign in as admin for all tests
    await signInAsAdmin(page);
  });

  test('should load dashboard with all core components', async ({ page }) => {
    await page.goto('/dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Verify page title and main elements
    await expect(page).toHaveTitle(/Dashboard|NOLK/);
    
    // Check for main dashboard components
    const expectedElements = [
      'main, [role="main"]',
      'nav, [role="navigation"]',
      'h1, h2, [data-testid*="title"]'
    ];
    
    await verifyPageElements(page, expectedElements);
    await takeScreenshot(page, 'dashboard-main-components');
  });

  test('should display and interact with KPI cards', async ({ page }) => {
    await page.goto('/dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for KPI cards
    const kpiCards = page.locator('[data-testid*="kpi"], .kpi-card, [class*="kpi"]');
    const cardCount = await kpiCards.count();

    if (cardCount > 0) {
      // Verify first KPI card is visible
      await expect(kpiCards.first()).toBeVisible();
      
      // Check for KPI values and labels
      const kpiValues = page.locator('[data-testid*="kpi-value"], .kpi-value');
      const kpiLabels = page.locator('[data-testid*="kpi-label"], .kpi-label');
      
      expect(await kpiValues.count()).toBeGreaterThan(0);
      expect(await kpiLabels.count()).toBeGreaterThan(0);
      
      console.log(`Found ${cardCount} KPI cards`);
    }

    await takeScreenshot(page, 'dashboard-kpi-cards');
  });

  test('should render charts correctly', async ({ page }) => {
    await page.goto('/dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Test chart rendering
    const charts = await testChartRendering(page);
    await takeScreenshot(page, 'dashboard-charts');
  });

  test('should have functional filters', async ({ page }) => {
    await page.goto('/dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Test filter functionality
    const activeFilters = await testFilters(page);
    console.log(`Found ${activeFilters.length} active filters:`, activeFilters);

    // Test date filter if present
    const dateFilter = page.locator('input[type="date"]');
    if (await dateFilter.isVisible()) {
      await dateFilter.fill('2024-01-01');
      await page.waitForTimeout(2000);
      await takeScreenshot(page, 'dashboard-date-filter-applied');
    }

    // Test brand filter if present
    const brandFilter = page.locator('select[name*="brand"], [data-testid*="brand-filter"]');
    if (await brandFilter.isVisible()) {
      await brandFilter.click();
      await page.waitForTimeout(1000);
      
      const options = page.locator('option, [role="option"]');
      const optionCount = await options.count();
      
      if (optionCount > 1) {
        await options.nth(1).click();
        await page.waitForTimeout(2000);
        await takeScreenshot(page, 'dashboard-brand-filter-applied');
      }
    }
  });

  test('should handle responsive design', async ({ page }) => {
    await page.goto('/dashboard');
    await waitForPageLoad(page);

    const viewports = [
      { width: 1920, height: 1080, name: 'desktop' },
      { width: 1024, height: 768, name: 'tablet' },
      { width: 375, height: 667, name: 'mobile' }
    ];

    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(1000);
      
      // Verify main elements are still visible
      await expect(page.locator('main, [role="main"]')).toBeVisible();
      await takeScreenshot(page, `dashboard-responsive-${viewport.name}`);
    }
  });

  test('should have proper accessibility features', async ({ page }) => {
    await page.goto('/dashboard');
    await waitForPageLoad(page);

    const accessibilityInfo = await testAccessibility(page);
    
    // Verify minimum accessibility requirements
    expect(accessibilityInfo.headings).toBeGreaterThan(0);
    expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
    
    console.log('Accessibility info:', accessibilityInfo);
    await takeScreenshot(page, 'dashboard-accessibility');
  });

  test('should handle navigation between dashboard sections', async ({ page }) => {
    await page.goto('/dashboard');
    await waitForPageLoad(page);

    // Test navigation to other main sections
    const navLinks = [
      { text: 'Brand Deep Dive', url: '/brand-deep-dive' },
      { text: 'Marketing Dashboard', url: '/marketing-dashboard' },
      { text: 'Executive Summary', url: '/executive-summary' },
      { text: 'Budget', url: '/budget' },
      { text: 'AI Assistant', url: '/ai-assistant' }
    ];

    for (const link of navLinks) {
      const navElement = page.locator(`nav a:has-text("${link.text}")`);
      
      if (await navElement.isVisible()) {
        await navElement.click();
        await page.waitForURL(`**${link.url}`);
        await waitForPageLoad(page);
        
        expect(page.url()).toContain(link.url);
        await takeScreenshot(page, `navigation-to-${link.text.toLowerCase().replace(' ', '-')}`);
        
        // Navigate back to dashboard
        await page.goto('/dashboard');
        await waitForPageLoad(page);
      }
    }
  });

  test('should handle data refresh and updates', async ({ page }) => {
    await page.goto('/dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Test page refresh
    await page.reload();
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Verify dashboard still works after refresh
    await expect(page.locator('main, [role="main"]')).toBeVisible();
    await takeScreenshot(page, 'dashboard-after-refresh');
  });

  test('should not have critical console errors', async ({ page }) => {
    const consoleErrors: string[] = [];
    
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    await page.goto('/dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Filter out acceptable errors
    const criticalErrors = consoleErrors.filter(error =>
      !error.includes('favicon') &&
      !error.includes('404') &&
      !error.includes('net::ERR_FAILED') &&
      !error.includes('ChunkLoadError') &&
      !error.includes('ResizeObserver')
    );

    if (criticalErrors.length > 0) {
      console.log('Critical console errors found:', criticalErrors);
    }

    // For now, log errors but don't fail the test
    // expect(criticalErrors).toHaveLength(0);
  });

  test('should load within performance thresholds', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);
    
    const loadTime = Date.now() - startTime;
    
    // Dashboard should load within 10 seconds
    expect(loadTime).toBeLessThan(10000);
    
    console.log(`Dashboard loaded in ${loadTime}ms`);
  });
});
