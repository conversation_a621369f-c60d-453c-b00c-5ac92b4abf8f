# Comprehensive Playwright Test Suite for NOLK v4

This directory contains a comprehensive end-to-end test suite that covers the entire NOLK v4 application using <PERSON><PERSON> with the farbour/admin user credentials.

## 🎯 Test Coverage Overview

The test suite provides comprehensive coverage across all major application areas:

### 📊 Main Application Pages
- **Dashboard** (`comprehensive-dashboard.spec.ts`)
- **Brand Deep Dive** (`comprehensive-brand-deep-dive.spec.ts`) 
- **Marketing Dashboard** (`comprehensive-marketing-dashboard.spec.ts`)
- **Executive Summary** (`comprehensive-executive-summary.spec.ts`)
- **Budget & AI Assistant** (`comprehensive-budget-ai.spec.ts`)

### 🔐 Admin Dashboard
- **Admin Dashboard** (`comprehensive-admin-dashboard.spec.ts`)
- All admin sections: Users, Roles, Permissions, Groups, Brands, Settings, Backups, DB Structure

### 🔌 API Testing
- **API Endpoints** (`comprehensive-api.spec.ts`)
- Dashboard APIs, Marketing APIs, Admin APIs, Budget API, AI Assistant API
- Authentication, error handling, performance testing

### ⚡ Performance & Quality
- **Performance Testing** (`comprehensive-performance.spec.ts`)
- **Error Handling** (`comprehensive-error-handling.spec.ts`)
- **Comprehensive Runner** (`comprehensive-test-runner.spec.ts`)

## 🚀 Running Tests

### Quick Start
```bash
# Run all comprehensive tests
npm run test:e2e:comprehensive

# Run smoke test (fastest overview)
npm run test:e2e:smoke

# Run specific test suites
npm run test:e2e:dashboard
npm run test:e2e:marketing
npm run test:e2e:admin
npm run test:e2e:api
npm run test:e2e:performance
npm run test:e2e:errors
```

### Standard Playwright Commands
```bash
# Run all E2E tests
npm run test:e2e

# Run with UI mode
npm run test:e2e:ui

# Run in headed mode (see browser)
npm run test:e2e:headed
```

## 🔑 Authentication

All tests use the **farbour/admin** credentials as specified in the user preferences. The test helper `signInAsAdmin()` automatically handles authentication for all test scenarios.

## 📋 Test Categories

### 1. Functional Testing
- ✅ Page loading and navigation
- ✅ User interface components
- ✅ Form submissions and validations
- ✅ Data filtering and search
- ✅ Chart and table rendering
- ✅ Modal and dialog interactions

### 2. Integration Testing
- ✅ API endpoint testing
- ✅ Authentication flows
- ✅ Data flow between components
- ✅ Cross-page navigation
- ✅ Filter state management

### 3. Performance Testing
- ✅ Page load times
- ✅ First Contentful Paint (FCP)
- ✅ Largest Contentful Paint (LCP)
- ✅ JavaScript bundle sizes
- ✅ Memory usage monitoring
- ✅ Network performance

### 4. Accessibility Testing
- ✅ ARIA labels and roles
- ✅ Keyboard navigation
- ✅ Screen reader support
- ✅ Semantic HTML structure
- ✅ Color contrast (basic)

### 5. Error Handling
- ✅ 404 error pages
- ✅ Network failures
- ✅ API errors
- ✅ Form validation errors
- ✅ Session timeout handling
- ✅ Browser navigation edge cases

### 6. Responsive Design
- ✅ Desktop (1920x1080)
- ✅ Tablet (1024x768)
- ✅ Mobile (375x667)
- ✅ Layout adaptation
- ✅ Mobile menu functionality

## 📊 Test Results

Test results are automatically generated in multiple formats:

- **HTML Report**: `tests/reports/e2e-html/index.html`
- **JSON Results**: `tests/reports/e2e-results.json`
- **JUnit XML**: `tests/reports/e2e-junit.xml`
- **Screenshots**: `tests/reports/e2e-artifacts/screenshots/`
- **Videos**: `tests/reports/e2e-artifacts/videos/` (on failure)

## 🛠️ Test Configuration

The test suite is configured in `playwright.config.ts` with:

- **Browser**: Chromium only (as per user preference)
- **Timeout**: 60 seconds for comprehensive tests
- **Retries**: 2 retries on CI, 0 locally
- **Parallel**: Full parallelization enabled
- **Screenshots**: On failure
- **Videos**: On failure
- **Traces**: On first retry

## 📝 Test Structure

Each test file follows a consistent structure:

```typescript
test.describe('Test Suite Name', () => {
  test.beforeEach(async ({ page }) => {
    await signInAsAdmin(page); // Authenticate with farbour/admin
  });

  test('should test specific functionality', async ({ page }) => {
    // Test implementation
    await takeScreenshot(page, 'test-name');
  });
});
```

## 🔧 Helper Functions

The test suite includes comprehensive helper functions in `utils/test-helpers.ts`:

- `signInAsAdmin()` - Authenticate with farbour/admin credentials
- `waitForPageLoad()` - Wait for complete page loading
- `testChartRendering()` - Test chart components
- `testDataTable()` - Test table functionality
- `testFilters()` - Test filter components
- `testAccessibility()` - Check accessibility features
- `takeScreenshot()` - Capture screenshots
- And many more...

## 🎯 Performance Benchmarks

The test suite enforces performance benchmarks:

- **Dashboard**: < 8 seconds
- **Marketing Dashboard**: < 15 seconds
- **Brand Deep Dive**: < 15 seconds
- **Executive Summary**: < 12 seconds
- **Budget**: < 8 seconds
- **AI Assistant**: < 8 seconds
- **Admin Dashboard**: < 10 seconds

## 🐛 Debugging Tests

### View Test Results
```bash
# Open HTML report
npx playwright show-report tests/reports/e2e-html

# Run specific test with debug
npx playwright test tests/e2e/comprehensive-dashboard.spec.ts --debug
```

### Common Issues
1. **Authentication failures**: Ensure farbour/admin user exists
2. **Timeout errors**: Increase timeout in playwright.config.ts
3. **Element not found**: Check selectors in test-helpers.ts
4. **Performance failures**: Check network conditions and server load

## 📈 Continuous Integration

The test suite is designed for CI/CD environments:

- Automatic retries on failure
- Comprehensive reporting
- Performance monitoring
- Error screenshots and videos
- Parallel execution for speed

## 🔄 Maintenance

To maintain the test suite:

1. **Update selectors** when UI changes
2. **Adjust timeouts** for performance changes
3. **Add new tests** for new features
4. **Update credentials** if authentication changes
5. **Review performance benchmarks** regularly

## 📞 Support

For issues with the test suite:

1. Check the HTML test report for detailed failure information
2. Review screenshots and videos in the artifacts folder
3. Verify the application is running on `http://localhost:6699`
4. Ensure the farbour/admin user has proper permissions
5. Check console logs for JavaScript errors

---

This comprehensive test suite ensures the NOLK v4 application maintains high quality, performance, and reliability across all user journeys and edge cases.
