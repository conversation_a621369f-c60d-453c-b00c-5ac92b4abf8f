import { test, expect } from '@playwright/test';
import {
  waitForPageLoad,
  signInAsAdmin,
  takeScreenshot,
  testChartRendering,
  testFilters,
  testDataTable,
  testFormSubmission,
  waitForLoadingToComplete,
  testAccessibility,
  verifyPageElements,
  testApiEndpoint
} from './utils/test-helpers';

test.describe('Comprehensive Budget Page Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Sign in as admin for all tests
    await signInAsAdmin(page);
  });

  test('should load budget page with all components', async ({ page }) => {
    await page.goto('/budget');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Verify page title and main elements
    await expect(page).toHaveTitle(/Budget|NOLK/);
    
    // Check for main components
    const expectedElements = [
      'main, [role="main"]',
      'nav, [role="navigation"]',
      'h1, h2, [data-testid*="title"]'
    ];
    
    await verifyPageElements(page, expectedElements);
    await takeScreenshot(page, 'budget-page-main');
  });

  test('should display budget data and charts', async ({ page }) => {
    await page.goto('/budget');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Test budget data table
    const tableInfo = await testDataTable(page);
    if (tableInfo.headerCount > 0) {
      console.log(`Budget table has ${tableInfo.headerCount} headers and ${tableInfo.rowCount} rows`);
    }

    // Test budget charts
    const charts = await testChartRendering(page);
    
    // Look for budget-specific elements
    const budgetElements = page.locator('text=/budget|allocated|spent|remaining|variance/i');
    const budgetCount = await budgetElements.count();
    console.log(`Found ${budgetCount} budget-related elements`);

    await takeScreenshot(page, 'budget-data-charts');
  });

  test('should handle budget filters and brand selection', async ({ page }) => {
    await page.goto('/budget');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Test filter functionality
    const activeFilters = await testFilters(page);
    console.log(`Found ${activeFilters.length} active filters`);

    // Test brand filter for budget
    const brandFilter = page.locator('select[name*="brand"], [data-testid*="brand-filter"]');
    if (await brandFilter.isVisible()) {
      await brandFilter.click();
      await page.waitForTimeout(1000);
      
      const options = page.locator('option, [role="option"]');
      const optionCount = await options.count();
      
      if (optionCount > 1) {
        await options.nth(1).click();
        await page.waitForTimeout(2000);
        await waitForLoadingToComplete(page);
        await takeScreenshot(page, 'budget-brand-filter-applied');
      }
    }
  });

  test('should test budget API endpoint', async ({ page }) => {
    await page.goto('/budget');
    await waitForPageLoad(page);

    // Test budget API
    try {
      const response = await testApiEndpoint(page, '/api/budget');
      console.log(`Budget API response: ${response.status()}`);
    } catch (error) {
      console.log(`Budget API failed: ${error}`);
    }
  });

  test('should be responsive on different screen sizes', async ({ page }) => {
    await page.goto('/budget');
    await waitForPageLoad(page);

    const viewports = [
      { width: 1920, height: 1080, name: 'desktop' },
      { width: 1024, height: 768, name: 'tablet' },
      { width: 375, height: 667, name: 'mobile' }
    ];

    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(1000);
      
      await expect(page.locator('main, [role="main"]')).toBeVisible();
      await takeScreenshot(page, `budget-responsive-${viewport.name}`);
    }
  });
});

test.describe('Comprehensive AI Assistant Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Sign in as admin for all tests
    await signInAsAdmin(page);
  });

  test('should load AI assistant page with all components', async ({ page }) => {
    await page.goto('/ai-assistant');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Verify page title and main elements
    await expect(page).toHaveTitle(/AI Assistant|NOLK/);
    
    // Check for main components
    const expectedElements = [
      'main, [role="main"]',
      'nav, [role="navigation"]',
      'h1, h2, [data-testid*="title"]'
    ];
    
    await verifyPageElements(page, expectedElements);
    await takeScreenshot(page, 'ai-assistant-main');
  });

  test('should display chat interface', async ({ page }) => {
    await page.goto('/ai-assistant');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for chat interface elements
    const chatElements = [
      'textarea, input[type="text"]', // Message input
      'button:has-text("Send")', // Send button
      '.chat, .messages, [data-testid*="chat"]', // Chat container
      '.message, [data-testid*="message"]' // Message elements
    ];

    let foundChatElements = 0;
    for (const selector of chatElements) {
      const element = page.locator(selector);
      if (await element.isVisible()) {
        foundChatElements++;
      }
    }

    console.log(`Found ${foundChatElements} chat interface elements`);
    await takeScreenshot(page, 'ai-assistant-chat-interface');
  });

  test('should handle message input and sending', async ({ page }) => {
    await page.goto('/ai-assistant');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for message input
    const messageInput = page.locator('textarea, input[type="text"], [data-testid*="message-input"]');
    
    if (await messageInput.isVisible()) {
      // Type a test message
      await messageInput.fill('What is the total revenue for this month?');
      await page.waitForTimeout(500);
      
      // Look for send button
      const sendButton = page.locator('button:has-text("Send"), [data-testid*="send"]');
      
      if (await sendButton.isVisible()) {
        await sendButton.click();
        await page.waitForTimeout(2000);
        
        // Look for response or loading indicator
        const loadingIndicator = page.locator('.loading, .thinking, text=/thinking|processing/i');
        if (await loadingIndicator.isVisible()) {
          console.log('AI is processing the message');
          await page.waitForTimeout(5000); // Wait for response
        }
        
        await takeScreenshot(page, 'ai-assistant-message-sent');
      }
    }
  });

  test('should display conversation history', async ({ page }) => {
    await page.goto('/ai-assistant');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for conversation history
    const messages = page.locator('.message, [data-testid*="message"], .chat-message');
    const messageCount = await messages.count();
    
    console.log(`Found ${messageCount} messages in conversation history`);
    
    if (messageCount > 0) {
      // Check for user and AI messages
      const userMessages = page.locator('.user-message, [data-testid*="user-message"]');
      const aiMessages = page.locator('.ai-message, [data-testid*="ai-message"], .assistant-message');
      
      const userCount = await userMessages.count();
      const aiCount = await aiMessages.count();
      
      console.log(`Found ${userCount} user messages and ${aiCount} AI messages`);
    }

    await takeScreenshot(page, 'ai-assistant-conversation-history');
  });

  test('should handle AI assistant features', async ({ page }) => {
    await page.goto('/ai-assistant');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for AI assistant specific features
    const aiFeatures = [
      'button:has-text("Clear"), [data-testid*="clear"]', // Clear chat
      'button:has-text("Export"), [data-testid*="export"]', // Export conversation
      '.suggestions, [data-testid*="suggestion"]', // Suggested questions
      '.templates, [data-testid*="template"]' // Message templates
    ];

    let foundFeatures = 0;
    for (const selector of aiFeatures) {
      const element = page.locator(selector);
      if (await element.isVisible()) {
        foundFeatures++;
      }
    }

    console.log(`Found ${foundFeatures} AI assistant features`);
    
    // Test clear chat if available
    const clearButton = page.locator('button:has-text("Clear"), [data-testid*="clear"]');
    if (await clearButton.isVisible()) {
      await clearButton.click();
      await page.waitForTimeout(1000);
      await takeScreenshot(page, 'ai-assistant-cleared');
    }
  });

  test('should test AI assistant API endpoint', async ({ page }) => {
    await page.goto('/ai-assistant');
    await waitForPageLoad(page);

    // Test AI assistant API
    try {
      const response = await testApiEndpoint(page, '/api/ai-assistant');
      console.log(`AI Assistant API response: ${response.status()}`);
    } catch (error) {
      console.log(`AI Assistant API failed: ${error}`);
    }
  });

  test('should handle suggested questions or templates', async ({ page }) => {
    await page.goto('/ai-assistant');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for suggested questions
    const suggestions = page.locator('.suggestion, [data-testid*="suggestion"], button:has-text("?")');
    const suggestionCount = await suggestions.count();
    
    if (suggestionCount > 0) {
      console.log(`Found ${suggestionCount} suggested questions`);
      
      // Click on first suggestion
      await suggestions.first().click();
      await page.waitForTimeout(1000);
      
      // Check if message input was populated
      const messageInput = page.locator('textarea, input[type="text"]');
      if (await messageInput.isVisible()) {
        const inputValue = await messageInput.inputValue();
        if (inputValue.length > 0) {
          console.log(`Suggestion populated input: ${inputValue}`);
        }
      }
      
      await takeScreenshot(page, 'ai-assistant-suggestion-used');
    }
  });

  test('should be responsive on different screen sizes', async ({ page }) => {
    await page.goto('/ai-assistant');
    await waitForPageLoad(page);

    const viewports = [
      { width: 1920, height: 1080, name: 'desktop' },
      { width: 1024, height: 768, name: 'tablet' },
      { width: 375, height: 667, name: 'mobile' }
    ];

    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(1000);
      
      await expect(page.locator('main, [role="main"]')).toBeVisible();
      
      // Check chat interface on different screens
      const chatContainer = page.locator('.chat, [data-testid*="chat"]');
      if (await chatContainer.isVisible()) {
        console.log(`Chat interface visible on ${viewport.name}`);
      }
      
      await takeScreenshot(page, `ai-assistant-responsive-${viewport.name}`);
    }
  });

  test('should have proper accessibility features', async ({ page }) => {
    await page.goto('/ai-assistant');
    await waitForPageLoad(page);

    const accessibilityInfo = await testAccessibility(page);
    
    // Verify minimum accessibility requirements
    expect(accessibilityInfo.headings).toBeGreaterThan(0);
    expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
    
    console.log('AI Assistant accessibility info:', accessibilityInfo);
    await takeScreenshot(page, 'ai-assistant-accessibility');
  });

  test('should load within performance thresholds', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/ai-assistant');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);
    
    const loadTime = Date.now() - startTime;
    
    // AI Assistant should load within 8 seconds
    expect(loadTime).toBeLessThan(8000);
    
    console.log(`AI Assistant loaded in ${loadTime}ms`);
  });
});
