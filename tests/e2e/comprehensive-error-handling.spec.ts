import { test, expect } from '@playwright/test';
import {
  waitForPageLoad,
  signInAsAdmin,
  takeScreenshot,
  waitForLoadingToComplete
} from './utils/test-helpers';

test.describe('Comprehensive Error Handling and Edge Cases', () => {
  test.beforeEach(async ({ page }) => {
    // Sign in as admin for most tests
    await signInAsAdmin(page);
  });

  test('should handle 404 errors gracefully', async ({ page }) => {
    const invalidUrls = [
      '/nonexistent-page',
      '/dashboard/invalid',
      '/admin/nonexistent',
      '/marketing-dashboard/invalid-campaign-id',
      '/brand-deep-dive/invalid'
    ];

    for (const url of invalidUrls) {
      await page.goto(url);
      await waitForPageLoad(page);

      // Should either show 404 page or redirect to valid page
      const currentUrl = page.url();
      console.log(`${url} -> ${currentUrl}`);

      // Check for 404 page elements or redirect
      const is404 = currentUrl.includes('404') ||
                   await page.locator('text=/404|not found|page not found/i').isVisible();
      const isRedirect = !currentUrl.includes(url.split('/')[1]);
      const isValidPage = currentUrl.includes('/dashboard') || currentUrl.includes('/auth');

      // Either shows 404, redirects, or shows a valid page (some routes might be valid)
      expect(is404 || isRedirect || isValidPage).toBe(true);
      await takeScreenshot(page, `error-404-${url.replace(/\//g, '-')}`);
    }
  });

  test('should handle network errors and offline scenarios', async ({ page }) => {
    await page.goto('/dashboard');
    await waitForPageLoad(page);

    // Simulate network failure
    await page.context().setOffline(true);

    // Try to navigate to another page
    try {
      await page.goto('/marketing-dashboard');
      await page.waitForTimeout(3000);
    } catch (error) {
      console.log('Expected network error:', error.message);
    }

    // Should show offline message or handle gracefully
    const offlineIndicator = page.locator('text=/offline|network error|connection failed/i');
    const isOfflineHandled = await offlineIndicator.isVisible();

    console.log(`Offline handling: ${isOfflineHandled ? 'Detected' : 'Not detected'}`);
    await takeScreenshot(page, 'network-offline');

    // Restore network
    await page.context().setOffline(false);
    await page.waitForTimeout(2000);

    // Should recover when network is restored
    await page.reload();
    await waitForPageLoad(page);
    await takeScreenshot(page, 'network-restored');
  });

  test('should handle API errors gracefully', async ({ page }) => {
    // Intercept API calls and return errors
    await page.route('**/api/dashboard/**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' })
      });
    });

    await page.goto('/dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Should show error message or fallback content
    const errorMessage = page.locator('text=/error|failed|something went wrong/i');
    const hasErrorHandling = await errorMessage.isVisible();

    console.log(`API error handling: ${hasErrorHandling ? 'Detected' : 'Not detected'}`);
    await takeScreenshot(page, 'api-error-500');
  });

  test('should handle authentication errors', async ({ page }) => {
    // Test without authentication
    const unauthenticatedPage = await page.context().newPage();

    const protectedPages = [
      '/dashboard',
      '/marketing-dashboard',
      '/brand-deep-dive',
      '/executive-summary',
      '/budget',
      '/ai-assistant',
      '/admin'
    ];

    for (const url of protectedPages) {
      await unauthenticatedPage.goto(url);
      await waitForPageLoad(unauthenticatedPage);

      // Should redirect to sign-in page or stay on dashboard if already authenticated
      const currentUrl = unauthenticatedPage.url();
      const isOnSignIn = currentUrl.includes('/auth/signin');
      const isOnDashboard = currentUrl.includes('/dashboard');

      // Either redirected to sign-in or stayed on dashboard (if session is still valid)
      expect(isOnSignIn || isOnDashboard).toBe(true);

      console.log(`${url} -> redirected to sign-in`);
    }

    await takeScreenshot(unauthenticatedPage, 'auth-redirect');
    await unauthenticatedPage.close();
  });

  test('should handle form validation errors', async ({ page }) => {
    await page.goto('/admin/users');
    await waitForPageLoad(page);

    // Look for add user button
    const addButton = page.locator('button:has-text("Add"), button:has-text("Create")');

    if (await addButton.isVisible()) {
      await addButton.click();
      await page.waitForTimeout(1000);

      // Look for form
      const form = page.locator('form');

      if (await form.isVisible()) {
        // Try to submit empty form
        const submitButton = form.locator('button[type="submit"], button:has-text("Save"), button:has-text("Create")');

        if (await submitButton.isVisible()) {
          await submitButton.click();
          await page.waitForTimeout(1000);

          // Should show validation errors
          const validationErrors = page.locator('.error, .invalid, text=/required|invalid/i');
          const hasValidation = await validationErrors.count() > 0;

          console.log(`Form validation: ${hasValidation ? 'Working' : 'Not detected'}`);
          await takeScreenshot(page, 'form-validation-errors');
        }
      }
    }
  });

  test('should handle large data sets without crashing', async ({ page }) => {
    // Test with potentially large datasets
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);

    // Set a very large date range to potentially load lots of data
    const startDateInput = page.locator('input[type="date"]').first();
    const endDateInput = page.locator('input[type="date"]').last();

    if (await startDateInput.isVisible() && await endDateInput.isVisible()) {
      await startDateInput.fill('2020-01-01');
      await endDateInput.fill('2024-12-31');
      await page.waitForTimeout(5000);
      await waitForLoadingToComplete(page);

      // Page should still be responsive
      const mainContent = await getMainContent(page);
      const isResponsive = await mainContent.isVisible();
      expect(isResponsive).toBe(true);

      console.log('Large dataset handling: Page remained responsive');
      await takeScreenshot(page, 'large-dataset-handling');
    }
  });

  test('should handle browser back/forward navigation', async ({ page }) => {
    // Navigate through several pages
    await page.goto('/dashboard');
    await waitForPageLoad(page);

    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);

    await page.goto('/brand-deep-dive');
    await waitForPageLoad(page);

    // Test back navigation
    await page.goBack();
    await waitForPageLoad(page);
    expect(page.url()).toContain('/marketing-dashboard');

    await page.goBack();
    await waitForPageLoad(page);
    expect(page.url()).toContain('/dashboard');

    // Test forward navigation
    await page.goForward();
    await waitForPageLoad(page);
    expect(page.url()).toContain('/marketing-dashboard');

    console.log('Browser navigation: Working correctly');
    await takeScreenshot(page, 'browser-navigation');
  });

  test('should handle page refresh and state preservation', async ({ page }) => {
    await page.goto('/dashboard');
    await waitForPageLoad(page);

    // Apply some filters if available
    const brandFilter = page.locator('select[name*="brand"]');
    if (await brandFilter.isVisible()) {
      await brandFilter.selectOption({ index: 1 });
      await page.waitForTimeout(2000);
    }

    // Refresh the page
    await page.reload();
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Page should load successfully after refresh
    const mainContent = await getMainContent(page);
    const isLoaded = await mainContent.isVisible();
    expect(isLoaded).toBe(true);

    console.log('Page refresh: Handled successfully');
    await takeScreenshot(page, 'page-refresh');
  });

  test('should handle concurrent user actions', async ({ page }) => {
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Simulate rapid user interactions
    const actions = [
      () => page.locator('select[name*="currency"]').selectOption({ index: 1 }),
      () => page.locator('input[type="date"]').first().fill('2024-01-01'),
      () => page.locator('button').first().click(),
      () => page.keyboard.press('Tab'),
      () => page.mouse.move(100, 100)
    ];

    // Execute actions rapidly
    const promises = actions.map(action => action().catch(() => {}));
    await Promise.all(promises);

    await page.waitForTimeout(2000);
    await waitForLoadingToComplete(page);

    // Page should still be functional
    const mainContent = await getMainContent(page);
    const isStillFunctional = await mainContent.isVisible();
    expect(isStillFunctional).toBe(true);

    console.log('Concurrent actions: Handled successfully');
    await takeScreenshot(page, 'concurrent-actions');
  });

  test('should handle memory leaks during navigation', async ({ page }) => {
    const pages = ['/dashboard', '/marketing-dashboard', '/brand-deep-dive', '/executive-summary'];
    let initialMemory = 0;

    // Get initial memory usage
    const memory = await page.evaluate(() => {
      if ('memory' in performance) {
        return (performance as any).memory.usedJSHeapSize;
      }
      return 0;
    });

    initialMemory = memory;
    console.log(`Initial memory: ${(initialMemory / 1024 / 1024).toFixed(2)}MB`);

    // Navigate through pages multiple times
    for (let i = 0; i < 3; i++) {
      for (const url of pages) {
        await page.goto(url);
        await waitForPageLoad(page);
        await waitForLoadingToComplete(page);
        await page.waitForTimeout(1000);
      }
    }

    // Check final memory usage
    const finalMemory = await page.evaluate(() => {
      if ('memory' in performance) {
        return (performance as any).memory.usedJSHeapSize;
      }
      return 0;
    });

    const memoryIncrease = finalMemory - initialMemory;
    console.log(`Final memory: ${(finalMemory / 1024 / 1024).toFixed(2)}MB`);
    console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);

    // Memory increase should be reasonable (less than 50MB)
    expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
  });

  test('should handle edge cases in data filtering', async ({ page }) => {
    await page.goto('/dashboard');
    await waitForPageLoad(page);

    // Test edge cases in date filtering
    const dateInputs = page.locator('input[type="date"]');
    const dateCount = await dateInputs.count();

    if (dateCount >= 2) {
      // Test invalid date range (end before start)
      await dateInputs.first().fill('2024-12-31');
      await dateInputs.last().fill('2024-01-01');
      await page.waitForTimeout(2000);

      // Should handle invalid date range gracefully
      const errorMessage = page.locator('text=/invalid|error/i');
      const hasErrorHandling = await errorMessage.isVisible();

      console.log(`Invalid date range handling: ${hasErrorHandling ? 'Detected' : 'Handled silently'}`);
      await takeScreenshot(page, 'invalid-date-range');

      // Reset to valid range
      await dateInputs.first().fill('2024-01-01');
      await dateInputs.last().fill('2024-12-31');
      await page.waitForTimeout(2000);
    }
  });

  test('should handle session timeout gracefully', async ({ page }) => {
    await page.goto('/dashboard');
    await waitForPageLoad(page);

    // Simulate session timeout by clearing cookies
    await page.context().clearCookies();

    // Try to navigate to another page
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);

    // Should redirect to sign-in page
    const currentUrl = page.url();
    expect(currentUrl).toContain('/auth/signin');

    console.log('Session timeout: Redirected to sign-in');
    await takeScreenshot(page, 'session-timeout');
  });
});
