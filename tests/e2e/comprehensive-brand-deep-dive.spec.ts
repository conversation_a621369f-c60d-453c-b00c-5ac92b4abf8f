import { test, expect } from '@playwright/test';
import {
  waitForPageLoad,
  signInAsAdmin,
  takeScreenshot,
  testChartRendering,
  testFilters,
  waitForLoadingToComplete,
  testAccessibility,
  verifyPageElements,
  testModal
} from './utils/test-helpers';

test.describe('Comprehensive Brand Deep Dive Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Sign in as admin for all tests
    await signInAsAdmin(page);
  });

  test('should load brand deep dive page with all components', async ({ page }) => {
    await page.goto('/brand-deep-dive');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Verify page title and main elements
    await expect(page).toHaveTitle(/Brand Deep Dive|NOLK/);
    
    // Check for main components
    const expectedElements = [
      'main, [role="main"]',
      'nav, [role="navigation"]',
      'h1, h2, [data-testid*="title"]'
    ];
    
    await verifyPageElements(page, expectedElements);
    await takeScreenshot(page, 'brand-deep-dive-main');
  });

  test('should display brand selector and allow brand selection', async ({ page }) => {
    await page.goto('/brand-deep-dive');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for brand selector
    const brandSelector = page.locator('[data-testid*="brand-selector"], select[name*="brand"], .brand-selector');
    
    if (await brandSelector.isVisible()) {
      await brandSelector.click();
      await page.waitForTimeout(1000);
      
      // Check for brand options
      const brandOptions = page.locator('option, [role="option"], [data-testid*="brand-option"]');
      const optionCount = await brandOptions.count();
      
      if (optionCount > 1) {
        await brandOptions.nth(1).click();
        await page.waitForTimeout(2000);
        await waitForLoadingToComplete(page);
        
        console.log(`Selected brand from ${optionCount} available options`);
        await takeScreenshot(page, 'brand-deep-dive-brand-selected');
      }
    }
  });

  test('should display KPI cards specific to selected brand', async ({ page }) => {
    await page.goto('/brand-deep-dive');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for brand-specific KPI cards
    const kpiCards = page.locator('[data-testid*="kpi"], .kpi-card, [class*="kpi"]');
    const cardCount = await kpiCards.count();

    if (cardCount > 0) {
      // Verify KPI cards are visible
      await expect(kpiCards.first()).toBeVisible();
      
      // Check for brand-specific metrics
      const brandMetrics = page.locator('text=/revenue|margin|sales|units/i');
      expect(await brandMetrics.count()).toBeGreaterThan(0);
      
      console.log(`Found ${cardCount} brand KPI cards`);
    }

    await takeScreenshot(page, 'brand-deep-dive-kpi-cards');
  });

  test('should render brand performance charts', async ({ page }) => {
    await page.goto('/brand-deep-dive');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Test chart rendering for brand data
    const charts = await testChartRendering(page);
    
    // Look for specific chart types
    const chartTypes = [
      'svg', // Recharts
      'canvas', // Chart.js
      '.recharts-wrapper',
      '[data-testid*="chart"]'
    ];

    let chartCount = 0;
    for (const chartType of chartTypes) {
      const elements = page.locator(chartType);
      chartCount += await elements.count();
    }

    console.log(`Found ${chartCount} charts on brand deep dive page`);
    await takeScreenshot(page, 'brand-deep-dive-charts');
  });

  test('should display marketing campaigns for selected brand', async ({ page }) => {
    await page.goto('/brand-deep-dive');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for marketing campaigns section
    const campaignsSection = page.locator('[data-testid*="campaign"], .campaign, text=/campaign/i');
    
    if (await campaignsSection.first().isVisible()) {
      // Check for campaign data
      const campaignItems = page.locator('[data-testid*="campaign-item"], .campaign-item, tr');
      const campaignCount = await campaignItems.count();
      
      console.log(`Found ${campaignCount} campaign items`);
      
      if (campaignCount > 0) {
        // Test clicking on a campaign if clickable
        const firstCampaign = campaignItems.first();
        if (await firstCampaign.isVisible()) {
          await takeScreenshot(page, 'brand-deep-dive-campaigns');
        }
      }
    }
  });

  test('should have functional brand-specific filters', async ({ page }) => {
    await page.goto('/brand-deep-dive');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Test filter functionality
    const activeFilters = await testFilters(page);
    console.log(`Found ${activeFilters.length} active filters`);

    // Test date range filter
    const dateInputs = page.locator('input[type="date"]');
    const dateCount = await dateInputs.count();
    
    if (dateCount > 0) {
      await dateInputs.first().fill('2024-01-01');
      if (dateCount > 1) {
        await dateInputs.nth(1).fill('2024-12-31');
      }
      await page.waitForTimeout(2000);
      await waitForLoadingToComplete(page);
      await takeScreenshot(page, 'brand-deep-dive-date-filter');
    }

    // Test currency filter if present
    const currencyFilter = page.locator('select[name*="currency"], [data-testid*="currency"]');
    if (await currencyFilter.isVisible()) {
      await currencyFilter.click();
      await page.waitForTimeout(500);
      
      const currencyOptions = page.locator('option, [role="option"]');
      const optionCount = await currencyOptions.count();
      
      if (optionCount > 1) {
        await currencyOptions.nth(1).click();
        await page.waitForTimeout(2000);
        await waitForLoadingToComplete(page);
        await takeScreenshot(page, 'brand-deep-dive-currency-filter');
      }
    }
  });

  test('should display tabs and allow tab navigation', async ({ page }) => {
    await page.goto('/brand-deep-dive');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for tab navigation
    const tabs = page.locator('[role="tab"], .tab, [data-testid*="tab"]');
    const tabCount = await tabs.count();

    if (tabCount > 1) {
      console.log(`Found ${tabCount} tabs`);
      
      // Test clicking through tabs
      for (let i = 0; i < Math.min(tabCount, 3); i++) {
        const tab = tabs.nth(i);
        if (await tab.isVisible()) {
          await tab.click();
          await page.waitForTimeout(1000);
          await waitForLoadingToComplete(page);
          
          const tabText = await tab.textContent();
          await takeScreenshot(page, `brand-deep-dive-tab-${i}-${tabText?.toLowerCase().replace(/\s+/g, '-')}`);
        }
      }
    }
  });

  test('should handle brand comparison if available', async ({ page }) => {
    await page.goto('/brand-deep-dive');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for brand comparison features
    const compareButton = page.locator('button:has-text("Compare"), [data-testid*="compare"]');
    
    if (await compareButton.isVisible()) {
      await compareButton.click();
      await page.waitForTimeout(1000);
      
      // Look for comparison interface
      const comparisonInterface = page.locator('[data-testid*="comparison"], .comparison');
      if (await comparisonInterface.isVisible()) {
        await takeScreenshot(page, 'brand-deep-dive-comparison');
      }
    }
  });

  test('should handle export functionality if available', async ({ page }) => {
    await page.goto('/brand-deep-dive');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for export buttons
    const exportButtons = page.locator('button:has-text("Export"), button:has-text("Download"), [data-testid*="export"]');
    const exportCount = await exportButtons.count();

    if (exportCount > 0) {
      console.log(`Found ${exportCount} export options`);
      
      // Test export button visibility (don't actually download)
      await expect(exportButtons.first()).toBeVisible();
      await takeScreenshot(page, 'brand-deep-dive-export-options');
    }
  });

  test('should be responsive across different screen sizes', async ({ page }) => {
    await page.goto('/brand-deep-dive');
    await waitForPageLoad(page);

    const viewports = [
      { width: 1920, height: 1080, name: 'desktop' },
      { width: 1024, height: 768, name: 'tablet' },
      { width: 375, height: 667, name: 'mobile' }
    ];

    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(1000);
      
      // Verify main elements are still visible and properly arranged
      await expect(page.locator('main, [role="main"]')).toBeVisible();
      
      // Check if mobile menu appears on small screens
      if (viewport.width <= 768) {
        const mobileMenu = page.locator('[data-testid*="mobile-menu"], .mobile-menu, button[aria-label*="menu"]');
        if (await mobileMenu.isVisible()) {
          await mobileMenu.click();
          await page.waitForTimeout(500);
          await takeScreenshot(page, `brand-deep-dive-mobile-menu-${viewport.name}`);
        }
      }
      
      await takeScreenshot(page, `brand-deep-dive-responsive-${viewport.name}`);
    }
  });

  test('should have proper accessibility features', async ({ page }) => {
    await page.goto('/brand-deep-dive');
    await waitForPageLoad(page);

    const accessibilityInfo = await testAccessibility(page);
    
    // Verify minimum accessibility requirements
    expect(accessibilityInfo.headings).toBeGreaterThan(0);
    expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
    
    console.log('Brand Deep Dive accessibility info:', accessibilityInfo);
    await takeScreenshot(page, 'brand-deep-dive-accessibility');
  });

  test('should load within performance thresholds', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/brand-deep-dive');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);
    
    const loadTime = Date.now() - startTime;
    
    // Brand Deep Dive should load within 15 seconds (more complex page)
    expect(loadTime).toBeLessThan(15000);
    
    console.log(`Brand Deep Dive loaded in ${loadTime}ms`);
  });
});
