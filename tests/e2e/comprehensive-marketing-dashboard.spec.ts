import { test, expect } from '@playwright/test';
import {
  waitForPageLoad,
  signInAsAdmin,
  takeScreenshot,
  testChartRendering,
  testFilters,
  testDataTable,
  testPagination,
  testSearch,
  waitForLoadingToComplete,
  testAccessibility,
  verifyPageElements
} from './utils/test-helpers';

test.describe('Comprehensive Marketing Dashboard Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Sign in as admin for all tests
    await signInAsAdmin(page);
  });

  test('should load marketing dashboard with all components', async ({ page }) => {
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Verify page title and main elements
    await expect(page).toHaveTitle(/Marketing Dashboard|NOLK/);
    
    // Check for main components
    const expectedElements = [
      'main, [role="main"]',
      'nav, [role="navigation"]',
      'h1, h2, [data-testid*="title"]'
    ];
    
    await verifyPageElements(page, expectedElements);
    await takeScreenshot(page, 'marketing-dashboard-main');
  });

  test('should display marketing KPI cards', async ({ page }) => {
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for marketing-specific KPI cards
    const kpiCards = page.locator('[data-testid*="kpi"], .kpi-card, [class*="kpi"]');
    const cardCount = await kpiCards.count();

    if (cardCount > 0) {
      // Verify KPI cards are visible
      await expect(kpiCards.first()).toBeVisible();
      
      // Check for marketing-specific metrics
      const marketingMetrics = page.locator('text=/spend|roas|acos|cpc|cpm|impressions|clicks/i');
      const metricsCount = await marketingMetrics.count();
      
      console.log(`Found ${cardCount} marketing KPI cards with ${metricsCount} marketing metrics`);
    }

    await takeScreenshot(page, 'marketing-dashboard-kpi-cards');
  });

  test('should display campaign data table with proper functionality', async ({ page }) => {
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Test data table functionality
    const tableInfo = await testDataTable(page);
    
    if (tableInfo.headerCount > 0) {
      console.log(`Campaign table has ${tableInfo.headerCount} headers and ${tableInfo.rowCount} rows`);
      
      // Test table sorting if available
      const sortableHeaders = page.locator('th[role="columnheader"], th.sortable, th[data-sortable]');
      const sortableCount = await sortableHeaders.count();
      
      if (sortableCount > 0) {
        await sortableHeaders.first().click();
        await page.waitForTimeout(1000);
        await takeScreenshot(page, 'marketing-dashboard-table-sorted');
      }
      
      // Test pagination
      const paginationInfo = await testPagination(page);
      if (paginationInfo.hasPagination) {
        console.log('Table has pagination controls');
        await takeScreenshot(page, 'marketing-dashboard-pagination');
      }
    }
  });

  test('should render spend breakdown charts', async ({ page }) => {
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Test chart rendering for marketing data
    const charts = await testChartRendering(page);
    
    // Look for spend breakdown specific charts
    const spendCharts = page.locator('[data-testid*="spend"], [class*="spend"], text=/spend breakdown/i');
    const spendChartCount = await spendCharts.count();
    
    console.log(`Found ${spendChartCount} spend-related chart elements`);
    await takeScreenshot(page, 'marketing-dashboard-spend-charts');
  });

  test('should have functional marketing filters', async ({ page }) => {
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Test filter functionality
    const activeFilters = await testFilters(page);
    console.log(`Found ${activeFilters.length} active filters`);

    // Test date range filter
    const dateInputs = page.locator('input[type="date"]');
    const dateCount = await dateInputs.count();
    
    if (dateCount > 0) {
      await dateInputs.first().fill('2024-01-01');
      if (dateCount > 1) {
        await dateInputs.nth(1).fill('2024-12-31');
      }
      await page.waitForTimeout(2000);
      await waitForLoadingToComplete(page);
      await takeScreenshot(page, 'marketing-dashboard-date-filter');
    }

    // Test currency filter
    const currencyFilter = page.locator('select[name*="currency"], [data-testid*="currency"]');
    if (await currencyFilter.isVisible()) {
      await currencyFilter.click();
      await page.waitForTimeout(500);
      
      const currencyOptions = page.locator('option, [role="option"]');
      const optionCount = await currencyOptions.count();
      
      if (optionCount > 1) {
        await currencyOptions.nth(1).click();
        await page.waitForTimeout(2000);
        await waitForLoadingToComplete(page);
        await takeScreenshot(page, 'marketing-dashboard-currency-filter');
      }
    }

    // Test group by filter
    const groupByFilter = page.locator('select[name*="group"], [data-testid*="group"]');
    if (await groupByFilter.isVisible()) {
      await groupByFilter.click();
      await page.waitForTimeout(500);
      
      const groupOptions = page.locator('option, [role="option"]');
      const optionCount = await groupOptions.count();
      
      if (optionCount > 1) {
        await groupOptions.nth(1).click();
        await page.waitForTimeout(2000);
        await waitForLoadingToComplete(page);
        await takeScreenshot(page, 'marketing-dashboard-group-filter');
      }
    }
  });

  test('should allow campaign search and filtering', async ({ page }) => {
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Test search functionality
    const searchWorked = await testSearch(page, 'campaign');
    
    if (searchWorked) {
      await waitForLoadingToComplete(page);
      await takeScreenshot(page, 'marketing-dashboard-search-results');
      
      // Clear search
      const searchInput = page.locator('input[type="search"], input[placeholder*="search"]');
      if (await searchInput.isVisible()) {
        await searchInput.clear();
        await page.keyboard.press('Enter');
        await page.waitForTimeout(1000);
      }
    }
  });

  test('should navigate to campaign details', async ({ page }) => {
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for clickable campaign rows or links
    const campaignLinks = page.locator('a[href*="/marketing-dashboard/"], tr[data-href], .campaign-link');
    const linkCount = await campaignLinks.count();

    if (linkCount > 0) {
      const firstCampaignLink = campaignLinks.first();
      
      // Get the href or data-href to verify navigation
      const href = await firstCampaignLink.getAttribute('href') || 
                   await firstCampaignLink.getAttribute('data-href');
      
      if (href) {
        await firstCampaignLink.click();
        await waitForPageLoad(page);
        
        // Verify we're on a campaign detail page
        expect(page.url()).toContain('/marketing-dashboard/');
        await takeScreenshot(page, 'campaign-detail-page');
        
        // Navigate back
        await page.goBack();
        await waitForPageLoad(page);
      }
    }
  });

  test('should display proper marketing metrics calculations', async ({ page }) => {
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for marketing-specific calculated metrics
    const marketingMetrics = [
      'ROAS', 'ACOS', 'CPC', 'CPM', 'CTR', 'Conversion Rate',
      'Cost per Acquisition', 'Return on Ad Spend'
    ];

    let foundMetrics = 0;
    for (const metric of marketingMetrics) {
      const metricElement = page.locator(`text=${metric}`);
      if (await metricElement.isVisible()) {
        foundMetrics++;
      }
    }

    console.log(`Found ${foundMetrics} marketing metrics out of ${marketingMetrics.length} expected`);
    
    // Look for percentage values and currency formatting
    const percentageValues = page.locator('text=/%/');
    const currencyValues = page.locator('text=/\\$|€|£|¥/');
    
    const percentageCount = await percentageValues.count();
    const currencyCount = await currencyValues.count();
    
    console.log(`Found ${percentageCount} percentage values and ${currencyCount} currency values`);
    await takeScreenshot(page, 'marketing-dashboard-metrics');
  });

  test('should handle data export functionality', async ({ page }) => {
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Look for export buttons
    const exportButtons = page.locator('button:has-text("Export"), button:has-text("Download"), [data-testid*="export"]');
    const exportCount = await exportButtons.count();

    if (exportCount > 0) {
      console.log(`Found ${exportCount} export options`);
      
      // Test export button visibility and functionality (don't actually download)
      await expect(exportButtons.first()).toBeVisible();
      await expect(exportButtons.first()).toBeEnabled();
      
      await takeScreenshot(page, 'marketing-dashboard-export-options');
    }
  });

  test('should be responsive across different screen sizes', async ({ page }) => {
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);

    const viewports = [
      { width: 1920, height: 1080, name: 'desktop' },
      { width: 1024, height: 768, name: 'tablet' },
      { width: 375, height: 667, name: 'mobile' }
    ];

    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(1000);
      
      // Verify main elements are still visible
      await expect(page.locator('main, [role="main"]')).toBeVisible();
      
      // Check table responsiveness
      const table = page.locator('table');
      if (await table.isVisible()) {
        // On mobile, table might be in a scrollable container
        const tableContainer = page.locator('.table-container, .overflow-x-auto');
        if (await tableContainer.isVisible()) {
          console.log(`Table is in scrollable container on ${viewport.name}`);
        }
      }
      
      await takeScreenshot(page, `marketing-dashboard-responsive-${viewport.name}`);
    }
  });

  test('should have proper accessibility features', async ({ page }) => {
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);

    const accessibilityInfo = await testAccessibility(page);
    
    // Verify minimum accessibility requirements
    expect(accessibilityInfo.headings).toBeGreaterThan(0);
    expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
    
    // Check table accessibility
    const tables = page.locator('table');
    const tableCount = await tables.count();
    
    if (tableCount > 0) {
      // Check for table headers
      const tableHeaders = page.locator('th');
      const headerCount = await tableHeaders.count();
      expect(headerCount).toBeGreaterThan(0);
    }
    
    console.log('Marketing Dashboard accessibility info:', accessibilityInfo);
    await takeScreenshot(page, 'marketing-dashboard-accessibility');
  });

  test('should load within performance thresholds', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);
    
    const loadTime = Date.now() - startTime;
    
    // Marketing Dashboard should load within 15 seconds (data-heavy page)
    expect(loadTime).toBeLessThan(15000);
    
    console.log(`Marketing Dashboard loaded in ${loadTime}ms`);
  });

  test('should not have critical console errors', async ({ page }) => {
    const consoleErrors: string[] = [];
    
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Filter out acceptable errors
    const criticalErrors = consoleErrors.filter(error =>
      !error.includes('favicon') &&
      !error.includes('404') &&
      !error.includes('net::ERR_FAILED') &&
      !error.includes('ChunkLoadError') &&
      !error.includes('ResizeObserver')
    );

    if (criticalErrors.length > 0) {
      console.log('Critical console errors found:', criticalErrors);
    }

    // For now, log errors but don't fail the test
    // expect(criticalErrors).toHaveLength(0);
  });
});
