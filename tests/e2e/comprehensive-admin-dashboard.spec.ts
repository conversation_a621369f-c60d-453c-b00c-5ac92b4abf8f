import { test, expect } from '@playwright/test';
import {
  waitForPageLoad,
  signInAsAdmin,
  takeScreenshot,
  testDataTable,
  testFormSubmission,
  testModal,
  testSearch,
  testPagination,
  waitForLoadingToComplete,
  testAccessibility,
  verifyPageElements,
  testApiEndpoint
} from './utils/test-helpers';

test.describe('Comprehensive Admin Dashboard Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Sign in as admin for all tests
    await signInAsAdmin(page);
  });

  test('should load admin dashboard with proper authorization', async ({ page }) => {
    await page.goto('/admin');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Verify we're on admin page and not redirected to sign-in
    expect(page.url()).toContain('/admin');
    expect(page.url()).not.toContain('/auth/signin');

    // Verify page title and main elements
    await expect(page).toHaveTitle(/Admin|NOLK/);
    
    // Check for admin-specific elements
    const expectedElements = [
      'main, [role="main"]',
      'nav, [role="navigation"]',
      'h1, h2, [data-testid*="admin"]'
    ];
    
    await verifyPageElements(page, expectedElements);
    await takeScreenshot(page, 'admin-dashboard-main');
  });

  test('should display admin navigation menu', async ({ page }) => {
    await page.goto('/admin');
    await waitForPageLoad(page);

    // Look for admin navigation items
    const adminNavItems = [
      'Users', 'Roles', 'Permissions', 'Groups', 
      'Brands', 'Settings', 'Backups', 'DB Structure'
    ];

    let foundNavItems = 0;
    for (const item of adminNavItems) {
      const navItem = page.locator(`nav a:has-text("${item}"), a[href*="${item.toLowerCase()}"]`);
      if (await navItem.isVisible()) {
        foundNavItems++;
      }
    }

    console.log(`Found ${foundNavItems} admin navigation items out of ${adminNavItems.length} expected`);
    await takeScreenshot(page, 'admin-navigation-menu');
  });

  test('should navigate to and test Users management', async ({ page }) => {
    await page.goto('/admin/users');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Verify users page loaded
    expect(page.url()).toContain('/admin/users');
    
    // Test users table
    const tableInfo = await testDataTable(page);
    if (tableInfo.headerCount > 0) {
      console.log(`Users table has ${tableInfo.headerCount} headers and ${tableInfo.rowCount} rows`);
    }

    // Look for user management buttons
    const addUserButton = page.locator('button:has-text("Add"), button:has-text("Create"), [data-testid*="add-user"]');
    if (await addUserButton.isVisible()) {
      await takeScreenshot(page, 'admin-users-page');
    }

    // Test search functionality
    const searchWorked = await testSearch(page, 'admin');
    if (searchWorked) {
      await takeScreenshot(page, 'admin-users-search');
    }
  });

  test('should navigate to and test Roles management', async ({ page }) => {
    await page.goto('/admin/roles');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Verify roles page loaded
    expect(page.url()).toContain('/admin/roles');
    
    // Test roles table/list
    const tableInfo = await testDataTable(page);
    if (tableInfo.headerCount > 0) {
      console.log(`Roles table has ${tableInfo.headerCount} headers and ${tableInfo.rowCount} rows`);
    }

    // Look for role management features
    const roleElements = page.locator('text=/admin|user|manager|viewer/i');
    const roleCount = await roleElements.count();
    console.log(`Found ${roleCount} role-related elements`);

    await takeScreenshot(page, 'admin-roles-page');
  });

  test('should navigate to and test Permissions management', async ({ page }) => {
    await page.goto('/admin/permissions');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Verify permissions page loaded
    expect(page.url()).toContain('/admin/permissions');
    
    // Test permissions table/list
    const tableInfo = await testDataTable(page);
    if (tableInfo.headerCount > 0) {
      console.log(`Permissions table has ${tableInfo.headerCount} headers and ${tableInfo.rowCount} rows`);
    }

    // Look for permission-related elements
    const permissionElements = page.locator('text=/read|write|delete|create|update/i');
    const permissionCount = await permissionElements.count();
    console.log(`Found ${permissionCount} permission-related elements`);

    await takeScreenshot(page, 'admin-permissions-page');
  });

  test('should navigate to and test Groups management', async ({ page }) => {
    await page.goto('/admin/groups');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Verify groups page loaded
    expect(page.url()).toContain('/admin/groups');
    
    // Test groups table/list
    const tableInfo = await testDataTable(page);
    if (tableInfo.headerCount > 0) {
      console.log(`Groups table has ${tableInfo.headerCount} headers and ${tableInfo.rowCount} rows`);
    }

    await takeScreenshot(page, 'admin-groups-page');
  });

  test('should navigate to and test Brands management', async ({ page }) => {
    await page.goto('/admin/brands');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Verify brands page loaded
    expect(page.url()).toContain('/admin/brands');
    
    // Test brands table/list
    const tableInfo = await testDataTable(page);
    if (tableInfo.headerCount > 0) {
      console.log(`Brands table has ${tableInfo.headerCount} headers and ${tableInfo.rowCount} rows`);
    }

    // Look for brand-specific features
    const brandElements = page.locator('[data-testid*="brand"], .brand, text=/brand/i');
    const brandCount = await brandElements.count();
    console.log(`Found ${brandCount} brand-related elements`);

    await takeScreenshot(page, 'admin-brands-page');
  });

  test('should navigate to and test Settings page', async ({ page }) => {
    await page.goto('/admin/settings');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Verify settings page loaded
    expect(page.url()).toContain('/admin/settings');
    
    // Look for settings forms and options
    const settingsElements = page.locator('form, input, select, textarea, [data-testid*="setting"]');
    const settingsCount = await settingsElements.count();
    console.log(`Found ${settingsCount} settings elements`);

    await takeScreenshot(page, 'admin-settings-page');
  });

  test('should navigate to and test Backups page', async ({ page }) => {
    await page.goto('/admin/backups');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Verify backups page loaded
    expect(page.url()).toContain('/admin/backups');
    
    // Look for backup-related features
    const backupElements = page.locator('text=/backup|restore|download|create/i');
    const backupCount = await backupElements.count();
    console.log(`Found ${backupCount} backup-related elements`);

    // Look for backup buttons
    const backupButtons = page.locator('button:has-text("Backup"), button:has-text("Create"), [data-testid*="backup"]');
    const buttonCount = await backupButtons.count();
    console.log(`Found ${buttonCount} backup buttons`);

    await takeScreenshot(page, 'admin-backups-page');
  });

  test('should navigate to and test DB Structure page', async ({ page }) => {
    await page.goto('/admin/db-structure');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Verify DB structure page loaded
    expect(page.url()).toContain('/admin/db-structure');
    
    // Look for database structure elements
    const dbElements = page.locator('text=/table|column|schema|database/i');
    const dbCount = await dbElements.count();
    console.log(`Found ${dbCount} database structure elements`);

    await takeScreenshot(page, 'admin-db-structure-page');
  });

  test('should test admin API endpoints', async ({ page }) => {
    await page.goto('/admin');
    await waitForPageLoad(page);

    // Test various admin API endpoints
    const adminEndpoints = [
      '/api/admin/users',
      '/api/admin/roles',
      '/api/admin/permissions',
      '/api/admin/groups',
      '/api/admin/brands'
    ];

    for (const endpoint of adminEndpoints) {
      try {
        const response = await testApiEndpoint(page, endpoint);
        console.log(`${endpoint}: ${response.status()}`);
      } catch (error) {
        console.log(`${endpoint}: Failed - ${error}`);
      }
    }
  });

  test('should handle admin form submissions', async ({ page }) => {
    await page.goto('/admin/users');
    await waitForPageLoad(page);

    // Look for add/create user form
    const addButton = page.locator('button:has-text("Add"), button:has-text("Create")');
    
    if (await addButton.isVisible()) {
      await addButton.click();
      await page.waitForTimeout(1000);
      
      // Look for form modal or page
      const form = page.locator('form, [data-testid*="form"]');
      
      if (await form.isVisible()) {
        // Test form fields (don't actually submit)
        const inputs = form.locator('input, select, textarea');
        const inputCount = await inputs.count();
        console.log(`Found form with ${inputCount} input fields`);
        
        await takeScreenshot(page, 'admin-user-form');
        
        // Close form/modal
        const cancelButton = page.locator('button:has-text("Cancel"), button:has-text("Close")');
        if (await cancelButton.isVisible()) {
          await cancelButton.click();
        }
      }
    }
  });

  test('should be responsive across different screen sizes', async ({ page }) => {
    await page.goto('/admin');
    await waitForPageLoad(page);

    const viewports = [
      { width: 1920, height: 1080, name: 'desktop' },
      { width: 1024, height: 768, name: 'tablet' },
      { width: 375, height: 667, name: 'mobile' }
    ];

    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(1000);
      
      // Verify main elements are still visible
      await expect(page.locator('main, [role="main"]')).toBeVisible();
      
      // Check admin navigation on different screens
      const adminNav = page.locator('nav');
      if (await adminNav.isVisible()) {
        console.log(`Admin navigation visible on ${viewport.name}`);
      }
      
      await takeScreenshot(page, `admin-dashboard-responsive-${viewport.name}`);
    }
  });

  test('should have proper accessibility features', async ({ page }) => {
    await page.goto('/admin');
    await waitForPageLoad(page);

    const accessibilityInfo = await testAccessibility(page);
    
    // Verify minimum accessibility requirements for admin interface
    expect(accessibilityInfo.headings).toBeGreaterThan(0);
    expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
    
    console.log('Admin Dashboard accessibility info:', accessibilityInfo);
    await takeScreenshot(page, 'admin-dashboard-accessibility');
  });

  test('should load within performance thresholds', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/admin');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);
    
    const loadTime = Date.now() - startTime;
    
    // Admin Dashboard should load within 10 seconds
    expect(loadTime).toBeLessThan(10000);
    
    console.log(`Admin Dashboard loaded in ${loadTime}ms`);
  });

  test('should not have critical console errors', async ({ page }) => {
    const consoleErrors: string[] = [];
    
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    await page.goto('/admin');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Filter out acceptable errors
    const criticalErrors = consoleErrors.filter(error =>
      !error.includes('favicon') &&
      !error.includes('404') &&
      !error.includes('net::ERR_FAILED') &&
      !error.includes('ChunkLoadError') &&
      !error.includes('ResizeObserver')
    );

    if (criticalErrors.length > 0) {
      console.log('Critical console errors found:', criticalErrors);
    }

    // For now, log errors but don't fail the test
    // expect(criticalErrors).toHaveLength(0);
  });
});
