<testsuites id="" name="" tests="5" failures="3" skipped="0" errors="0" time="32.040898999999996">
<testsuite name="auth.spec.ts" timestamp="2025-05-30T01:04:37.894Z" hostname="chromium" tests="5" failures="3" skipped="0" time="77.271" errors="0">
<testcase name="Credentials Authentication › should successfully sign in with farbour/admin credentials" classname="auth.spec.ts" time="3.614">
<failure message="auth.spec.ts:178:7 should successfully sign in with farbour/admin credentials" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:178:7 › Credentials Authentication › should successfully sign in with farbour/admin credentials 

    Error: expect.toBeVisible: Error: strict mode violation: locator('h1, h2, [data-testid="dashboard-title"]') resolved to 2 elements:
        1) <h1 class="text-2xl sm:text-3xl font-bold tracking-tight">Dashboard</h1> aka getByRole('heading', { name: 'Dashboard' })
        2) <h2 class="text-sm sm:text-lg text-muted-foreground mt-1">Overview of your business performance</h2> aka getByRole('heading', { name: 'Overview of your business' })

    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for locator('h1, h2, [data-testid="dashboard-title"]')


      188 |     // Verify dashboard elements are present
      189 |     const dashboardHeading = page.locator('h1, h2, [data-testid="dashboard-title"]');
    > 190 |     await expect(dashboardHeading).toBeVisible({ timeout: 10000 });
          |                                    ^
      191 |
      192 |     // Take screenshot of successful login
      193 |     await takeScreenshot(page, 'successful-admin-login');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:190:36

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/auth-Credentials-Authentic-b9324-h-farbour-admin-credentials-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/auth-Credentials-Authentic-b9324-h-farbour-admin-credentials-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/auth-Credentials-Authentic-b9324-h-farbour-admin-credentials-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/auth-Credentials-Authentic-b9324-h-farbour-admin-credentials-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/auth-Credentials-Authentic-b9324-h-farbour-admin-credentials-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/auth-Credentials-Authentic-b9324-h-farbour-admin-credentials-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Credentials Authentication › should reject invalid credentials" classname="auth.spec.ts" time="30.421">
<failure message="auth.spec.ts:196:7 should reject invalid credentials" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:196:7 › Credentials Authentication › should reject invalid credentials ─

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


      207 |
      208 |     // Fill in invalid credentials
    > 209 |     await page.fill('input[name="username"]', 'invalid');
          |                ^
      210 |     await page.fill('input[name="password"]', 'wrongpassword');
      211 |
      212 |     // Click sign in button
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:209:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/auth-Credentials-Authentic-ab45c--reject-invalid-credentials-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/auth-Credentials-Authentic-ab45c--reject-invalid-credentials-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/auth-Credentials-Authentic-ab45c--reject-invalid-credentials-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/auth-Credentials-Authentic-ab45c--reject-invalid-credentials-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/auth-Credentials-Authentic-ab45c--reject-invalid-credentials-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/auth-Credentials-Authentic-ab45c--reject-invalid-credentials-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Credentials Authentication › should verify admin access after login" classname="auth.spec.ts" time="5.703">
</testcase>
<testcase name="Credentials Authentication › should maintain session across page navigation" classname="auth.spec.ts" time="7.144">
</testcase>
<testcase name="Credentials Authentication › should successfully sign out" classname="auth.spec.ts" time="30.389">
<failure message="auth.spec.ts:264:7 should successfully sign out" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:264:7 › Credentials Authentication › should successfully sign out ──────

    Test timeout of 30000ms exceeded.

    Error: page.waitForURL: Test timeout of 30000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "/auth/signin" until "load"
    ============================================================

       at utils/test-helpers.ts:90

      88 |
      89 |   // Wait for redirect to sign-in page
    > 90 |   await page.waitForURL('/auth/signin');
         |              ^
      91 | }
      92 |
      93 | /**
        at signOut (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:90:14)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:269:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/auth-Credentials-Authentication-should-successfully-sign-out-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/auth-Credentials-Authentication-should-successfully-sign-out-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/auth-Credentials-Authentication-should-successfully-sign-out-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/auth-Credentials-Authentication-should-successfully-sign-out-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/auth-Credentials-Authentication-should-successfully-sign-out-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/auth-Credentials-Authentication-should-successfully-sign-out-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>