<testsuites id="" name="" tests="123" failures="40" skipped="0" errors="0" time="165.82609699999998">
<testsuite name="comprehensive-admin-dashboard.spec.ts" timestamp="2025-05-30T02:05:20.122Z" hostname="chromium" tests="16" failures="5" skipped="0" time="173.741" errors="0">
<testcase name="Comprehensive Admin Dashboard Testing › should load admin dashboard with proper authorization" classname="comprehensive-admin-dashboard.spec.ts" time="27.837">
<failure message="comprehensive-admin-dashboard.spec.ts:23:7 should load admin dashboard with proper authorization" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-admin-dashboard.spec.ts:23:7 › Comprehensive Admin Dashboard Testing › should load admin dashboard with proper authorization 

    Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)

    Locator: locator(':root')
    Expected pattern: /Admin|NOLK/
    Received string:  "Create Next App"
    Call log:
      - expect.toHaveTitle with timeout 15000ms
      - waiting for locator(':root')
        19 × locator resolved to <html lang="en">…</html>
           - unexpected value "Create Next App"


      31 |
      32 |     // Verify page title and main elements
    > 33 |     await expect(page).toHaveTitle(/Admin|NOLK/);
         |                        ^
      34 |     
      35 |     // Check for admin-specific elements
      36 |     const expectedElements = [
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:33:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-722b6-d-with-proper-authorization-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-722b6-d-with-proper-authorization-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-admin-dashbo-722b6-d-with-proper-authorization-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-722b6-d-with-proper-authorization-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-722b6-d-with-proper-authorization-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-722b6-d-with-proper-authorization-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should display admin navigation menu" classname="comprehensive-admin-dashboard.spec.ts" time="12.854">
<failure message="comprehensive-admin-dashboard.spec.ts:46:7 should display admin navigation menu" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-admin-dashboard.spec.ts:46:7 › Comprehensive Admin Dashboard Testing › should display admin navigation menu 

    Error: locator.isVisible: Error: strict mode violation: locator('nav a:has-text("Users"), a[href*="users"]') resolved to 2 elements:
        1) <a href="/admin/users" data-size="default" data-active="false" data-state="closed" data-sidebar="menu-button" data-slot="sidebar-menu-button" class="peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left outline-hidden ring-sidebar-ring transition-[width,height,padding] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabl…>…</a> aka getByRole('link', { name: 'Users', exact: true })
        2) <a href="/admin/users">…</a> aka getByRole('link', { name: 'User Management Manage users' })

    Call log:
        - checking visibility of locator('nav a:has-text("Users"), a[href*="users"]')


      57 |     for (const item of adminNavItems) {
      58 |       const navItem = page.locator(`nav a:has-text("${item}"), a[href*="${item.toLowerCase()}"]`);
    > 59 |       if (await navItem.isVisible()) {
         |                         ^
      60 |         foundNavItems++;
      61 |       }
      62 |     }
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:59:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-00f9b-splay-admin-navigation-menu-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-00f9b-splay-admin-navigation-menu-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-admin-dashbo-00f9b-splay-admin-navigation-menu-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-00f9b-splay-admin-navigation-menu-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-00f9b-splay-admin-navigation-menu-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-00f9b-splay-admin-navigation-menu-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Users management" classname="comprehensive-admin-dashboard.spec.ts" time="13.224">
<system-out>
<![CDATA[Users table has 6 headers and 14 rows
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Roles management" classname="comprehensive-admin-dashboard.spec.ts" time="12.876">
<system-out>
<![CDATA[Roles table has 4 headers and 4 rows
Found 10 role-related elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Permissions management" classname="comprehensive-admin-dashboard.spec.ts" time="12.88">
<system-out>
<![CDATA[Permissions table has 6 headers and 2 rows
Found 3 permission-related elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Groups management" classname="comprehensive-admin-dashboard.spec.ts" time="13.29">
<system-out>
<![CDATA[Groups table has 4 headers and 4 rows
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Brands management" classname="comprehensive-admin-dashboard.spec.ts" time="13.249">
<failure message="comprehensive-admin-dashboard.spec.ts:156:7 should navigate to and test Brands management" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-admin-dashboard.spec.ts:156:7 › Comprehensive Admin Dashboard Testing › should navigate to and test Brands management 

    Error: locator.count: Unexpected token "=" while parsing css selector "[data-testid*="brand"], .brand, text=/brand/i". Did you mean to CSS.escape it?

      170 |     // Look for brand-specific features
      171 |     const brandElements = page.locator('[data-testid*="brand"], .brand, text=/brand/i');
    > 172 |     const brandCount = await brandElements.count();
          |                                            ^
      173 |     console.log(`Found ${brandCount} brand-related elements`);
      174 |
      175 |     await takeScreenshot(page, 'admin-brands-page');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:172:44

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-c9ae0--and-test-Brands-management-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-c9ae0--and-test-Brands-management-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[Brands table has 8 headers and 23 rows

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-c9ae0--and-test-Brands-management-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-c9ae0--and-test-Brands-management-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Settings page" classname="comprehensive-admin-dashboard.spec.ts" time="12.903">
<system-out>
<![CDATA[Found 0 settings elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Backups page" classname="comprehensive-admin-dashboard.spec.ts" time="7.18">
<system-out>
<![CDATA[Found 3 backup-related elements
Found 0 backup buttons
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test DB Structure page" classname="comprehensive-admin-dashboard.spec.ts" time="6.301">
<system-out>
<![CDATA[Found 0 database structure elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should test admin API endpoints" classname="comprehensive-admin-dashboard.spec.ts" time="6.985">
<system-out>
<![CDATA[/api/admin/users: 200
/api/admin/roles: 200
/api/admin/permissions: 200
/api/admin/groups: 200
/api/admin/brands: 200
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should handle admin form submissions" classname="comprehensive-admin-dashboard.spec.ts" time="7.053">
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should be responsive across different screen sizes" classname="comprehensive-admin-dashboard.spec.ts" time="7.871">
<failure message="comprehensive-admin-dashboard.spec.ts:285:7 should be responsive across different screen sizes" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-admin-dashboard.spec.ts:285:7 › Comprehensive Admin Dashboard Testing › should be responsive across different screen sizes 

    Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role="main"]') resolved to 2 elements:
        1) <main data-slot="sidebar-inset" class="bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2">…</main> aka getByText('Toggle SidebarDashboardAdminAdmin DashboardManage users, roles, and system')
        2) <main class="flex-1 overflow-auto">…</main> aka getByRole('main').filter({ hasText: 'Toggle' }).getByRole('main')

    Call log:
      - expect.toBeVisible with timeout 15000ms
      - waiting for locator('main, [role="main"]')


      298 |       
      299 |       // Verify main elements are still visible
    > 300 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
          |                                                         ^
      301 |       
      302 |       // Check admin navigation on different screens
      303 |       const adminNav = page.locator('nav');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:300:57

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-7048f-ross-different-screen-sizes-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-7048f-ross-different-screen-sizes-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-admin-dashbo-7048f-ross-different-screen-sizes-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-7048f-ross-different-screen-sizes-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-7048f-ross-different-screen-sizes-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-7048f-ross-different-screen-sizes-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should have proper accessibility features" classname="comprehensive-admin-dashboard.spec.ts" time="6.921">
<failure message="comprehensive-admin-dashboard.spec.ts:312:7 should have proper accessibility features" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-admin-dashboard.spec.ts:312:7 › Comprehensive Admin Dashboard Testing › should have proper accessibility features 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      318 |     // Verify minimum accessibility requirements for admin interface
      319 |     expect(accessibilityInfo.headings).toBeGreaterThan(0);
    > 320 |     expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
          |                                         ^
      321 |     
      322 |     console.log('Admin Dashboard accessibility info:', accessibilityInfo);
      323 |     await takeScreenshot(page, 'admin-dashboard-accessibility');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:320:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-14f8d-oper-accessibility-features-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-14f8d-oper-accessibility-features-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-admin-dashbo-14f8d-oper-accessibility-features-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-14f8d-oper-accessibility-features-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-14f8d-oper-accessibility-features-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-14f8d-oper-accessibility-features-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should load within performance thresholds" classname="comprehensive-admin-dashboard.spec.ts" time="6.549">
<system-out>
<![CDATA[Admin Dashboard loaded in 1407ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should not have critical console errors" classname="comprehensive-admin-dashboard.spec.ts" time="5.768">
<system-out>
<![CDATA[Critical console errors found: [
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9578:33)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9581:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)'[39m,
  [32m'Error fetching saved views: TypeError: Failed to fetch\n'[39m +
    [32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\n'[39m +
    [32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\n'[39m +
    [32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'[39m,
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'[39m,
  [32m'Error fetching saved views: TypeError: Failed to fetch\n'[39m +
    [32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\n'[39m +
    [32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\n'[39m +
    [32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9578:33)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)'[39m
]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-api.spec.ts" timestamp="2025-05-30T02:05:20.122Z" hostname="chromium" tests="13" failures="0" skipped="0" time="79.844" errors="0">
<testcase name="Comprehensive API Testing › should test dashboard API endpoints" classname="comprehensive-api.spec.ts" time="5.639">
<system-out>
<![CDATA[/api/dashboard/flexible-kpis: 200
/api/dashboard/flexible-kpis returned data: [
  [32m'Gross Revenue'[39m,         [32m'Net Revenue'[39m,
  [32m'Gross Margin'[39m,          [32m'Adspend'[39m,
  [32m'Contribution Margin'[39m,   [32m'% Gross Margin'[39m,
  [32m'% Contribution Margin'[39m, [32m'% Adspend'[39m,
  [32m'Landed Cost'[39m,           [32m'Fulfillment Cost'[39m,
  [32m'Transaction Cost'[39m,      [32m'Discount'[39m,
  [32m'Refund'[39m,                [32m'% Landed Cost'[39m,
  [32m'% Fulfillment Cost'[39m,    [32m'% Transaction Cost'[39m,
  [32m'% Discount'[39m,            [32m'% Refund'[39m,
  [32m'ACOS'[39m,                  [32m'TACOS'[39m,
  [32m'TCAC'[39m,                  [32m'Website Traffic'[39m,
  [32m'Conversion Rate'[39m,       [32m'Organic Traffic'[39m,
  [32m'Paid Traffic'[39m
]
/api/dashboard/kpi-data: 404
/api/dashboard/chart-data: 404
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test marketing API endpoints" classname="comprehensive-api.spec.ts" time="5.731">
<system-out>
<![CDATA[/api/marketing/campaigns: 404
/api/marketing/kpis: 404
/api/marketing/spend-breakdown: 404
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test admin API endpoints with proper authorization" classname="comprehensive-api.spec.ts" time="7.187">
<system-out>
<![CDATA[/api/admin/users: 200
/api/admin/users returned data: [
  [32m'0'[39m,  [32m'1'[39m,  [32m'2'[39m,  [32m'3'[39m,
  [32m'4'[39m,  [32m'5'[39m,  [32m'6'[39m,  [32m'7'[39m,
  [32m'8'[39m,  [32m'9'[39m,  [32m'10'[39m, [32m'11'[39m,
  [32m'12'[39m, [32m'13'[39m
]
/api/admin/roles: 200
/api/admin/roles returned data: [ [32m'data'[39m, [32m'pagination'[39m ]
/api/admin/permissions: 200
/api/admin/permissions returned data: [ [32m'0'[39m, [32m'1'[39m ]
/api/admin/groups: 200
/api/admin/groups returned data: [ [32m'data'[39m, [32m'pagination'[39m ]
/api/admin/brands: 200
/api/admin/brands returned data: [ [32m'data'[39m, [32m'pagination'[39m ]
/api/admin/settings: 401
/api/admin/settings: Unauthorized (expected for non-admin users)
/api/admin/backups: 401
/api/admin/backups: Unauthorized (expected for non-admin users)
/api/admin/db-structure: 401
/api/admin/db-structure: Unauthorized (expected for non-admin users)
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test budget API endpoint" classname="comprehensive-api.spec.ts" time="5.764">
<system-out>
<![CDATA[Budget API: 200
Budget API returned data: [ [32m'budgets'[39m ]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test AI assistant API endpoint" classname="comprehensive-api.spec.ts" time="11.236">
<system-out>
<![CDATA[AI Assistant GET: 405
AI Assistant POST: 500
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API error handling" classname="comprehensive-api.spec.ts" time="5.617">
<system-out>
<![CDATA[/api/nonexistent: 404
/api/dashboard/invalid: 404
/api/admin/invalid: 404
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API with query parameters" classname="comprehensive-api.spec.ts" time="5.142">
<system-out>
<![CDATA[/api/dashboard/flexible-kpis?startDate=2024-01-01&endDate=2024-12-31: 200
/api/dashboard/flexible-kpis?startDate=2024-01-01&endDate=2024-12-31 with params returned: [
  [32m'Gross Revenue'[39m,         [32m'Net Revenue'[39m,
  [32m'Gross Margin'[39m,          [32m'Adspend'[39m,
  [32m'Contribution Margin'[39m,   [32m'% Gross Margin'[39m,
  [32m'% Contribution Margin'[39m, [32m'% Adspend'[39m,
  [32m'Landed Cost'[39m,           [32m'Fulfillment Cost'[39m,
  [32m'Transaction Cost'[39m,      [32m'Discount'[39m,
  [32m'Refund'[39m,                [32m'% Landed Cost'[39m,
  [32m'% Fulfillment Cost'[39m,    [32m'% Transaction Cost'[39m,
  [32m'% Discount'[39m,            [32m'% Refund'[39m,
  [32m'ACOS'[39m,                  [32m'TACOS'[39m,
  [32m'TCAC'[39m,                  [32m'Website Traffic'[39m,
  [32m'Conversion Rate'[39m,       [32m'Organic Traffic'[39m,
  [32m'Paid Traffic'[39m
]
/api/marketing/campaigns?brand=test&currency=USD: 404
/api/budget?brand=test&brands=true: 200
/api/budget?brand=test&brands=true with params returned: [ [32m'brands'[39m ]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API response times" classname="comprehensive-api.spec.ts" time="5.528">
<system-out>
<![CDATA[/api/dashboard/flexible-kpis: 200 in 328ms
/api/marketing/campaigns: 404 in 130ms
/api/budget: 200 in 122ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API data validation" classname="comprehensive-api.spec.ts" time="5.417">
</testcase>
<testcase name="Comprehensive API Testing › should test API authentication requirements" classname="comprehensive-api.spec.ts" time="5.61">
<system-out>
<![CDATA[/api/admin/users (unauthenticated): 200
/api/admin/users (unauthenticated): Failed as expected - Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoContain[2m([22m[32mexpected[39m[2m) // indexOf[22m

Expected value: [32m200[39m
Received array: [31m[401, 302, 403][39m
/api/admin/roles (unauthenticated): 200
/api/admin/roles (unauthenticated): Failed as expected - Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoContain[2m([22m[32mexpected[39m[2m) // indexOf[22m

Expected value: [32m200[39m
Received array: [31m[401, 302, 403][39m
/api/dashboard/flexible-kpis (unauthenticated): 200
/api/dashboard/flexible-kpis (unauthenticated): Failed as expected - Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoContain[2m([22m[32mexpected[39m[2m) // indexOf[22m

Expected value: [32m200[39m
Received array: [31m[401, 302, 403][39m
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API CORS headers" classname="comprehensive-api.spec.ts" time="5.191">
<system-out>
<![CDATA[API Response Headers:
Content-Type: application/json
Cache-Control: no-store, max-age=0
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API rate limiting (if implemented)" classname="comprehensive-api.spec.ts" time="5.909">
<system-out>
<![CDATA[Request 1: 200
Request 2: 200
Request 3: 200
Request 4: 200
Request 5: 200
Request 6: 200
Request 7: 200
Request 8: 200
Request 9: 200
Request 10: 200
Successful requests: 10, Rate limited: 0
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API monitoring endpoints" classname="comprehensive-api.spec.ts" time="5.873">
<system-out>
<![CDATA[/api/monitoring/health: 200
/api/monitoring/health health check: {
  overall: [32m'healthy'[39m,
  timestamp: [33m1748570754501[39m,
  uptime: [33m37.830651584[39m,
  version: [32m'0.1.0'[39m,
  environment: [32m'development'[39m,
  checks: [
    {
      service: [32m'SQLite Database'[39m,
      status: [32m'healthy'[39m,
      responseTime: [33m0[39m,
      details: [32m'Database responding normally'[39m,
      lastChecked: [33m1748570754479[39m
    },
    {
      service: [32m'Redshift Database'[39m,
      status: [32m'healthy'[39m,
      responseTime: [33m22[39m,
      details: [32m'Redshift responding normally'[39m,
      lastChecked: [33m1748570754501[39m
    },
    {
      service: [32m'NextAuth'[39m,
      status: [32m'healthy'[39m,
      details: [32m'NextAuth configured'[39m,
      lastChecked: [33m1748570754501[39m
    },
    {
      service: [32m'Google OAuth'[39m,
      status: [32m'healthy'[39m,
      details: [32m'Google OAuth configured'[39m,
      lastChecked: [33m1748570754501[39m
    }
  ],
  performance: {
    memory: {
      rss: [33m3004432384[39m,
      heapTotal: [33m423673856[39m,
      heapUsed: [33m227225544[39m,
      external: [33m50693947[39m,
      arrayBuffers: [33m48111090[39m
    },
    cpu: { user: [33m68584794[39m, system: [33m21672784[39m },
    queries: {
      totalQueries: [33m0[39m,
      slowQueries: [33m0[39m,
      avgDuration: [33m0[39m,
      cacheHitRate: [33m0[39m,
      topSlowQueries: []
    },
    apis: {
      totalRequests: [33m0[39m,
      errorRequests: [33m0[39m,
      slowRequests: [33m0[39m,
      avgDuration: [33m0[39m,
      errorRate: [33m0[39m,
      topSlowEndpoints: []
    },
    errors: { totalErrors: [33m0[39m, errorsByType: {}, recentErrors: [] }
  },
  database: {
    sqlite: {
      status: [32m'healthy'[39m,
      responseTime: [33m0[39m,
      details: [32m'Database responding normally'[39m
    },
    redshift: {
      status: [32m'healthy'[39m,
      responseTime: [33m22[39m,
      poolStats: [36m[Object][39m,
      details: [32m'Redshift responding normally'[39m
    }
  }
}
/api/monitoring/status: 404
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-brand-deep-dive.spec.ts" timestamp="2025-05-30T02:05:20.122Z" hostname="chromium" tests="12" failures="6" skipped="0" time="89.223" errors="0">
<testcase name="Comprehensive Brand Deep Dive Testing › should load brand deep dive page with all components" classname="comprehensive-brand-deep-dive.spec.ts" time="21.955">
<failure message="comprehensive-brand-deep-dive.spec.ts:20:7 should load brand deep dive page with all components" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-brand-deep-dive.spec.ts:20:7 › Comprehensive Brand Deep Dive Testing › should load brand deep dive page with all components 

    Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)

    Locator: locator(':root')
    Expected pattern: /Brand Deep Dive|NOLK/
    Received string:  "Create Next App"
    Call log:
      - expect.toHaveTitle with timeout 15000ms
      - waiting for locator(':root')
        19 × locator resolved to <html lang="en">…</html>
           - unexpected value "Create Next App"


      24 |
      25 |     // Verify page title and main elements
    > 26 |     await expect(page).toHaveTitle(/Brand Deep Dive|NOLK/);
         |                        ^
      27 |     
      28 |     // Check for main components
      29 |     const expectedElements = [
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:26:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-1c6ae-ve-page-with-all-components-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-1c6ae-ve-page-with-all-components-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-brand-deep-d-1c6ae-ve-page-with-all-components-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-1c6ae-ve-page-with-all-components-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-1c6ae-ve-page-with-all-components-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-1c6ae-ve-page-with-all-components-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should display brand selector and allow brand selection" classname="comprehensive-brand-deep-dive.spec.ts" time="6.061">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should display KPI cards specific to selected brand" classname="comprehensive-brand-deep-dive.spec.ts" time="6.388">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should render brand performance charts" classname="comprehensive-brand-deep-dive.spec.ts" time="6.195">
<failure message="comprehensive-brand-deep-dive.spec.ts:89:7 should render brand performance charts" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-brand-deep-dive.spec.ts:89:7 › Comprehensive Brand Deep Dive Testing › should render brand performance charts 

    Error: expect.toBeVisible: Error: strict mode violation: locator('svg, canvas, .recharts-wrapper') resolved to 23 elements:
        1) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-inner-shadow-top !size-5">…</svg> aka getByRole('link', { name: 'NOLK' })
        2) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dashboard ">…</svg> aka getByRole('listitem').filter({ hasText: /^Dashboard$/ }).getByRole('link')
        3) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-list-details ">…</svg> aka getByRole('listitem').filter({ hasText: 'Brand Deep Dive' }).getByRole('link')
        4) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-chart-bar ">…</svg> aka getByRole('link', { name: 'Marketing Dashboard' })
        5) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-report ">…</svg> aka getByRole('link', { name: 'Executive Summary' })
        6) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-wallet ">…</svg> aka getByRole('link', { name: 'Budget' })
        7) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-file-ai ">…</svg> aka getByRole('link', { name: 'AI Assistant' })
        8) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-settings ">…</svg> aka getByRole('link', { name: 'Settings' })
        9) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dots-vertical ml-auto size-4">…</svg> aka getByRole('button', { name: 'F François Arbour farbour' })
        10) <svg width="24" height="24" fill="none" stroke-width="2" aria-hidden="true" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-panel-left" xmlns="http://www.w3.org/2000/svg">…</svg> aka getByRole('button', { name: 'Toggle Sidebar' })
        ...

    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for locator('svg, canvas, .recharts-wrapper')


       at utils/test-helpers.ts:386

      384 | export async function testChartRendering(page: Page, chartSelector = 'svg, canvas, .recharts-wrapper') {
      385 |   const chart = page.locator(chartSelector);
    > 386 |   await expect(chart).toBeVisible({ timeout: 10000 });
          |                       ^
      387 |
      388 |   // Wait for chart to fully render
      389 |   await page.waitForTimeout(2000);
        at testChartRendering (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:386:23)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:95:44

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-1f8e4-er-brand-performance-charts-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-1f8e4-er-brand-performance-charts-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-brand-deep-d-1f8e4-er-brand-performance-charts-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-1f8e4-er-brand-performance-charts-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-1f8e4-er-brand-performance-charts-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-1f8e4-er-brand-performance-charts-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should display marketing campaigns for selected brand" classname="comprehensive-brand-deep-dive.spec.ts" time="5.985">
<failure message="comprehensive-brand-deep-dive.spec.ts:115:7 should display marketing campaigns for selected brand" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-brand-deep-dive.spec.ts:115:7 › Comprehensive Brand Deep Dive Testing › should display marketing campaigns for selected brand 

    Error: locator.isVisible: Unexpected token "=" while parsing css selector "[data-testid*="campaign"], .campaign, text=/campaign/i". Did you mean to CSS.escape it?
    Call log:
        - checking visibility of [data-testid*="campaign"], .campaign, text=/campaign/i >> nth=0


      121 |     const campaignsSection = page.locator('[data-testid*="campaign"], .campaign, text=/campaign/i');
      122 |     
    > 123 |     if (await campaignsSection.first().isVisible()) {
          |                                        ^
      124 |       // Check for campaign data
      125 |       const campaignItems = page.locator('[data-testid*="campaign-item"], .campaign-item, tr');
      126 |       const campaignCount = await campaignItems.count();
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:123:40

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-d54f0-ampaigns-for-selected-brand-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-d54f0-ampaigns-for-selected-brand-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-brand-deep-d-d54f0-ampaigns-for-selected-brand-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-d54f0-ampaigns-for-selected-brand-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-d54f0-ampaigns-for-selected-brand-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-d54f0-ampaigns-for-selected-brand-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should have functional brand-specific filters" classname="comprehensive-brand-deep-dive.spec.ts" time="6.467">
<failure message="comprehensive-brand-deep-dive.spec.ts:140:7 should have functional brand-specific filters" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-brand-deep-dive.spec.ts:140:7 › Comprehensive Brand Deep Dive Testing › should have functional brand-specific filters 

    Error: locator.isVisible: Error: strict mode violation: locator('input[type="date"]') resolved to 2 elements:
        1) <input type="date" id="start-date" data-slot="input" value="2025-03-01" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 …/> aka getByRole('textbox', { name: 'Start Date' })
        2) <input type="date" id="end-date" data-slot="input" value="2025-05-30" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md…/> aka getByRole('textbox', { name: 'End Date' })

    Call log:
        - checking visibility of locator('input[type="date"]')


       at utils/test-helpers.ts:411

      409 |   for (const selector of filterElements) {
      410 |     const element = page.locator(selector);
    > 411 |     if (await element.isVisible()) {
          |                       ^
      412 |       activeFilters.push(selector);
      413 |     }
      414 |   }
        at testFilters (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:411:23)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:146:27

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-6fb13-onal-brand-specific-filters-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-6fb13-onal-brand-specific-filters-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-brand-deep-d-6fb13-onal-brand-specific-filters-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-6fb13-onal-brand-specific-filters-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-6fb13-onal-brand-specific-filters-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-6fb13-onal-brand-specific-filters-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should display tabs and allow tab navigation" classname="comprehensive-brand-deep-dive.spec.ts" time="5.963">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should handle brand comparison if available" classname="comprehensive-brand-deep-dive.spec.ts" time="5.89">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should handle export functionality if available" classname="comprehensive-brand-deep-dive.spec.ts" time="5.66">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should be responsive across different screen sizes" classname="comprehensive-brand-deep-dive.spec.ts" time="6.821">
<failure message="comprehensive-brand-deep-dive.spec.ts:246:7 should be responsive across different screen sizes" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-brand-deep-dive.spec.ts:246:7 › Comprehensive Brand Deep Dive Testing › should be responsive across different screen sizes 

    Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role="main"]') resolved to 2 elements:
        1) <main data-slot="sidebar-inset" class="bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2">…</main> aka getByText('Toggle SidebarDashboardBrand Deep DiveBrand Deep DiveDetailed analysis of brand')
        2) <main class="flex-1 overflow-auto">…</main> aka getByRole('main').filter({ hasText: 'Toggle SidebarDashboardBrand' }).getByRole('main')

    Call log:
      - expect.toBeVisible with timeout 15000ms
      - waiting for locator('main, [role="main"]')


      259 |       
      260 |       // Verify main elements are still visible and properly arranged
    > 261 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
          |                                                         ^
      262 |       
      263 |       // Check if mobile menu appears on small screens
      264 |       if (viewport.width <= 768) {
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:261:57

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-ce6db-ross-different-screen-sizes-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-ce6db-ross-different-screen-sizes-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-brand-deep-d-ce6db-ross-different-screen-sizes-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-ce6db-ross-different-screen-sizes-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-ce6db-ross-different-screen-sizes-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-ce6db-ross-different-screen-sizes-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should have proper accessibility features" classname="comprehensive-brand-deep-dive.spec.ts" time="5.909">
<failure message="comprehensive-brand-deep-dive.spec.ts:277:7 should have proper accessibility features" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-brand-deep-dive.spec.ts:277:7 › Comprehensive Brand Deep Dive Testing › should have proper accessibility features 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      283 |     // Verify minimum accessibility requirements
      284 |     expect(accessibilityInfo.headings).toBeGreaterThan(0);
    > 285 |     expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
          |                                         ^
      286 |     
      287 |     console.log('Brand Deep Dive accessibility info:', accessibilityInfo);
      288 |     await takeScreenshot(page, 'brand-deep-dive-accessibility');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:285:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-53baa-oper-accessibility-features-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-53baa-oper-accessibility-features-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-brand-deep-d-53baa-oper-accessibility-features-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-53baa-oper-accessibility-features-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-53baa-oper-accessibility-features-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-53baa-oper-accessibility-features-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should load within performance thresholds" classname="comprehensive-brand-deep-dive.spec.ts" time="5.929">
<system-out>
<![CDATA[Brand Deep Dive loaded in 1148ms
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-budget-ai.spec.ts" timestamp="2025-05-30T02:05:20.122Z" hostname="chromium" tests="15" failures="6" skipped="0" time="123.765" errors="0">
<testcase name="Comprehensive Budget Page Testing › should load budget page with all components" classname="comprehensive-budget-ai.spec.ts" time="21.795">
<failure message="comprehensive-budget-ai.spec.ts:22:7 should load budget page with all components" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-budget-ai.spec.ts:22:7 › Comprehensive Budget Page Testing › should load budget page with all components 

    Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)

    Locator: locator(':root')
    Expected pattern: /Budget|NOLK/
    Received string:  "Create Next App"
    Call log:
      - expect.toHaveTitle with timeout 15000ms
      - waiting for locator(':root')
        19 × locator resolved to <html lang="en">…</html>
           - unexpected value "Create Next App"


      26 |
      27 |     // Verify page title and main elements
    > 28 |     await expect(page).toHaveTitle(/Budget|NOLK/);
         |                        ^
      29 |     
      30 |     // Check for main components
      31 |     const expectedElements = [
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:28:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-9eaca-et-page-with-all-components-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-9eaca-et-page-with-all-components-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-budget-ai-Co-9eaca-et-page-with-all-components-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-9eaca-et-page-with-all-components-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-9eaca-et-page-with-all-components-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-9eaca-et-page-with-all-components-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Budget Page Testing › should display budget data and charts" classname="comprehensive-budget-ai.spec.ts" time="6.695">
<failure message="comprehensive-budget-ai.spec.ts:41:7 should display budget data and charts" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-budget-ai.spec.ts:41:7 › Comprehensive Budget Page Testing › should display budget data and charts 

    Error: expect.toBeVisible: Error: strict mode violation: locator('svg, canvas, .recharts-wrapper') resolved to 13 elements:
        1) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-inner-shadow-top !size-5">…</svg> aka getByRole('link', { name: 'NOLK' })
        2) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dashboard ">…</svg> aka getByRole('listitem').filter({ hasText: /^Dashboard$/ }).getByRole('link')
        3) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-list-details ">…</svg> aka getByRole('link', { name: 'Brand Deep Dive' })
        4) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-chart-bar ">…</svg> aka getByRole('link', { name: 'Marketing Dashboard' })
        5) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-report ">…</svg> aka getByRole('link', { name: 'Executive Summary' })
        6) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-wallet ">…</svg> aka getByRole('listitem').filter({ hasText: 'Budget' }).getByRole('link')
        7) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-file-ai ">…</svg> aka getByRole('link', { name: 'AI Assistant' })
        8) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-settings ">…</svg> aka getByRole('link', { name: 'Settings' })
        9) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dots-vertical ml-auto size-4">…</svg> aka getByRole('button', { name: 'F François Arbour farbour' })
        10) <svg width="24" height="24" fill="none" stroke-width="2" aria-hidden="true" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-panel-left" xmlns="http://www.w3.org/2000/svg">…</svg> aka getByRole('button', { name: 'Toggle Sidebar' })
        ...

    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for locator('svg, canvas, .recharts-wrapper')


       at utils/test-helpers.ts:386

      384 | export async function testChartRendering(page: Page, chartSelector = 'svg, canvas, .recharts-wrapper') {
      385 |   const chart = page.locator(chartSelector);
    > 386 |   await expect(chart).toBeVisible({ timeout: 10000 });
          |                       ^
      387 |
      388 |   // Wait for chart to fully render
      389 |   await page.waitForTimeout(2000);
        at testChartRendering (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:386:23)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:53:44

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-c20ee-play-budget-data-and-charts-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-c20ee-play-budget-data-and-charts-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-budget-ai-Co-c20ee-play-budget-data-and-charts-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[Budget table has 17 headers and 12 rows

[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-c20ee-play-budget-data-and-charts-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-c20ee-play-budget-data-and-charts-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-c20ee-play-budget-data-and-charts-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Budget Page Testing › should handle budget filters and brand selection" classname="comprehensive-budget-ai.spec.ts" time="5.966">
<system-out>
<![CDATA[Found 0 active filters
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Budget Page Testing › should test budget API endpoint" classname="comprehensive-budget-ai.spec.ts" time="5.435">
<system-out>
<![CDATA[Budget API response: 200
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Budget Page Testing › should be responsive on different screen sizes" classname="comprehensive-budget-ai.spec.ts" time="7.057">
<failure message="comprehensive-budget-ai.spec.ts:103:7 should be responsive on different screen sizes" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-budget-ai.spec.ts:103:7 › Comprehensive Budget Page Testing › should be responsive on different screen sizes 

    Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role="main"]') resolved to 2 elements:
        1) <main data-slot="sidebar-inset" class="bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2">…</main> aka getByText('Toggle SidebarDashboardBudgetBudget DataManage and view budget')
        2) <main class="flex-1 overflow-auto">…</main> aka getByRole('main').filter({ hasText: 'Toggle' }).getByRole('main')

    Call log:
      - expect.toBeVisible with timeout 15000ms
      - waiting for locator('main, [role="main"]')


      115 |       await page.waitForTimeout(1000);
      116 |       
    > 117 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
          |                                                         ^
      118 |       await takeScreenshot(page, `budget-responsive-${viewport.name}`);
      119 |     }
      120 |   });
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:117:57

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-84dd1-e-on-different-screen-sizes-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-84dd1-e-on-different-screen-sizes-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-budget-ai-Co-84dd1-e-on-different-screen-sizes-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-84dd1-e-on-different-screen-sizes-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-84dd1-e-on-different-screen-sizes-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-84dd1-e-on-different-screen-sizes-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should load AI assistant page with all components" classname="comprehensive-budget-ai.spec.ts" time="22.313">
<failure message="comprehensive-budget-ai.spec.ts:129:7 should load AI assistant page with all components" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-budget-ai.spec.ts:129:7 › Comprehensive AI Assistant Testing › should load AI assistant page with all components 

    Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)

    Locator: locator(':root')
    Expected pattern: /AI Assistant|NOLK/
    Received string:  "Create Next App"
    Call log:
      - expect.toHaveTitle with timeout 15000ms
      - waiting for locator(':root')
        19 × locator resolved to <html lang="en">…</html>
           - unexpected value "Create Next App"


      133 |
      134 |     // Verify page title and main elements
    > 135 |     await expect(page).toHaveTitle(/AI Assistant|NOLK/);
          |                        ^
      136 |     
      137 |     // Check for main components
      138 |     const expectedElements = [
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:135:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-dd46a-nt-page-with-all-components-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-dd46a-nt-page-with-all-components-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-budget-ai-Co-dd46a-nt-page-with-all-components-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-dd46a-nt-page-with-all-components-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-dd46a-nt-page-with-all-components-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-dd46a-nt-page-with-all-components-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should display chat interface" classname="comprehensive-budget-ai.spec.ts" time="6.776">
<system-out>
<![CDATA[Found 1 chat interface elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should handle message input and sending" classname="comprehensive-budget-ai.spec.ts" time="5.979">
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should display conversation history" classname="comprehensive-budget-ai.spec.ts" time="5.917">
<system-out>
<![CDATA[Found 0 messages in conversation history
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should handle AI assistant features" classname="comprehensive-budget-ai.spec.ts" time="5.387">
<system-out>
<![CDATA[Found 0 AI assistant features
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should test AI assistant API endpoint" classname="comprehensive-budget-ai.spec.ts" time="5.556">
<system-out>
<![CDATA[AI Assistant API failed: Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoBe[2m([22m[32mexpected[39m[2m) // Object.is equality[22m

Expected: [32m200[39m
Received: [31m405[39m
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should handle suggested questions or templates" classname="comprehensive-budget-ai.spec.ts" time="6.121">
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should be responsive on different screen sizes" classname="comprehensive-budget-ai.spec.ts" time="6.618">
<failure message="comprehensive-budget-ai.spec.ts:304:7 should be responsive on different screen sizes" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-budget-ai.spec.ts:304:7 › Comprehensive AI Assistant Testing › should be responsive on different screen sizes 

    Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role="main"]') resolved to 2 elements:
        1) <main data-slot="sidebar-inset" class="bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2">…</main> aka getByText('Toggle SidebarDashboardAi AssistantAI AssistantGet instant answers and insights')
        2) <main class="flex-1 overflow-auto">…</main> aka getByRole('main').filter({ hasText: 'Toggle SidebarDashboardAi' }).getByRole('main')

    Call log:
      - expect.toBeVisible with timeout 15000ms
      - waiting for locator('main, [role="main"]')


      316 |       await page.waitForTimeout(1000);
      317 |       
    > 318 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
          |                                                         ^
      319 |       
      320 |       // Check chat interface on different screens
      321 |       const chatContainer = page.locator('.chat, [data-testid*="chat"]');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:318:57

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-1b3a3-e-on-different-screen-sizes-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-1b3a3-e-on-different-screen-sizes-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-budget-ai-Co-1b3a3-e-on-different-screen-sizes-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-1b3a3-e-on-different-screen-sizes-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-1b3a3-e-on-different-screen-sizes-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-1b3a3-e-on-different-screen-sizes-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should have proper accessibility features" classname="comprehensive-budget-ai.spec.ts" time="6.026">
<failure message="comprehensive-budget-ai.spec.ts:330:7 should have proper accessibility features" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-budget-ai.spec.ts:330:7 › Comprehensive AI Assistant Testing › should have proper accessibility features 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      336 |     // Verify minimum accessibility requirements
      337 |     expect(accessibilityInfo.headings).toBeGreaterThan(0);
    > 338 |     expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
          |                                         ^
      339 |     
      340 |     console.log('AI Assistant accessibility info:', accessibilityInfo);
      341 |     await takeScreenshot(page, 'ai-assistant-accessibility');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:338:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-349b6-oper-accessibility-features-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-349b6-oper-accessibility-features-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-budget-ai-Co-349b6-oper-accessibility-features-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-349b6-oper-accessibility-features-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-349b6-oper-accessibility-features-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-349b6-oper-accessibility-features-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should load within performance thresholds" classname="comprehensive-budget-ai.spec.ts" time="6.124">
<system-out>
<![CDATA[AI Assistant loaded in 1194ms
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-dashboard.spec.ts" timestamp="2025-05-30T02:05:20.122Z" hostname="chromium" tests="10" failures="3" skipped="0" time="99.425" errors="0">
<testcase name="Comprehensive Dashboard Testing › should load dashboard with all core components" classname="comprehensive-dashboard.spec.ts" time="20.933">
<failure message="comprehensive-dashboard.spec.ts:22:7 should load dashboard with all core components" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-dashboard.spec.ts:22:7 › Comprehensive Dashboard Testing › should load dashboard with all core components 

    Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)

    Locator: locator(':root')
    Expected pattern: /Dashboard|NOLK/
    Received string:  "Create Next App"
    Call log:
      - expect.toHaveTitle with timeout 15000ms
      - waiting for locator(':root')
        19 × locator resolved to <html lang="en">…</html>
           - unexpected value "Create Next App"


      26 |
      27 |     // Verify page title and main elements
    > 28 |     await expect(page).toHaveTitle(/Dashboard|NOLK/);
         |                        ^
      29 |
      30 |     // Check for main dashboard components
      31 |     const mainContent = await getMainContent(page);
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts:28:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-dashboard-Co-72517-rd-with-all-core-components-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-dashboard-Co-72517-rd-with-all-core-components-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-dashboard-Co-72517-rd-with-all-core-components-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-dashboard-Co-72517-rd-with-all-core-components-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-dashboard-Co-72517-rd-with-all-core-components-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-dashboard-Co-72517-rd-with-all-core-components-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Dashboard Testing › should display and interact with KPI cards" classname="comprehensive-dashboard.spec.ts" time="6.423">
</testcase>
<testcase name="Comprehensive Dashboard Testing › should render charts correctly" classname="comprehensive-dashboard.spec.ts" time="7.29">
<failure message="comprehensive-dashboard.spec.ts:69:7 should render charts correctly" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-dashboard.spec.ts:69:7 › Comprehensive Dashboard Testing › should render charts correctly 

    Error: expect.toBeVisible: Error: strict mode violation: locator('svg, canvas, .recharts-wrapper') resolved to 72 elements:
        1) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-inner-shadow-top !size-5">…</svg> aka getByRole('link', { name: 'NOLK' })
        2) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dashboard ">…</svg> aka getByRole('listitem').filter({ hasText: /^Dashboard$/ }).getByRole('link')
        3) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-list-details ">…</svg> aka getByRole('link', { name: 'Brand Deep Dive' })
        4) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-chart-bar ">…</svg> aka getByRole('link', { name: 'Marketing Dashboard' })
        5) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-report ">…</svg> aka getByRole('link', { name: 'Executive Summary' })
        6) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-wallet ">…</svg> aka getByRole('link', { name: 'Budget' })
        7) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-file-ai ">…</svg> aka getByRole('link', { name: 'AI Assistant' })
        8) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-settings ">…</svg> aka getByRole('link', { name: 'Settings' })
        9) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dots-vertical ml-auto size-4">…</svg> aka getByRole('button', { name: 'F François Arbour farbour' })
        10) <svg width="24" height="24" fill="none" stroke-width="2" aria-hidden="true" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-panel-left" xmlns="http://www.w3.org/2000/svg">…</svg> aka getByRole('button', { name: 'Toggle Sidebar' })
        ...

    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for locator('svg, canvas, .recharts-wrapper')


       at utils/test-helpers.ts:386

      384 | export async function testChartRendering(page: Page, chartSelector = 'svg, canvas, .recharts-wrapper') {
      385 |   const chart = page.locator(chartSelector);
    > 386 |   await expect(chart).toBeVisible({ timeout: 10000 });
          |                       ^
      387 |
      388 |   // Wait for chart to fully render
      389 |   await page.waitForTimeout(2000);
        at testChartRendering (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:386:23)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts:75:44

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-dashboard-Co-07fd9-uld-render-charts-correctly-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-dashboard-Co-07fd9-uld-render-charts-correctly-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-dashboard-Co-07fd9-uld-render-charts-correctly-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-dashboard-Co-07fd9-uld-render-charts-correctly-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-dashboard-Co-07fd9-uld-render-charts-correctly-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-dashboard-Co-07fd9-uld-render-charts-correctly-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Dashboard Testing › should have functional filters" classname="comprehensive-dashboard.spec.ts" time="7.091">
<failure message="comprehensive-dashboard.spec.ts:79:7 should have functional filters" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-dashboard.spec.ts:79:7 › Comprehensive Dashboard Testing › should have functional filters 

    Error: locator.isVisible: Error: strict mode violation: locator('input[type="date"]') resolved to 2 elements:
        1) <input type="date" id="start-date" data-slot="input" value="2025-03-01" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 …/> aka getByRole('textbox', { name: 'Start Date' })
        2) <input type="date" id="end-date" data-slot="input" value="2025-05-30" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md…/> aka getByRole('textbox', { name: 'End Date' })

    Call log:
        - checking visibility of locator('input[type="date"]')


       at utils/test-helpers.ts:411

      409 |   for (const selector of filterElements) {
      410 |     const element = page.locator(selector);
    > 411 |     if (await element.isVisible()) {
          |                       ^
      412 |       activeFilters.push(selector);
      413 |     }
      414 |   }
        at testFilters (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:411:23)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts:85:27

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-dashboard-Co-4f9f8-uld-have-functional-filters-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-dashboard-Co-4f9f8-uld-have-functional-filters-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-dashboard-Co-4f9f8-uld-have-functional-filters-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-dashboard-Co-4f9f8-uld-have-functional-filters-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-dashboard-Co-4f9f8-uld-have-functional-filters-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-dashboard-Co-4f9f8-uld-have-functional-filters-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Dashboard Testing › should handle responsive design" classname="comprehensive-dashboard.spec.ts" time="9.985">
</testcase>
<testcase name="Comprehensive Dashboard Testing › should have proper accessibility features" classname="comprehensive-dashboard.spec.ts" time="6.882">
<system-out>
<![CDATA[⚠️ Dashboard: No landmarks found, but continuing test
Accessibility info: { ariaLabels: [33m21[39m, roles: [33m26[39m, headings: [33m3[39m, landmarks: [33m0[39m }
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Dashboard Testing › should handle navigation between dashboard sections" classname="comprehensive-dashboard.spec.ts" time="18.765">
</testcase>
<testcase name="Comprehensive Dashboard Testing › should handle data refresh and updates" classname="comprehensive-dashboard.spec.ts" time="7.609">
</testcase>
<testcase name="Comprehensive Dashboard Testing › should not have critical console errors" classname="comprehensive-dashboard.spec.ts" time="7.145">
<system-out>
<![CDATA[Critical console errors found: [
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'[39m,
  [32m'Error fetching sales channels: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3801:56)'[39m,
  [32m'Error fetching saved views: TypeError: Failed to fetch\n'[39m +
    [32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\n'[39m +
    [32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\n'[39m +
    [32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'[39m
]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Dashboard Testing › should load within performance thresholds" classname="comprehensive-dashboard.spec.ts" time="7.302">
<system-out>
<![CDATA[Dashboard loaded in 2213ms
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-error-handling.spec.ts" timestamp="2025-05-30T02:05:20.122Z" hostname="chromium" tests="12" failures="6" skipped="0" time="179.68" errors="0">
<testcase name="Comprehensive Error Handling and Edge Cases › should handle 404 errors gracefully" classname="comprehensive-error-handling.spec.ts" time="10.299">
<failure message="comprehensive-error-handling.spec.ts:15:7 should handle 404 errors gracefully" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-error-handling.spec.ts:15:7 › Comprehensive Error Handling and Edge Cases › should handle 404 errors gracefully 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      35 |       const isRedirect = !currentUrl.includes(url.split('/')[1]);
      36 |       
    > 37 |       expect(is404 || isRedirect).toBe(true);
         |                                   ^
      38 |       await takeScreenshot(page, `error-404-${url.replace(/\//g, '-')}`);
      39 |     }
      40 |   });
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:37:35

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[/nonexistent-page -> http://localhost:6699/nonexistent-page
/dashboard/invalid -> http://localhost:6699/dashboard/invalid
/admin/nonexistent -> http://localhost:6699/admin/nonexistent
/marketing-dashboard/invalid-campaign-id -> http://localhost:6699/marketing-dashboard/invalid-campaign-id

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle network errors and offline scenarios" classname="comprehensive-error-handling.spec.ts" time="6.81">
<failure message="comprehensive-error-handling.spec.ts:42:7 should handle network errors and offline scenarios" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-error-handling.spec.ts:42:7 › Comprehensive Error Handling and Edge Cases › should handle network errors and offline scenarios 

    Error: page.goto: net::ERR_INTERNET_DISCONNECTED at http://localhost:6699/marketing-dashboard
    Call log:
      - navigating to "http://localhost:6699/marketing-dashboard", waiting until "load"


      48 |     
      49 |     // Try to navigate to another page
    > 50 |     await page.goto('/marketing-dashboard');
         |                ^
      51 |     await page.waitForTimeout(3000);
      52 |     
      53 |     // Should show offline message or handle gracefully
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:50:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-7ef44-rrors-and-offline-scenarios-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-7ef44-rrors-and-offline-scenarios-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-error-handli-7ef44-rrors-and-offline-scenarios-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-7ef44-rrors-and-offline-scenarios-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-7ef44-rrors-and-offline-scenarios-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-7ef44-rrors-and-offline-scenarios-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle API errors gracefully" classname="comprehensive-error-handling.spec.ts" time="6.468">
<system-out>
<![CDATA[API error handling: Detected
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle authentication errors" classname="comprehensive-error-handling.spec.ts" time="6.596">
<failure message="comprehensive-error-handling.spec.ts:92:7 should handle authentication errors" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-error-handling.spec.ts:92:7 › Comprehensive Error Handling and Edge Cases › should handle authentication errors 

    Error: expect(received).toContain(expected) // indexOf

    Expected substring: "/auth/signin"
    Received string:    "http://localhost:6699/dashboard"

      110 |       // Should redirect to sign-in page
      111 |       const currentUrl = unauthenticatedPage.url();
    > 112 |       expect(currentUrl).toContain('/auth/signin');
          |                          ^
      113 |       
      114 |       console.log(`${url} -> redirected to sign-in`);
      115 |     }
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:112:26

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-2.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video-1.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle form validation errors" classname="comprehensive-error-handling.spec.ts" time="7.031">
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle large data sets without crashing" classname="comprehensive-error-handling.spec.ts" time="11.34">
<failure message="comprehensive-error-handling.spec.ts:154:7 should handle large data sets without crashing" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-error-handling.spec.ts:154:7 › Comprehensive Error Handling and Edge Cases › should handle large data sets without crashing 

    Error: locator.isVisible: Error: strict mode violation: locator('main') resolved to 2 elements:
        1) <main data-slot="sidebar-inset" class="bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2">…</main> aka getByText('Toggle SidebarDashboardMarketing DashboardMarketing DashboardTrack and analyze')
        2) <main class="flex-1 overflow-auto">…</main> aka getByRole('main').filter({ hasText: 'Toggle' }).getByRole('main')

    Call log:
        - checking visibility of locator('main')


      168 |       
      169 |       // Page should still be responsive
    > 170 |       const isResponsive = await page.locator('main').isVisible();
          |                                                       ^
      171 |       expect(isResponsive).toBe(true);
      172 |       
      173 |       console.log('Large dataset handling: Page remained responsive');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:170:55

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-72ce6--data-sets-without-crashing-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-72ce6--data-sets-without-crashing-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-error-handli-72ce6--data-sets-without-crashing-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-72ce6--data-sets-without-crashing-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-72ce6--data-sets-without-crashing-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-72ce6--data-sets-without-crashing-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle browser back/forward navigation" classname="comprehensive-error-handling.spec.ts" time="13.221">
<system-out>
<![CDATA[Browser navigation: Working correctly
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle page refresh and state preservation" classname="comprehensive-error-handling.spec.ts" time="8.212">
<failure message="comprehensive-error-handling.spec.ts:207:7 should handle page refresh and state preservation" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-error-handling.spec.ts:207:7 › Comprehensive Error Handling and Edge Cases › should handle page refresh and state preservation 

    Error: locator.isVisible: Error: strict mode violation: locator('main') resolved to 2 elements:
        1) <main data-slot="sidebar-inset" class="bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2">…</main> aka getByText('Toggle SidebarDashboardDashboardDashboardOverview of your business')
        2) <main class="flex-1 overflow-auto">…</main> aka getByRole('main').filter({ hasText: 'Toggle SidebarDashboardDashboardDashboardOverview of your business' }).getByRole('main')

    Call log:
        - checking visibility of locator('main')


      222 |     
      223 |     // Page should load successfully after refresh
    > 224 |     const isLoaded = await page.locator('main').isVisible();
          |                                                 ^
      225 |     expect(isLoaded).toBe(true);
      226 |     
      227 |     console.log('Page refresh: Handled successfully');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:224:49

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-a4ae1-resh-and-state-preservation-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-a4ae1-resh-and-state-preservation-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-error-handli-a4ae1-resh-and-state-preservation-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-a4ae1-resh-and-state-preservation-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-a4ae1-resh-and-state-preservation-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-a4ae1-resh-and-state-preservation-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle concurrent user actions" classname="comprehensive-error-handling.spec.ts" time="60.097">
<failure message="comprehensive-error-handling.spec.ts:231:7 should handle concurrent user actions" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-error-handling.spec.ts:231:7 › Comprehensive Error Handling and Edge Cases › should handle concurrent user actions 

    Test timeout of 60000ms exceeded.

    Error: page.waitForTimeout: Target page, context or browser has been closed

      247 |     await Promise.all(promises);
      248 |     
    > 249 |     await page.waitForTimeout(2000);
          |                ^
      250 |     await waitForLoadingToComplete(page);
      251 |     
      252 |     // Page should still be functional
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:249:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle memory leaks during navigation" classname="comprehensive-error-handling.spec.ts" time="31.06">
<system-out>
<![CDATA[Initial memory: 40.15MB
Final memory: 40.15MB
Memory increase: 0.00MB
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle edge cases in data filtering" classname="comprehensive-error-handling.spec.ts" time="10.659">
<system-out>
<![CDATA[Invalid date range handling: Detected
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle session timeout gracefully" classname="comprehensive-error-handling.spec.ts" time="7.887">
<system-out>
<![CDATA[Session timeout: Redirected to sign-in
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-executive-summary.spec.ts" timestamp="2025-05-30T02:05:20.122Z" hostname="chromium" tests="13" failures="5" skipped="0" time="100.956" errors="0">
<testcase name="Comprehensive Executive Summary Testing › should load executive summary with all components" classname="comprehensive-executive-summary.spec.ts" time="22.032">
<failure message="comprehensive-executive-summary.spec.ts:20:7 should load executive summary with all components" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-executive-summary.spec.ts:20:7 › Comprehensive Executive Summary Testing › should load executive summary with all components 

    Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)

    Locator: locator(':root')
    Expected pattern: /Executive Summary|NOLK/
    Received string:  "Create Next App"
    Call log:
      - expect.toHaveTitle with timeout 15000ms
      - waiting for locator(':root')
        19 × locator resolved to <html lang="en">…</html>
           - unexpected value "Create Next App"


      24 |
      25 |     // Verify page title and main elements
    > 26 |     await expect(page).toHaveTitle(/Executive Summary|NOLK/);
         |                        ^
      27 |     
      28 |     // Check for main components
      29 |     const expectedElements = [
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:26:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-executive-su-c57a0-summary-with-all-components-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-executive-su-c57a0-summary-with-all-components-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-executive-su-c57a0-summary-with-all-components-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-c57a0-summary-with-all-components-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-c57a0-summary-with-all-components-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-c57a0-summary-with-all-components-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should display brand selector and period selector" classname="comprehensive-executive-summary.spec.ts" time="6.38">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should display executive KPI cards with proper formatting" classname="comprehensive-executive-summary.spec.ts" time="6.826">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should switch between cards and slides view" classname="comprehensive-executive-summary.spec.ts" time="7.179">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should display trend charts and analysis" classname="comprehensive-executive-summary.spec.ts" time="6.074">
<failure message="comprehensive-executive-summary.spec.ts:156:7 should display trend charts and analysis" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-executive-summary.spec.ts:156:7 › Comprehensive Executive Summary Testing › should display trend charts and analysis 

    Error: expect.toBeVisible: Error: strict mode violation: locator('svg, canvas, .recharts-wrapper') resolved to 37 elements:
        1) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-inner-shadow-top !size-5">…</svg> aka getByRole('link', { name: 'NOLK' })
        2) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dashboard ">…</svg> aka getByRole('listitem').filter({ hasText: /^Dashboard$/ }).getByRole('link')
        3) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-list-details ">…</svg> aka getByRole('link', { name: 'Brand Deep Dive' })
        4) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-chart-bar ">…</svg> aka getByRole('link', { name: 'Marketing Dashboard' })
        5) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-report ">…</svg> aka getByRole('listitem').filter({ hasText: 'Executive Summary' }).getByRole('link')
        6) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-wallet ">…</svg> aka getByRole('link', { name: 'Budget' })
        7) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-file-ai ">…</svg> aka getByRole('link', { name: 'AI Assistant' })
        8) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-settings ">…</svg> aka getByRole('link', { name: 'Settings' })
        9) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dots-vertical ml-auto size-4">…</svg> aka getByRole('button', { name: 'F François Arbour farbour' })
        10) <svg width="24" height="24" fill="none" stroke-width="2" aria-hidden="true" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-panel-left" xmlns="http://www.w3.org/2000/svg">…</svg> aka getByRole('button', { name: 'Toggle Sidebar' })
        ...

    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for locator('svg, canvas, .recharts-wrapper')


       at utils/test-helpers.ts:386

      384 | export async function testChartRendering(page: Page, chartSelector = 'svg, canvas, .recharts-wrapper') {
      385 |   const chart = page.locator(chartSelector);
    > 386 |   await expect(chart).toBeVisible({ timeout: 10000 });
          |                       ^
      387 |
      388 |   // Wait for chart to fully render
      389 |   await page.waitForTimeout(2000);
        at testChartRendering (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:386:23)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:162:44

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-executive-su-d4c94-y-trend-charts-and-analysis-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-executive-su-d4c94-y-trend-charts-and-analysis-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-executive-su-d4c94-y-trend-charts-and-analysis-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-d4c94-y-trend-charts-and-analysis-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-d4c94-y-trend-charts-and-analysis-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-d4c94-y-trend-charts-and-analysis-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should handle PDF export functionality" classname="comprehensive-executive-summary.spec.ts" time="8.014">
<failure message="comprehensive-executive-summary.spec.ts:176:7 should handle PDF export functionality" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-executive-summary.spec.ts:176:7 › Comprehensive Executive Summary Testing › should handle PDF export functionality 

    Error: locator.isVisible: Unexpected token "=" while parsing css selector "[data-testid*="progress"], .progress, text=/generating|exporting/i". Did you mean to CSS.escape it?
    Call log:
        - checking visibility of [data-testid*="progress"], .progress, text=/generating|exporting/i


      192 |       // Look for export progress or confirmation
      193 |       const exportProgress = page.locator('[data-testid*="progress"], .progress, text=/generating|exporting/i');
    > 194 |       if (await exportProgress.isVisible()) {
          |                                ^
      195 |         console.log('PDF export process initiated');
      196 |         await takeScreenshot(page, 'executive-summary-pdf-export-progress');
      197 |       }
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:194:32

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-executive-su-16129-le-PDF-export-functionality-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-executive-su-16129-le-PDF-export-functionality-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-executive-su-16129-le-PDF-export-functionality-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-16129-le-PDF-export-functionality-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-16129-le-PDF-export-functionality-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-16129-le-PDF-export-functionality-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should display period comparison data" classname="comprehensive-executive-summary.spec.ts" time="6.188">
<system-out>
<![CDATA[Found 10 comparison elements and 0 comparison values
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should handle currency switching" classname="comprehensive-executive-summary.spec.ts" time="5.899">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should display methodology and notes if available" classname="comprehensive-executive-summary.spec.ts" time="6.237">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should be responsive across different screen sizes" classname="comprehensive-executive-summary.spec.ts" time="7.515">
<failure message="comprehensive-executive-summary.spec.ts:280:7 should be responsive across different screen sizes" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-executive-summary.spec.ts:280:7 › Comprehensive Executive Summary Testing › should be responsive across different screen sizes 

    Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role="main"]') resolved to 7 elements:
        1) <main data-slot="sidebar-inset" class="bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2">…</main> aka getByText('Toggle SidebarDashboardExecutive SummaryExecutive SummaryPerformance highlights')
        2) <main class="flex-1 overflow-auto">…</main> aka getByRole('main').filter({ hasText: 'Executive SummaryPerformance' }).nth(1)
        3) <main aria-label="KPI Content" class="flex-1 min-h-0 h-full">…</main> aka getByRole('article', { name: 'Slide 1: Total Revenue' }).getByLabel('KPI Content')
        4) <main aria-label="KPI Content" class="flex-1 min-h-0 h-full">…</main> aka getByRole('article', { name: 'Slide 2: Net Revenue' }).getByLabel('KPI Content')
        5) <main aria-label="KPI Content" class="flex-1 min-h-0 h-full">…</main> aka getByRole('article', { name: 'Slide 3: Gross Profit' }).getByLabel('KPI Content')
        6) <main aria-label="KPI Content" class="flex-1 min-h-0 h-full">…</main> aka getByRole('article', { name: 'Slide 4: Contribution Margin' }).getByLabel('KPI Content')
        7) <main aria-label="KPI Content" class="flex-1 min-h-0 h-full">…</main> aka getByRole('article', { name: 'Slide 5: Adspend' }).getByLabel('KPI Content')

    Call log:
      - expect.toBeVisible with timeout 15000ms
      - waiting for locator('main, [role="main"]')


      293 |       
      294 |       // Verify main elements are still visible
    > 295 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
          |                                                         ^
      296 |       
      297 |       // Check KPI cards layout on different screens
      298 |       const kpiCards = page.locator('[data-testid*="kpi"], .kpi-card');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:295:57

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-executive-su-1b50d-ross-different-screen-sizes-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-executive-su-1b50d-ross-different-screen-sizes-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-executive-su-1b50d-ross-different-screen-sizes-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-1b50d-ross-different-screen-sizes-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-1b50d-ross-different-screen-sizes-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-1b50d-ross-different-screen-sizes-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should have proper accessibility features" classname="comprehensive-executive-summary.spec.ts" time="5.853">
<failure message="comprehensive-executive-summary.spec.ts:310:7 should have proper accessibility features" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-executive-summary.spec.ts:310:7 › Comprehensive Executive Summary Testing › should have proper accessibility features 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      316 |     // Verify minimum accessibility requirements
      317 |     expect(accessibilityInfo.headings).toBeGreaterThan(0);
    > 318 |     expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
          |                                         ^
      319 |     
      320 |     console.log('Executive Summary accessibility info:', accessibilityInfo);
      321 |     await takeScreenshot(page, 'executive-summary-accessibility');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:318:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-executive-su-f681c-oper-accessibility-features-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-executive-su-f681c-oper-accessibility-features-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-executive-su-f681c-oper-accessibility-features-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-f681c-oper-accessibility-features-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-f681c-oper-accessibility-features-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-f681c-oper-accessibility-features-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should load within performance thresholds" classname="comprehensive-executive-summary.spec.ts" time="6.573">
<system-out>
<![CDATA[Executive Summary loaded in 1824ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should not have critical console errors" classname="comprehensive-executive-summary.spec.ts" time="6.186">
<system-out>
<![CDATA[Critical console errors found: [
  [32m'Error fetching saved views: TypeError: Failed to fetch\n'[39m +
    [32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\n'[39m +
    [32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\n'[39m +
    [32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'[39m,
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'[39m,
  [32m'Error fetching sales channels: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3801:56)'[39m
]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-marketing-dashboard.spec.ts" timestamp="2025-05-30T02:05:20.122Z" hostname="chromium" tests="13" failures="7" skipped="0" time="99.2" errors="0">
<testcase name="Comprehensive Marketing Dashboard Testing › should load marketing dashboard with all components" classname="comprehensive-marketing-dashboard.spec.ts" time="20.81">
<failure message="comprehensive-marketing-dashboard.spec.ts:22:7 should load marketing dashboard with all components" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-marketing-dashboard.spec.ts:22:7 › Comprehensive Marketing Dashboard Testing › should load marketing dashboard with all components 

    Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)

    Locator: locator(':root')
    Expected pattern: /Marketing Dashboard|NOLK/
    Received string:  "Create Next App"
    Call log:
      - expect.toHaveTitle with timeout 15000ms
      - waiting for locator(':root')
        19 × locator resolved to <html lang="en">…</html>
           - unexpected value "Create Next App"


      26 |
      27 |     // Verify page title and main elements
    > 28 |     await expect(page).toHaveTitle(/Marketing Dashboard|NOLK/);
         |                        ^
      29 |     
      30 |     // Check for main components
      31 |     const expectedElements = [
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts:28:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-44c11-shboard-with-all-components-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-44c11-shboard-with-all-components-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-marketing-da-44c11-shboard-with-all-components-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-44c11-shboard-with-all-components-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-44c11-shboard-with-all-components-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-44c11-shboard-with-all-components-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should display marketing KPI cards" classname="comprehensive-marketing-dashboard.spec.ts" time="6.982">
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should display campaign data table with proper functionality" classname="comprehensive-marketing-dashboard.spec.ts" time="6.788">
<system-out>
<![CDATA[Campaign table has 12 headers and 50 rows
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should render spend breakdown charts" classname="comprehensive-marketing-dashboard.spec.ts" time="6.965">
<failure message="comprehensive-marketing-dashboard.spec.ts:94:7 should render spend breakdown charts" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-marketing-dashboard.spec.ts:94:7 › Comprehensive Marketing Dashboard Testing › should render spend breakdown charts 

    Error: expect.toBeVisible: Error: strict mode violation: locator('svg, canvas, .recharts-wrapper') resolved to 56 elements:
        1) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-inner-shadow-top !size-5">…</svg> aka getByRole('link', { name: 'NOLK' })
        2) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dashboard ">…</svg> aka getByRole('listitem').filter({ hasText: /^Dashboard$/ }).getByRole('link')
        3) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-list-details ">…</svg> aka getByRole('link', { name: 'Brand Deep Dive' })
        4) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-chart-bar ">…</svg> aka getByRole('listitem').filter({ hasText: 'Marketing Dashboard' }).getByRole('link')
        5) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-report ">…</svg> aka getByRole('link', { name: 'Executive Summary' })
        6) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-wallet ">…</svg> aka getByRole('link', { name: 'Budget' })
        7) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-file-ai ">…</svg> aka getByRole('link', { name: 'AI Assistant' })
        8) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-settings ">…</svg> aka getByRole('link', { name: 'Settings' })
        9) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dots-vertical ml-auto size-4">…</svg> aka getByRole('button', { name: 'F François Arbour farbour' })
        10) <svg width="24" height="24" fill="none" stroke-width="2" aria-hidden="true" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-panel-left" xmlns="http://www.w3.org/2000/svg">…</svg> aka getByRole('button', { name: 'Toggle Sidebar' })
        ...

    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for locator('svg, canvas, .recharts-wrapper')


       at utils/test-helpers.ts:386

      384 | export async function testChartRendering(page: Page, chartSelector = 'svg, canvas, .recharts-wrapper') {
      385 |   const chart = page.locator(chartSelector);
    > 386 |   await expect(chart).toBeVisible({ timeout: 10000 });
          |                       ^
      387 |
      388 |   // Wait for chart to fully render
      389 |   await page.waitForTimeout(2000);
        at testChartRendering (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:386:23)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts:100:44

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-80b02-nder-spend-breakdown-charts-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-80b02-nder-spend-breakdown-charts-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-marketing-da-80b02-nder-spend-breakdown-charts-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-80b02-nder-spend-breakdown-charts-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-80b02-nder-spend-breakdown-charts-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-80b02-nder-spend-breakdown-charts-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should have functional marketing filters" classname="comprehensive-marketing-dashboard.spec.ts" time="6.504">
<failure message="comprehensive-marketing-dashboard.spec.ts:110:7 should have functional marketing filters" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-marketing-dashboard.spec.ts:110:7 › Comprehensive Marketing Dashboard Testing › should have functional marketing filters 

    Error: locator.isVisible: Error: strict mode violation: locator('input[type="date"]') resolved to 2 elements:
        1) <input type="date" id="start-date" data-slot="input" value="2025-03-01" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 …/> aka getByRole('textbox', { name: 'Start Date' })
        2) <input type="date" id="end-date" data-slot="input" value="2025-05-30" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md…/> aka getByRole('textbox', { name: 'End Date' })

    Call log:
        - checking visibility of locator('input[type="date"]')


       at utils/test-helpers.ts:411

      409 |   for (const selector of filterElements) {
      410 |     const element = page.locator(selector);
    > 411 |     if (await element.isVisible()) {
          |                       ^
      412 |       activeFilters.push(selector);
      413 |     }
      414 |   }
        at testFilters (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:411:23)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts:116:27

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-7a624-unctional-marketing-filters-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-7a624-unctional-marketing-filters-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-marketing-da-7a624-unctional-marketing-filters-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-7a624-unctional-marketing-filters-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-7a624-unctional-marketing-filters-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-7a624-unctional-marketing-filters-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should allow campaign search and filtering" classname="comprehensive-marketing-dashboard.spec.ts" time="6.361">
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should navigate to campaign details" classname="comprehensive-marketing-dashboard.spec.ts" time="6.301">
<failure message="comprehensive-marketing-dashboard.spec.ts:190:7 should navigate to campaign details" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-marketing-dashboard.spec.ts:190:7 › Comprehensive Marketing Dashboard Testing › should navigate to campaign details 

    Error: expect(received).toContain(expected) // indexOf

    Expected substring: "/marketing-dashboard/"
    Received string:    "http://localhost:6699/marketing-dashboard"

      209 |         
      210 |         // Verify we're on a campaign detail page
    > 211 |         expect(page.url()).toContain('/marketing-dashboard/');
          |                            ^
      212 |         await takeScreenshot(page, 'campaign-detail-page');
      213 |         
      214 |         // Navigate back
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts:211:28

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-40c94-avigate-to-campaign-details-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-40c94-avigate-to-campaign-details-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-marketing-da-40c94-avigate-to-campaign-details-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-40c94-avigate-to-campaign-details-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-40c94-avigate-to-campaign-details-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-40c94-avigate-to-campaign-details-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should display proper marketing metrics calculations" classname="comprehensive-marketing-dashboard.spec.ts" time="6.179">
<failure message="comprehensive-marketing-dashboard.spec.ts:221:7 should display proper marketing metrics calculations" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-marketing-dashboard.spec.ts:221:7 › Comprehensive Marketing Dashboard Testing › should display proper marketing metrics calculations 

    Error: locator.isVisible: Error: strict mode violation: locator('text=ROAS') resolved to 2 elements:
        1) <div data-slot="card-title" class="text-sm font-medium">Overall ROAS</div> aka getByText('Overall ROAS')
        2) <div title="Sort descending" class="flex cursor-pointer select-none items-center">ROAS</div> aka getByText('ROAS', { exact: true })

    Call log:
        - checking visibility of locator('text=ROAS')


      233 |     for (const metric of marketingMetrics) {
      234 |       const metricElement = page.locator(`text=${metric}`);
    > 235 |       if (await metricElement.isVisible()) {
          |                               ^
      236 |         foundMetrics++;
      237 |       }
      238 |     }
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts:235:31

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-c3fa8-keting-metrics-calculations-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-c3fa8-keting-metrics-calculations-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-marketing-da-c3fa8-keting-metrics-calculations-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-c3fa8-keting-metrics-calculations-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-c3fa8-keting-metrics-calculations-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-c3fa8-keting-metrics-calculations-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should handle data export functionality" classname="comprehensive-marketing-dashboard.spec.ts" time="6.052">
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should be responsive across different screen sizes" classname="comprehensive-marketing-dashboard.spec.ts" time="7.553">
<failure message="comprehensive-marketing-dashboard.spec.ts:273:7 should be responsive across different screen sizes" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-marketing-dashboard.spec.ts:273:7 › Comprehensive Marketing Dashboard Testing › should be responsive across different screen sizes 

    Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role="main"]') resolved to 2 elements:
        1) <main data-slot="sidebar-inset" class="bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2">…</main> aka getByText('Toggle SidebarDashboardMarketing DashboardMarketing DashboardTrack and analyze')
        2) <main class="flex-1 overflow-auto">…</main> aka getByRole('main').filter({ hasText: 'Toggle' }).getByRole('main')

    Call log:
      - expect.toBeVisible with timeout 15000ms
      - waiting for locator('main, [role="main"]')


      286 |       
      287 |       // Verify main elements are still visible
    > 288 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
          |                                                         ^
      289 |       
      290 |       // Check table responsiveness
      291 |       const table = page.locator('table');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts:288:57

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-de393-ross-different-screen-sizes-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-de393-ross-different-screen-sizes-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-marketing-da-de393-ross-different-screen-sizes-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-de393-ross-different-screen-sizes-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-de393-ross-different-screen-sizes-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-de393-ross-different-screen-sizes-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should have proper accessibility features" classname="comprehensive-marketing-dashboard.spec.ts" time="6.428">
<failure message="comprehensive-marketing-dashboard.spec.ts:304:7 should have proper accessibility features" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-marketing-dashboard.spec.ts:304:7 › Comprehensive Marketing Dashboard Testing › should have proper accessibility features 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      310 |     // Verify minimum accessibility requirements
      311 |     expect(accessibilityInfo.headings).toBeGreaterThan(0);
    > 312 |     expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
          |                                         ^
      313 |     
      314 |     // Check table accessibility
      315 |     const tables = page.locator('table');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts:312:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-1e0d4-oper-accessibility-features-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-1e0d4-oper-accessibility-features-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-marketing-da-1e0d4-oper-accessibility-features-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-1e0d4-oper-accessibility-features-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-1e0d4-oper-accessibility-features-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-1e0d4-oper-accessibility-features-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should load within performance thresholds" classname="comprehensive-marketing-dashboard.spec.ts" time="6.268">
<system-out>
<![CDATA[Marketing Dashboard loaded in 1133ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should not have critical console errors" classname="comprehensive-marketing-dashboard.spec.ts" time="6.009">
<system-out>
<![CDATA[Critical console errors found: [
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9578:33)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9581:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)'[39m,
  [32m'Error fetching saved views: TypeError: Failed to fetch\n'[39m +
    [32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\n'[39m +
    [32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\n'[39m +
    [32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9578:33)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)'[39m,
  [32m'Error fetching saved views: TypeError: Failed to fetch\n'[39m +
    [32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\n'[39m +
    [32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\n'[39m +
    [32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'[39m,
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'[39m
]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-performance.spec.ts" timestamp="2025-05-30T02:05:20.122Z" hostname="chromium" tests="11" failures="1" skipped="0" time="123.184" errors="0">
<testcase name="Comprehensive Performance Testing › should measure page load performance across all main pages" classname="comprehensive-performance.spec.ts" time="12.208">
<system-out>
<![CDATA[Dashboard: 1223ms (max: 8000ms)
Brand Deep Dive: 1252ms (max: 12000ms)
Marketing Dashboard: 1098ms (max: 15000ms)
Executive Summary: 1035ms (max: 10000ms)
Budget: 917ms (max: 8000ms)
AI Assistant: 945ms (max: 6000ms)
Admin Dashboard: 817ms (max: 8000ms)

=== Performance Summary ===
✅ PASS Dashboard: 1223ms
✅ PASS Brand Deep Dive: 1252ms
✅ PASS Marketing Dashboard: 1098ms
✅ PASS Executive Summary: 1035ms
✅ PASS Budget: 917ms
✅ PASS AI Assistant: 945ms
✅ PASS Admin Dashboard: 817ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should measure Time to First Contentful Paint (FCP)" classname="comprehensive-performance.spec.ts" time="20.502">
</testcase>
<testcase name="Comprehensive Performance Testing › should measure Largest Contentful Paint (LCP)" classname="comprehensive-performance.spec.ts" time="27.569">
</testcase>
<testcase name="Comprehensive Performance Testing › should measure JavaScript bundle sizes" classname="comprehensive-performance.spec.ts" time="6.691">
<system-out>
<![CDATA[JS Bundle: %5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_fd44f5a4._.js - 1.01KB
JS Bundle: _e69f0d32._.js - 0.81KB
JS Bundle: node_modules_%40swc_helpers_cjs_00636ac3._.js - 1.25KB
JS Bundle: node_modules_next_dist_2ecbf5fa._.js - 17.73KB
JS Bundle: app_favicon_ico_mjs_659ce808._.js - 0.55KB
JS Bundle: _93808211._.js - 16.07KB
JS Bundle: node_modules_next_dist_1a6ee436._.js - 18.44KB
JS Bundle: _45166852._.js - 17.95KB
JS Bundle: node_modules_next_dist_compiled_2ce9398a._.js - 192.75KB
JS Bundle: node_modules_next_dist_client_8f19e6fb._.js - 166.58KB
JS Bundle: app_layout_tsx_c0237562._.js - 0.58KB
JS Bundle: node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js - 17.12KB
JS Bundle: node_modules_lodash_f240f67a._.js - 32.65KB
JS Bundle: _5e541529._.js - 70.10KB
JS Bundle: node_modules_recharts_es6_98d167ba._.js - 117.77KB
JS Bundle: node_modules_%40floating-ui_9ec1fa39._.js - 21.34KB
JS Bundle: node_modules_%40radix-ui_fead58fd._.js - 55.73KB
JS Bundle: node_modules_53dbc141._.js - 142.33KB
JS Bundle: app_dashboard_page_tsx_63d9a548._.js - 0.87KB
JS Bundle: %5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js - 4.05KB
JS Bundle: %5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_66796270._.js - 0.57KB
Total JS Bundle Size: 896.22KB
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test memory usage during navigation" classname="comprehensive-performance.spec.ts" time="9.956">
<system-out>
<![CDATA[/dashboard Memory: 31.57MB used
/brand-deep-dive Memory: 31.57MB used
/marketing-dashboard Memory: 31.57MB used
/executive-summary Memory: 31.57MB used

=== Memory Usage Summary ===
/dashboard: 31.57MB used, 110.63MB total
/brand-deep-dive: 31.57MB used, 110.63MB total
/marketing-dashboard: 31.57MB used, 110.63MB total
/executive-summary: 31.57MB used, 110.63MB total
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test network performance and resource loading" classname="comprehensive-performance.spec.ts" time="6.853">
<system-out>
<![CDATA[Network Performance:
Domain Lookup: 0.00ms
Connection: 0.00ms
Request: 157.30ms
Response: 8.80ms
DOM Processing: 20.30ms
Total Resources: 36
Slow Resources (>1s): 0
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test performance under different network conditions" classname="comprehensive-performance.spec.ts" time="7.628">
<system-out>
<![CDATA[Dashboard load time with slow network: 2256ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test performance with large datasets" classname="comprehensive-performance.spec.ts" time="12.843">
<system-out>
<![CDATA[/marketing-dashboard with data rendering: 4054ms
/brand-deep-dive with data rendering: 4161ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test scroll performance on long pages" classname="comprehensive-performance.spec.ts" time="6.736">
<system-out>
<![CDATA[Scroll performance: 1011ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test chart rendering performance" classname="comprehensive-performance.spec.ts" time="6.123">
</testcase>
<testcase name="Comprehensive Performance Testing › should test accessibility performance" classname="comprehensive-performance.spec.ts" time="6.075">
<failure message="comprehensive-performance.spec.ts:341:7 should test accessibility performance" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-performance.spec.ts:341:7 › Comprehensive Performance Testing › should test accessibility performance 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      359 |       // Verify minimum accessibility requirements
      360 |       expect(accessibilityInfo.headings).toBeGreaterThan(0);
    > 361 |       expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
          |                                           ^
      362 |     }
      363 |   });
      364 | });
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-performance.spec.ts:361:43

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-performance--11fe7-t-accessibility-performance-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-performance--11fe7-t-accessibility-performance-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-performance--11fe7-t-accessibility-performance-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[/dashboard accessibility scan: 1170ms
Accessibility elements found: { ariaLabels: [33m21[39m, roles: [33m26[39m, headings: [33m3[39m, landmarks: [33m0[39m }

[[ATTACHMENT|e2e-artifacts/comprehensive-performance--11fe7-t-accessibility-performance-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-performance--11fe7-t-accessibility-performance-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-performance--11fe7-t-accessibility-performance-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-test-runner.spec.ts" timestamp="2025-05-30T02:05:20.122Z" hostname="chromium" tests="8" failures="1" skipped="0" time="98.099" errors="0">
<testcase name="Comprehensive Application Test Suite › should run complete application smoke test" classname="comprehensive-test-runner.spec.ts" time="16.446">
<system-out>
<![CDATA[🚀 Starting comprehensive application smoke test...
📄 Testing Dashboard...
✅ Dashboard: 1406ms
📄 Testing Brand Deep Dive...
✅ Brand Deep Dive: 983ms
📄 Testing Marketing Dashboard...
✅ Marketing Dashboard: 1058ms
📄 Testing Executive Summary...
✅ Executive Summary: 1170ms
📄 Testing Budget...
✅ Budget: 932ms
📄 Testing AI Assistant...
✅ AI Assistant: 1637ms
🔐 Testing admin pages...
✅ Admin Dashboard: Accessible
✅ Admin Users: Accessible
✅ Admin Roles: Accessible
✅ Admin Permissions: Accessible

📊 SMOKE TEST SUMMARY:
========================
✅ Dashboard: 1406ms
✅ Brand Deep Dive: 983ms
✅ Marketing Dashboard: 1058ms
✅ Executive Summary: 1170ms
✅ Budget: 932ms
✅ AI Assistant: 1637ms

🎯 Results: 6/6 tests passed
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify authentication system works correctly" classname="comprehensive-test-runner.spec.ts" time="19.362">
<failure message="comprehensive-test-runner.spec.ts:122:7 should verify authentication system works correctly" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-test-runner.spec.ts:122:7 › Comprehensive Application Test Suite › should verify authentication system works correctly 

    TimeoutError: locator.waitFor: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible


       at utils/test-helpers.ts:46

      44 |
      45 |     // Wait for the button to be visible
    > 46 |     await credentialsTab.waitFor({ state: 'visible', timeout: 10000 });
         |                          ^
      47 |     await credentialsTab.click();
      48 |     await page.waitForTimeout(1500); // Wait for tab switch animation
      49 |
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Testing authentication system...
Sign in failed: locator.waitFor: Timeout 10000ms exceeded.
Call log:
[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible[22m

    at signInWithCredentials [90m(/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/utils/test-helpers.ts:46:26[90m)[39m
    at signInAsAdmin [90m(/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/utils/test-helpers.ts:75:3[90m)[39m
    at [90m/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/comprehensive-test-runner.spec.ts:127:7 {
  name: [32m'TimeoutError'[39m,
  [[32mSymbol(step)[39m]: {
    stepId: [32m'pw:api@25'[39m,
    location: {
      file: [32m'/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts'[39m,
      line: [33m46[39m,
      column: [33m26[39m,
      function: [32m'signInWithCredentials'[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m'locator.waitFor(button >> internal:has-text="Username/Password"i)'[39m,
    apiName: [32m'locator.waitFor'[39m,
    params: {
      selector: [32m'button >> internal:has-text="Username/Password"i'[39m,
      strict: [33mtrue[39m,
      omitReturnValue: [33mtrue[39m,
      state: [32m'visible'[39m,
      timeout: [33m10000[39m
    },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@25'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1748570873580[39m,
    error: {
      message: [32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\x1B[22m\n"[39m,
      stack: [32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\x1B[22m\n"[39m +
        [32m'\n'[39m +
        [32m'    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)\n'[39m +
        [32m'    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)\n'[39m +
        [32m'    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7'[39m,
      cause: [90mundefined[39m
    }
  }
}
❌ Authentication test failed: locator.waitFor: Timeout 10000ms exceeded.
Call log:
[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible[22m

    at signInWithCredentials [90m(/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/utils/test-helpers.ts:46:26[90m)[39m
    at signInAsAdmin [90m(/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/utils/test-helpers.ts:75:3[90m)[39m
    at [90m/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/comprehensive-test-runner.spec.ts:127:7 {
  name: [32m'TimeoutError'[39m,
  [[32mSymbol(step)[39m]: {
    stepId: [32m'pw:api@25'[39m,
    location: {
      file: [32m'/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts'[39m,
      line: [33m46[39m,
      column: [33m26[39m,
      function: [32m'signInWithCredentials'[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m'locator.waitFor(button >> internal:has-text="Username/Password"i)'[39m,
    apiName: [32m'locator.waitFor'[39m,
    params: {
      selector: [32m'button >> internal:has-text="Username/Password"i'[39m,
      strict: [33mtrue[39m,
      omitReturnValue: [33mtrue[39m,
      state: [32m'visible'[39m,
      timeout: [33m10000[39m
    },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@25'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1748570873580[39m,
    error: {
      message: [32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\x1B[22m\n"[39m,
      stack: [32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\x1B[22m\n"[39m +
        [32m'\n'[39m +
        [32m'    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)\n'[39m +
        [32m'    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)\n'[39m +
        [32m'    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7'[39m,
      cause: [90mundefined[39m
    }
  }
}

[[ATTACHMENT|e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify all navigation links work" classname="comprehensive-test-runner.spec.ts" time="12">
<system-out>
<![CDATA[🧭 Testing navigation system...
✅ Navigation to Dashboard: Working
✅ Navigation to Marketing Dashboard: Working
✅ Navigation to Executive Summary: Working
✅ Navigation to Budget: Working
✅ Navigation to AI Assistant: Working
🎯 Navigation: 5/6 links working
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify data loading and display" classname="comprehensive-test-runner.spec.ts" time="10.43">
<system-out>
<![CDATA[📊 Testing data loading and display...
📈 Dashboard KPIs: 0 data elements found
⚠️ Dashboard KPIs: No data elements found
📈 Marketing Campaigns: 1 data elements found
✅ Marketing Campaigns: Data loaded successfully
📈 Brand Data: 0 data elements found
⚠️ Brand Data: No data elements found
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify responsive design works" classname="comprehensive-test-runner.spec.ts" time="10.388">
<system-out>
<![CDATA[📱 Testing responsive design...
✅ Desktop (1920x1080): Layout working
✅ Tablet (1024x768): Layout working
✅ Mobile (375x667): Layout working
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify accessibility standards" classname="comprehensive-test-runner.spec.ts" time="9.61">
<system-out>
<![CDATA[♿ Testing accessibility standards...
🔍 /dashboard accessibility:
  - Headings: 3
  - ARIA labels: 21
  - Landmarks: 0
⚠️ /dashboard: No landmarks found, but continuing test
✅ /dashboard: Accessibility standards met
🔍 /marketing-dashboard accessibility:
  - Headings: 4
  - ARIA labels: 1
  - Landmarks: 0
⚠️ /marketing-dashboard: No landmarks found, but continuing test
✅ /marketing-dashboard: Accessibility standards met
🔍 /admin accessibility:
  - Headings: 4
  - ARIA labels: 1
  - Landmarks: 0
⚠️ /admin: No landmarks found, but continuing test
✅ /admin: Accessibility standards met
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify performance benchmarks" classname="comprehensive-test-runner.spec.ts" time="9.248">
<system-out>
<![CDATA[⚡ Testing performance benchmarks...
✅ Dashboard: 1496ms (under 8000ms limit)
✅ Marketing Dashboard: 1413ms (under 15000ms limit)
✅ Admin Dashboard: 998ms (under 8000ms limit)
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should run final comprehensive validation" classname="comprehensive-test-runner.spec.ts" time="10.615">
<system-out>
<![CDATA[🎯 Running final comprehensive validation...
✅ Journey step: /brand-deep-dive completed successfully
✅ Journey step: /marketing-dashboard completed successfully
✅ Journey step: /executive-summary completed successfully
✅ Journey step: /admin completed successfully
🎉 Comprehensive test suite completed successfully!
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>