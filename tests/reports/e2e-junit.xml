<testsuites id="" name="" tests="123" failures="21" skipped="0" errors="0" time="155.52323199999998">
<testsuite name="comprehensive-admin-dashboard.spec.ts" timestamp="2025-05-30T02:27:11.670Z" hostname="chromium" tests="16" failures="5" skipped="0" time="170.271" errors="0">
<testcase name="Comprehensive Admin Dashboard Testing › should load admin dashboard with proper authorization" classname="comprehensive-admin-dashboard.spec.ts" time="27.929">
<failure message="comprehensive-admin-dashboard.spec.ts:23:7 should load admin dashboard with proper authorization" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-admin-dashboard.spec.ts:23:7 › Comprehensive Admin Dashboard Testing › should load admin dashboard with proper authorization 

    Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)

    Locator: locator(':root')
    Expected pattern: /Admin|NOLK/
    Received string:  "Create Next App"
    Call log:
      - expect.toHaveTitle with timeout 15000ms
      - waiting for locator(':root')
        19 × locator resolved to <html lang="en">…</html>
           - unexpected value "Create Next App"


      31 |
      32 |     // Verify page title and main elements
    > 33 |     await expect(page).toHaveTitle(/Admin|NOLK/);
         |                        ^
      34 |     
      35 |     // Check for admin-specific elements
      36 |     const expectedElements = [
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:33:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-722b6-d-with-proper-authorization-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-722b6-d-with-proper-authorization-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-admin-dashbo-722b6-d-with-proper-authorization-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-722b6-d-with-proper-authorization-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-722b6-d-with-proper-authorization-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-722b6-d-with-proper-authorization-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should display admin navigation menu" classname="comprehensive-admin-dashboard.spec.ts" time="12.946">
<failure message="comprehensive-admin-dashboard.spec.ts:46:7 should display admin navigation menu" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-admin-dashboard.spec.ts:46:7 › Comprehensive Admin Dashboard Testing › should display admin navigation menu 

    Error: locator.isVisible: Error: strict mode violation: locator('nav a:has-text("Users"), a[href*="users"]') resolved to 2 elements:
        1) <a href="/admin/users" data-size="default" data-active="false" data-state="closed" data-sidebar="menu-button" data-slot="sidebar-menu-button" class="peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left outline-hidden ring-sidebar-ring transition-[width,height,padding] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabl…>…</a> aka getByRole('link', { name: 'Users', exact: true })
        2) <a href="/admin/users">…</a> aka getByRole('link', { name: 'User Management Manage users' })

    Call log:
        - checking visibility of locator('nav a:has-text("Users"), a[href*="users"]')


      57 |     for (const item of adminNavItems) {
      58 |       const navItem = page.locator(`nav a:has-text("${item}"), a[href*="${item.toLowerCase()}"]`);
    > 59 |       if (await navItem.isVisible()) {
         |                         ^
      60 |         foundNavItems++;
      61 |       }
      62 |     }
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:59:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-00f9b-splay-admin-navigation-menu-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-00f9b-splay-admin-navigation-menu-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-admin-dashbo-00f9b-splay-admin-navigation-menu-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-00f9b-splay-admin-navigation-menu-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-00f9b-splay-admin-navigation-menu-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-00f9b-splay-admin-navigation-menu-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Users management" classname="comprehensive-admin-dashboard.spec.ts" time="13.066">
<system-out>
<![CDATA[Users table has 6 headers and 14 rows
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Roles management" classname="comprehensive-admin-dashboard.spec.ts" time="13.035">
<system-out>
<![CDATA[Roles table has 4 headers and 4 rows
Found 10 role-related elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Permissions management" classname="comprehensive-admin-dashboard.spec.ts" time="13.031">
<system-out>
<![CDATA[Permissions table has 6 headers and 2 rows
Found 3 permission-related elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Groups management" classname="comprehensive-admin-dashboard.spec.ts" time="13.009">
<system-out>
<![CDATA[Groups table has 4 headers and 4 rows
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Brands management" classname="comprehensive-admin-dashboard.spec.ts" time="12.973">
<failure message="comprehensive-admin-dashboard.spec.ts:156:7 should navigate to and test Brands management" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-admin-dashboard.spec.ts:156:7 › Comprehensive Admin Dashboard Testing › should navigate to and test Brands management 

    Error: locator.count: Unexpected token "=" while parsing css selector "[data-testid*="brand"], .brand, text=/brand/i". Did you mean to CSS.escape it?

      170 |     // Look for brand-specific features
      171 |     const brandElements = page.locator('[data-testid*="brand"], .brand, text=/brand/i');
    > 172 |     const brandCount = await brandElements.count();
          |                                            ^
      173 |     console.log(`Found ${brandCount} brand-related elements`);
      174 |
      175 |     await takeScreenshot(page, 'admin-brands-page');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:172:44

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-c9ae0--and-test-Brands-management-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-c9ae0--and-test-Brands-management-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[Brands table has 8 headers and 23 rows

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-c9ae0--and-test-Brands-management-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-c9ae0--and-test-Brands-management-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Settings page" classname="comprehensive-admin-dashboard.spec.ts" time="10.721">
<system-out>
<![CDATA[Found 0 settings elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Backups page" classname="comprehensive-admin-dashboard.spec.ts" time="6.976">
<system-out>
<![CDATA[Found 3 backup-related elements
Found 0 backup buttons
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test DB Structure page" classname="comprehensive-admin-dashboard.spec.ts" time="7.114">
<system-out>
<![CDATA[Found 0 database structure elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should test admin API endpoints" classname="comprehensive-admin-dashboard.spec.ts" time="6.496">
<system-out>
<![CDATA[/api/admin/users: 200
/api/admin/roles: 200
/api/admin/permissions: 200
/api/admin/groups: 200
/api/admin/brands: 200
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should handle admin form submissions" classname="comprehensive-admin-dashboard.spec.ts" time="7.445">
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should be responsive across different screen sizes" classname="comprehensive-admin-dashboard.spec.ts" time="7.168">
<failure message="comprehensive-admin-dashboard.spec.ts:285:7 should be responsive across different screen sizes" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-admin-dashboard.spec.ts:285:7 › Comprehensive Admin Dashboard Testing › should be responsive across different screen sizes 

    Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role="main"]') resolved to 2 elements:
        1) <main data-slot="sidebar-inset" class="bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2">…</main> aka getByText('Toggle SidebarDashboardAdminAdmin DashboardManage users, roles, and system')
        2) <main class="flex-1 overflow-auto">…</main> aka getByRole('main').filter({ hasText: 'Toggle' }).getByRole('main')

    Call log:
      - expect.toBeVisible with timeout 15000ms
      - waiting for locator('main, [role="main"]')


      298 |       
      299 |       // Verify main elements are still visible
    > 300 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
          |                                                         ^
      301 |       
      302 |       // Check admin navigation on different screens
      303 |       const adminNav = page.locator('nav');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:300:57

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-7048f-ross-different-screen-sizes-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-7048f-ross-different-screen-sizes-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-admin-dashbo-7048f-ross-different-screen-sizes-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-7048f-ross-different-screen-sizes-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-7048f-ross-different-screen-sizes-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-7048f-ross-different-screen-sizes-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should have proper accessibility features" classname="comprehensive-admin-dashboard.spec.ts" time="6.143">
<failure message="comprehensive-admin-dashboard.spec.ts:312:7 should have proper accessibility features" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-admin-dashboard.spec.ts:312:7 › Comprehensive Admin Dashboard Testing › should have proper accessibility features 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      318 |     // Verify minimum accessibility requirements for admin interface
      319 |     expect(accessibilityInfo.headings).toBeGreaterThan(0);
    > 320 |     expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
          |                                         ^
      321 |     
      322 |     console.log('Admin Dashboard accessibility info:', accessibilityInfo);
      323 |     await takeScreenshot(page, 'admin-dashboard-accessibility');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:320:41

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-14f8d-oper-accessibility-features-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-admin-dashbo-14f8d-oper-accessibility-features-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-admin-dashbo-14f8d-oper-accessibility-features-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-14f8d-oper-accessibility-features-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-14f8d-oper-accessibility-features-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-admin-dashbo-14f8d-oper-accessibility-features-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should load within performance thresholds" classname="comprehensive-admin-dashboard.spec.ts" time="6.127">
<system-out>
<![CDATA[Admin Dashboard loaded in 1331ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should not have critical console errors" classname="comprehensive-admin-dashboard.spec.ts" time="6.092">
<system-out>
<![CDATA[Critical console errors found: [
  [32m'Error fetching saved views: TypeError: Failed to fetch\n'[39m +
    [32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\n'[39m +
    [32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\n'[39m +
    [32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'[39m,
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9578:33)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9581:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)'[39m,
  [32m'Error fetching saved views: TypeError: Failed to fetch\n'[39m +
    [32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\n'[39m +
    [32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\n'[39m +
    [32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9578:33)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)'[39m,
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'[39m
]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-api.spec.ts" timestamp="2025-05-30T02:27:11.670Z" hostname="chromium" tests="13" failures="0" skipped="0" time="85.95" errors="0">
<testcase name="Comprehensive API Testing › should test dashboard API endpoints" classname="comprehensive-api.spec.ts" time="5.729">
<system-out>
<![CDATA[/api/dashboard/flexible-kpis: 200
/api/dashboard/flexible-kpis returned data: [
  [32m'Gross Revenue'[39m,         [32m'Net Revenue'[39m,
  [32m'Gross Margin'[39m,          [32m'Adspend'[39m,
  [32m'Contribution Margin'[39m,   [32m'% Gross Margin'[39m,
  [32m'% Contribution Margin'[39m, [32m'% Adspend'[39m,
  [32m'Landed Cost'[39m,           [32m'Fulfillment Cost'[39m,
  [32m'Transaction Cost'[39m,      [32m'Discount'[39m,
  [32m'Refund'[39m,                [32m'% Landed Cost'[39m,
  [32m'% Fulfillment Cost'[39m,    [32m'% Transaction Cost'[39m,
  [32m'% Discount'[39m,            [32m'% Refund'[39m,
  [32m'ACOS'[39m,                  [32m'TACOS'[39m,
  [32m'TCAC'[39m,                  [32m'Website Traffic'[39m,
  [32m'Conversion Rate'[39m,       [32m'Organic Traffic'[39m,
  [32m'Paid Traffic'[39m
]
/api/dashboard/kpi-data: 404
/api/dashboard/chart-data: 404
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test marketing API endpoints" classname="comprehensive-api.spec.ts" time="5.533">
<system-out>
<![CDATA[/api/marketing/campaigns: 404
/api/marketing/kpis: 404
/api/marketing/spend-breakdown: 404
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test admin API endpoints with proper authorization" classname="comprehensive-api.spec.ts" time="7.105">
<system-out>
<![CDATA[/api/admin/users: 200
/api/admin/users returned data: [
  [32m'0'[39m,  [32m'1'[39m,  [32m'2'[39m,  [32m'3'[39m,
  [32m'4'[39m,  [32m'5'[39m,  [32m'6'[39m,  [32m'7'[39m,
  [32m'8'[39m,  [32m'9'[39m,  [32m'10'[39m, [32m'11'[39m,
  [32m'12'[39m, [32m'13'[39m
]
/api/admin/roles: 200
/api/admin/roles returned data: [ [32m'data'[39m, [32m'pagination'[39m ]
/api/admin/permissions: 200
/api/admin/permissions returned data: [ [32m'0'[39m, [32m'1'[39m ]
/api/admin/groups: 200
/api/admin/groups returned data: [ [32m'data'[39m, [32m'pagination'[39m ]
/api/admin/brands: 200
/api/admin/brands returned data: [ [32m'data'[39m, [32m'pagination'[39m ]
/api/admin/settings: 401
/api/admin/settings: Unauthorized (expected for non-admin users)
/api/admin/backups: 401
/api/admin/backups: Unauthorized (expected for non-admin users)
/api/admin/db-structure: 401
/api/admin/db-structure: Unauthorized (expected for non-admin users)
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test budget API endpoint" classname="comprehensive-api.spec.ts" time="5.667">
<system-out>
<![CDATA[Budget API: 200
Budget API returned data: [ [32m'budgets'[39m ]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test AI assistant API endpoint" classname="comprehensive-api.spec.ts" time="19.184">
<system-out>
<![CDATA[AI Assistant GET: 405
AI Assistant POST: 200
AI Assistant response: {
  message: [32m'# Total Revenue Analysis\n'[39m +
    [32m'\n'[39m +
    [32m'## Summary\n'[39m +
    [32m'The total gross revenue across all brands is **C$14,347,789**\n'[39m +
    [32m'\n'[39m +
    [32m'Would you like me to break down this revenue by specific time periods, brands, or channels?'[39m,
  charts: [
    {
      type: [32m'line'[39m,
      title: [32m'KPI Comparison'[39m,
      data: [36m[Array][39m,
      xAxis: [32m'date'[39m,
      yAxis: [36m[Array][39m
    }
  ],
  contextInfo: [32m'Error generating context information.'[39m
}
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API error handling" classname="comprehensive-api.spec.ts" time="5.787">
<system-out>
<![CDATA[/api/nonexistent: 404
/api/dashboard/invalid: 404
/api/admin/invalid: 404
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API with query parameters" classname="comprehensive-api.spec.ts" time="5.156">
<system-out>
<![CDATA[/api/dashboard/flexible-kpis?startDate=2024-01-01&endDate=2024-12-31: 200
/api/dashboard/flexible-kpis?startDate=2024-01-01&endDate=2024-12-31 with params returned: [
  [32m'Gross Revenue'[39m,         [32m'Net Revenue'[39m,
  [32m'Gross Margin'[39m,          [32m'Adspend'[39m,
  [32m'Contribution Margin'[39m,   [32m'% Gross Margin'[39m,
  [32m'% Contribution Margin'[39m, [32m'% Adspend'[39m,
  [32m'Landed Cost'[39m,           [32m'Fulfillment Cost'[39m,
  [32m'Transaction Cost'[39m,      [32m'Discount'[39m,
  [32m'Refund'[39m,                [32m'% Landed Cost'[39m,
  [32m'% Fulfillment Cost'[39m,    [32m'% Transaction Cost'[39m,
  [32m'% Discount'[39m,            [32m'% Refund'[39m,
  [32m'ACOS'[39m,                  [32m'TACOS'[39m,
  [32m'TCAC'[39m,                  [32m'Website Traffic'[39m,
  [32m'Conversion Rate'[39m,       [32m'Organic Traffic'[39m,
  [32m'Paid Traffic'[39m
]
/api/marketing/campaigns?brand=test&currency=USD: 404
/api/budget?brand=test&brands=true: 200
/api/budget?brand=test&brands=true with params returned: [ [32m'brands'[39m ]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API response times" classname="comprehensive-api.spec.ts" time="5.094">
<system-out>
<![CDATA[/api/dashboard/flexible-kpis: 200 in 254ms
/api/marketing/campaigns: 404 in 56ms
/api/budget: 200 in 67ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API data validation" classname="comprehensive-api.spec.ts" time="5.231">
</testcase>
<testcase name="Comprehensive API Testing › should test API authentication requirements" classname="comprehensive-api.spec.ts" time="5.281">
<system-out>
<![CDATA[/api/admin/users (unauthenticated): 200
/api/admin/users (unauthenticated): Failed as expected - Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoContain[2m([22m[32mexpected[39m[2m) // indexOf[22m

Expected value: [32m200[39m
Received array: [31m[401, 302, 403][39m
/api/admin/roles (unauthenticated): 200
/api/admin/roles (unauthenticated): Failed as expected - Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoContain[2m([22m[32mexpected[39m[2m) // indexOf[22m

Expected value: [32m200[39m
Received array: [31m[401, 302, 403][39m
/api/dashboard/flexible-kpis (unauthenticated): 200
/api/dashboard/flexible-kpis (unauthenticated): Failed as expected - Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoContain[2m([22m[32mexpected[39m[2m) // indexOf[22m

Expected value: [32m200[39m
Received array: [31m[401, 302, 403][39m
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API CORS headers" classname="comprehensive-api.spec.ts" time="5.048">
<system-out>
<![CDATA[API Response Headers:
Content-Type: application/json
Cache-Control: no-store, max-age=0
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API rate limiting (if implemented)" classname="comprehensive-api.spec.ts" time="6.01">
<system-out>
<![CDATA[Request 1: 200
Request 2: 200
Request 3: 200
Request 4: 200
Request 5: 200
Request 6: 200
Request 7: 200
Request 8: 200
Request 9: 200
Request 10: 200
Successful requests: 10, Rate limited: 0
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API monitoring endpoints" classname="comprehensive-api.spec.ts" time="5.125">
<system-out>
<![CDATA[/api/monitoring/health: 200
/api/monitoring/health health check: {
  overall: [32m'healthy'[39m,
  timestamp: [33m1748572065392[39m,
  uptime: [33m36.888842958[39m,
  version: [32m'0.1.0'[39m,
  environment: [32m'development'[39m,
  checks: [
    {
      service: [32m'SQLite Database'[39m,
      status: [32m'healthy'[39m,
      responseTime: [33m0[39m,
      details: [32m'Database responding normally'[39m,
      lastChecked: [33m1748572065371[39m
    },
    {
      service: [32m'Redshift Database'[39m,
      status: [32m'healthy'[39m,
      responseTime: [33m21[39m,
      details: [32m'Redshift responding normally'[39m,
      lastChecked: [33m1748572065392[39m
    },
    {
      service: [32m'NextAuth'[39m,
      status: [32m'healthy'[39m,
      details: [32m'NextAuth configured'[39m,
      lastChecked: [33m1748572065392[39m
    },
    {
      service: [32m'Google OAuth'[39m,
      status: [32m'healthy'[39m,
      details: [32m'Google OAuth configured'[39m,
      lastChecked: [33m1748572065392[39m
    }
  ],
  performance: {
    memory: {
      rss: [33m2979414016[39m,
      heapTotal: [33m442580992[39m,
      heapUsed: [33m400550176[39m,
      external: [33m65451075[39m,
      arrayBuffers: [33m62799962[39m
    },
    cpu: { user: [33m68653265[39m, system: [33m21761291[39m },
    queries: {
      totalQueries: [33m0[39m,
      slowQueries: [33m0[39m,
      avgDuration: [33m0[39m,
      cacheHitRate: [33m0[39m,
      topSlowQueries: []
    },
    apis: {
      totalRequests: [33m0[39m,
      errorRequests: [33m0[39m,
      slowRequests: [33m0[39m,
      avgDuration: [33m0[39m,
      errorRate: [33m0[39m,
      topSlowEndpoints: []
    },
    errors: { totalErrors: [33m0[39m, errorsByType: {}, recentErrors: [] }
  },
  database: {
    sqlite: {
      status: [32m'healthy'[39m,
      responseTime: [33m0[39m,
      details: [32m'Database responding normally'[39m
    },
    redshift: {
      status: [32m'healthy'[39m,
      responseTime: [33m21[39m,
      poolStats: [36m[Object][39m,
      details: [32m'Redshift responding normally'[39m
    }
  }
}
/api/monitoring/status: 404
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-brand-deep-dive.spec.ts" timestamp="2025-05-30T02:27:11.670Z" hostname="chromium" tests="12" failures="2" skipped="0" time="78.099" errors="0">
<testcase name="Comprehensive Brand Deep Dive Testing › should load brand deep dive page with all components" classname="comprehensive-brand-deep-dive.spec.ts" time="6.914">
<system-out>
<![CDATA[Page title: Create Next App
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should display brand selector and allow brand selection" classname="comprehensive-brand-deep-dive.spec.ts" time="6.063">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should display KPI cards specific to selected brand" classname="comprehensive-brand-deep-dive.spec.ts" time="5.955">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should render brand performance charts" classname="comprehensive-brand-deep-dive.spec.ts" time="8.66">
<system-out>
<![CDATA[Found 1 charts with selector: svg[class*="chart"]
Found 23 charts on brand deep dive page
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should display marketing campaigns for selected brand" classname="comprehensive-brand-deep-dive.spec.ts" time="6.636">
<failure message="comprehensive-brand-deep-dive.spec.ts:115:7 should display marketing campaigns for selected brand" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-brand-deep-dive.spec.ts:115:7 › Comprehensive Brand Deep Dive Testing › should display marketing campaigns for selected brand 

    Error: locator.isVisible: Unexpected token "=" while parsing css selector "[data-testid*="campaign"], .campaign, text=/campaign/i". Did you mean to CSS.escape it?
    Call log:
        - checking visibility of [data-testid*="campaign"], .campaign, text=/campaign/i >> nth=0


      121 |     const campaignsSection = page.locator('[data-testid*="campaign"], .campaign, text=/campaign/i');
      122 |
    > 123 |     if (await campaignsSection.first().isVisible()) {
          |                                        ^
      124 |       // Check for campaign data
      125 |       const campaignItems = page.locator('[data-testid*="campaign-item"], .campaign-item, tr');
      126 |       const campaignCount = await campaignItems.count();
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:123:40

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-d54f0-ampaigns-for-selected-brand-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-d54f0-ampaigns-for-selected-brand-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-brand-deep-d-d54f0-ampaigns-for-selected-brand-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-d54f0-ampaigns-for-selected-brand-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-d54f0-ampaigns-for-selected-brand-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-d54f0-ampaigns-for-selected-brand-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should have functional brand-specific filters" classname="comprehensive-brand-deep-dive.spec.ts" time="8.042">
<system-out>
<![CDATA[Found 1 active filters
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should display tabs and allow tab navigation" classname="comprehensive-brand-deep-dive.spec.ts" time="5.669">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should handle brand comparison if available" classname="comprehensive-brand-deep-dive.spec.ts" time="5.808">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should handle export functionality if available" classname="comprehensive-brand-deep-dive.spec.ts" time="5.909">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should be responsive across different screen sizes" classname="comprehensive-brand-deep-dive.spec.ts" time="6.906">
<failure message="comprehensive-brand-deep-dive.spec.ts:246:7 should be responsive across different screen sizes" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-brand-deep-dive.spec.ts:246:7 › Comprehensive Brand Deep Dive Testing › should be responsive across different screen sizes 

    ReferenceError: getMainContent is not defined

      259 |
      260 |       // Verify main elements are still visible and properly arranged
    > 261 |       const mainContent = await getMainContent(page);
          |                           ^
      262 |       await expect(mainContent).toBeVisible();
      263 |
      264 |       // Check if mobile menu appears on small screens
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:261:27

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-ce6db-ross-different-screen-sizes-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-brand-deep-d-ce6db-ross-different-screen-sizes-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-ce6db-ross-different-screen-sizes-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-brand-deep-d-ce6db-ross-different-screen-sizes-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should have proper accessibility features" classname="comprehensive-brand-deep-dive.spec.ts" time="5.776">
<system-out>
<![CDATA[Brand Deep Dive landmarks: 0
Brand Deep Dive accessibility info: { ariaLabels: [33m1[39m, roles: [33m6[39m, headings: [33m3[39m, landmarks: [33m0[39m }
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should load within performance thresholds" classname="comprehensive-brand-deep-dive.spec.ts" time="5.761">
<system-out>
<![CDATA[Brand Deep Dive loaded in 1049ms
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-budget-ai.spec.ts" timestamp="2025-05-30T02:27:11.670Z" hostname="chromium" tests="15" failures="2" skipped="0" time="94.184" errors="0">
<testcase name="Comprehensive Budget Page Testing › should load budget page with all components" classname="comprehensive-budget-ai.spec.ts" time="6.583">
<system-out>
<![CDATA[Page title: Create Next App
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Budget Page Testing › should display budget data and charts" classname="comprehensive-budget-ai.spec.ts" time="8.036">
<system-out>
<![CDATA[Budget table has 17 headers and 12 rows
Found 1 charts with selector: svg[class*="chart"]
Found 5 budget-related elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Budget Page Testing › should handle budget filters and brand selection" classname="comprehensive-budget-ai.spec.ts" time="5.752">
<system-out>
<![CDATA[Found 0 active filters
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Budget Page Testing › should test budget API endpoint" classname="comprehensive-budget-ai.spec.ts" time="5.339">
<system-out>
<![CDATA[Budget API response: 200
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Budget Page Testing › should be responsive on different screen sizes" classname="comprehensive-budget-ai.spec.ts" time="6.485">
<failure message="comprehensive-budget-ai.spec.ts:103:7 should be responsive on different screen sizes" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-budget-ai.spec.ts:103:7 › Comprehensive Budget Page Testing › should be responsive on different screen sizes 

    ReferenceError: getMainContent is not defined

      115 |       await page.waitForTimeout(1000);
      116 |
    > 117 |       const mainContent = await getMainContent(page);
          |                           ^
      118 |       await expect(mainContent).toBeVisible();
      119 |       await takeScreenshot(page, `budget-responsive-${viewport.name}`);
      120 |     }
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:117:27

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-84dd1-e-on-different-screen-sizes-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-84dd1-e-on-different-screen-sizes-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-84dd1-e-on-different-screen-sizes-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-84dd1-e-on-different-screen-sizes-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should load AI assistant page with all components" classname="comprehensive-budget-ai.spec.ts" time="7.5">
<system-out>
<![CDATA[Page title: Create Next App
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should display chat interface" classname="comprehensive-budget-ai.spec.ts" time="6.782">
<system-out>
<![CDATA[Found 1 chat interface elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should handle message input and sending" classname="comprehensive-budget-ai.spec.ts" time="6.316">
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should display conversation history" classname="comprehensive-budget-ai.spec.ts" time="5.701">
<system-out>
<![CDATA[Found 0 messages in conversation history
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should handle AI assistant features" classname="comprehensive-budget-ai.spec.ts" time="5.645">
<system-out>
<![CDATA[Found 0 AI assistant features
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should test AI assistant API endpoint" classname="comprehensive-budget-ai.spec.ts" time="5.59">
<system-out>
<![CDATA[AI Assistant API failed: Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoBe[2m([22m[32mexpected[39m[2m) // Object.is equality[22m

Expected: [32m200[39m
Received: [31m405[39m
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should handle suggested questions or templates" classname="comprehensive-budget-ai.spec.ts" time="5.387">
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should be responsive on different screen sizes" classname="comprehensive-budget-ai.spec.ts" time="6.571">
<failure message="comprehensive-budget-ai.spec.ts:305:7 should be responsive on different screen sizes" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-budget-ai.spec.ts:305:7 › Comprehensive AI Assistant Testing › should be responsive on different screen sizes 

    ReferenceError: getMainContent is not defined

      317 |       await page.waitForTimeout(1000);
      318 |
    > 319 |       const mainContent = await getMainContent(page);
          |                           ^
      320 |       await expect(mainContent).toBeVisible();
      321 |
      322 |       // Check chat interface on different screens
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:319:27

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-1b3a3-e-on-different-screen-sizes-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-1b3a3-e-on-different-screen-sizes-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-1b3a3-e-on-different-screen-sizes-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-budget-ai-Co-1b3a3-e-on-different-screen-sizes-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should have proper accessibility features" classname="comprehensive-budget-ai.spec.ts" time="6.306">
<system-out>
<![CDATA[AI Assistant landmarks: 0
AI Assistant accessibility info: { ariaLabels: [33m1[39m, roles: [33m2[39m, headings: [33m2[39m, landmarks: [33m0[39m }
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should load within performance thresholds" classname="comprehensive-budget-ai.spec.ts" time="6.191">
<system-out>
<![CDATA[AI Assistant loaded in 1327ms
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-dashboard.spec.ts" timestamp="2025-05-30T02:27:11.670Z" hostname="chromium" tests="10" failures="1" skipped="0" time="91.355" errors="0">
<testcase name="Comprehensive Dashboard Testing › should load dashboard with all core components" classname="comprehensive-dashboard.spec.ts" time="6.672">
<system-out>
<![CDATA[Page title: Create Next App
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Dashboard Testing › should display and interact with KPI cards" classname="comprehensive-dashboard.spec.ts" time="7.622">
</testcase>
<testcase name="Comprehensive Dashboard Testing › should render charts correctly" classname="comprehensive-dashboard.spec.ts" time="9.506">
<system-out>
<![CDATA[Found 11 charts with selector: .recharts-wrapper
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Dashboard Testing › should have functional filters" classname="comprehensive-dashboard.spec.ts" time="6.561">
<failure message="comprehensive-dashboard.spec.ts:79:7 should have functional filters" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-dashboard.spec.ts:79:7 › Comprehensive Dashboard Testing › should have functional filters 

    Error: locator.isVisible: Error: strict mode violation: locator('input[type="date"]') resolved to 2 elements:
        1) <input type="date" id="start-date" data-slot="input" value="2025-03-01" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 …/> aka getByRole('textbox', { name: 'Start Date' })
        2) <input type="date" id="end-date" data-slot="input" value="2025-05-30" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md…/> aka getByRole('textbox', { name: 'End Date' })

    Call log:
        - checking visibility of locator('input[type="date"]')


      88 |     // Test date filter if present
      89 |     const dateFilter = page.locator('input[type="date"]');
    > 90 |     if (await dateFilter.isVisible()) {
         |                          ^
      91 |       await dateFilter.fill('2024-01-01');
      92 |       await page.waitForTimeout(2000);
      93 |       await takeScreenshot(page, 'dashboard-date-filter-applied');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts:90:26

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-dashboard-Co-4f9f8-uld-have-functional-filters-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-dashboard-Co-4f9f8-uld-have-functional-filters-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-dashboard-Co-4f9f8-uld-have-functional-filters-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[Found 1 active filters: [ [32m'input[type="date"] (1/2)'[39m ]

[[ATTACHMENT|e2e-artifacts/comprehensive-dashboard-Co-4f9f8-uld-have-functional-filters-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-dashboard-Co-4f9f8-uld-have-functional-filters-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-dashboard-Co-4f9f8-uld-have-functional-filters-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Dashboard Testing › should handle responsive design" classname="comprehensive-dashboard.spec.ts" time="9.574">
</testcase>
<testcase name="Comprehensive Dashboard Testing › should have proper accessibility features" classname="comprehensive-dashboard.spec.ts" time="6.641">
<system-out>
<![CDATA[Dashboard landmarks: 0
Accessibility info: { ariaLabels: [33m21[39m, roles: [33m26[39m, headings: [33m3[39m, landmarks: [33m0[39m }
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Dashboard Testing › should handle navigation between dashboard sections" classname="comprehensive-dashboard.spec.ts" time="23.152">
</testcase>
<testcase name="Comprehensive Dashboard Testing › should handle data refresh and updates" classname="comprehensive-dashboard.spec.ts" time="8.735">
</testcase>
<testcase name="Comprehensive Dashboard Testing › should not have critical console errors" classname="comprehensive-dashboard.spec.ts" time="6.723">
<system-out>
<![CDATA[Critical console errors found: [
  [32m'Error fetching sales channels: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3801:56)'[39m,
  [32m'Error fetching saved views: TypeError: Failed to fetch\n'[39m +
    [32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\n'[39m +
    [32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\n'[39m +
    [32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'[39m,
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'[39m
]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Dashboard Testing › should load within performance thresholds" classname="comprehensive-dashboard.spec.ts" time="6.169">
<system-out>
<![CDATA[Dashboard loaded in 1333ms
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-error-handling.spec.ts" timestamp="2025-05-30T02:27:11.670Z" hostname="chromium" tests="12" failures="6" skipped="0" time="191.666" errors="0">
<testcase name="Comprehensive Error Handling and Edge Cases › should handle 404 errors gracefully" classname="comprehensive-error-handling.spec.ts" time="9.702">
<failure message="comprehensive-error-handling.spec.ts:15:7 should handle 404 errors gracefully" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-error-handling.spec.ts:15:7 › Comprehensive Error Handling and Edge Cases › should handle 404 errors gracefully 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      37 |
      38 |       // Either shows 404, redirects, or shows a valid page (some routes might be valid)
    > 39 |       expect(is404 || isRedirect || isValidPage).toBe(true);
         |                                                  ^
      40 |       await takeScreenshot(page, `error-404-${url.replace(/\//g, '-')}`);
      41 |     }
      42 |   });
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:39:50

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[/nonexistent-page -> http://localhost:6699/nonexistent-page
/dashboard/invalid -> http://localhost:6699/dashboard/invalid
/admin/nonexistent -> http://localhost:6699/admin/nonexistent
/marketing-dashboard/invalid-campaign-id -> http://localhost:6699/marketing-dashboard/invalid-campaign-id

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle network errors and offline scenarios" classname="comprehensive-error-handling.spec.ts" time="9.832">
<system-out>
<![CDATA[Expected network error: page.goto: net::ERR_INTERNET_DISCONNECTED at http://localhost:6699/marketing-dashboard
Call log:
[2m  - navigating to "http://localhost:6699/marketing-dashboard", waiting until "load"[22m

Offline handling: Not detected
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle API errors gracefully" classname="comprehensive-error-handling.spec.ts" time="6.782">
<failure message="comprehensive-error-handling.spec.ts:76:7 should handle API errors gracefully" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-error-handling.spec.ts:76:7 › Comprehensive Error Handling and Edge Cases › should handle API errors gracefully 

    Error: locator.isVisible: Error: strict mode violation: locator('text=/error|failed|something went wrong/i') resolved to 2 elements:
        1) <div data-slot="card-description" class="text-muted-foreground text-sm">Error loading data: HTTP 500: Internal Server Err…</div> aka getByText('Error loading data: HTTP 500')
        2) <div class="text-center text-red-500">Server error occurred. Please refresh the page or…</div> aka getByText('Server error occurred. Please')

    Call log:
        - checking visibility of locator('text=/error|failed|something went wrong/i')


      90 |     // Should show error message or fallback content
      91 |     const errorMessage = page.locator('text=/error|failed|something went wrong/i');
    > 92 |     const hasErrorHandling = await errorMessage.isVisible();
         |                                                 ^
      93 |
      94 |     console.log(`API error handling: ${hasErrorHandling ? 'Detected' : 'Not detected'}`);
      95 |     await takeScreenshot(page, 'api-error-500');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:92:49

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-5bfc5-andle-API-errors-gracefully-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-5bfc5-andle-API-errors-gracefully-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-error-handli-5bfc5-andle-API-errors-gracefully-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-5bfc5-andle-API-errors-gracefully-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-5bfc5-andle-API-errors-gracefully-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-5bfc5-andle-API-errors-gracefully-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle authentication errors" classname="comprehensive-error-handling.spec.ts" time="8.434">
<failure message="comprehensive-error-handling.spec.ts:98:7 should handle authentication errors" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-error-handling.spec.ts:98:7 › Comprehensive Error Handling and Edge Cases › should handle authentication errors 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      120 |
      121 |       // Either redirected to sign-in or stayed on dashboard (if session is still valid)
    > 122 |       expect(isOnSignIn || isOnDashboard).toBe(true);
          |                                           ^
      123 |
      124 |       console.log(`${url} -> redirected to sign-in`);
      125 |     }
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:122:43

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[/dashboard -> redirected to sign-in

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-2.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video-1.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle form validation errors" classname="comprehensive-error-handling.spec.ts" time="7.39">
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle large data sets without crashing" classname="comprehensive-error-handling.spec.ts" time="11.983">
<failure message="comprehensive-error-handling.spec.ts:164:7 should handle large data sets without crashing" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-error-handling.spec.ts:164:7 › Comprehensive Error Handling and Edge Cases › should handle large data sets without crashing 

    ReferenceError: getMainContent is not defined

      178 |
      179 |       // Page should still be responsive
    > 180 |       const mainContent = await getMainContent(page);
          |                           ^
      181 |       const isResponsive = await mainContent.isVisible();
      182 |       expect(isResponsive).toBe(true);
      183 |
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:180:27

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-72ce6--data-sets-without-crashing-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-72ce6--data-sets-without-crashing-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-72ce6--data-sets-without-crashing-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-72ce6--data-sets-without-crashing-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle browser back/forward navigation" classname="comprehensive-error-handling.spec.ts" time="14.317">
<system-out>
<![CDATA[Browser navigation: Working correctly
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle page refresh and state preservation" classname="comprehensive-error-handling.spec.ts" time="9.734">
<failure message="comprehensive-error-handling.spec.ts:218:7 should handle page refresh and state preservation" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-error-handling.spec.ts:218:7 › Comprehensive Error Handling and Edge Cases › should handle page refresh and state preservation 

    ReferenceError: getMainContent is not defined

      233 |
      234 |     // Page should load successfully after refresh
    > 235 |     const mainContent = await getMainContent(page);
          |                         ^
      236 |     const isLoaded = await mainContent.isVisible();
      237 |     expect(isLoaded).toBe(true);
      238 |
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:235:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-a4ae1-resh-and-state-preservation-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-a4ae1-resh-and-state-preservation-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-a4ae1-resh-and-state-preservation-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-a4ae1-resh-and-state-preservation-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle concurrent user actions" classname="comprehensive-error-handling.spec.ts" time="60.098">
<failure message="comprehensive-error-handling.spec.ts:243:7 should handle concurrent user actions" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-error-handling.spec.ts:243:7 › Comprehensive Error Handling and Edge Cases › should handle concurrent user actions 

    Test timeout of 60000ms exceeded.

    Error: page.waitForTimeout: Target page, context or browser has been closed

      259 |     await Promise.all(promises);
      260 |
    > 261 |     await page.waitForTimeout(2000);
          |                ^
      262 |     await waitForLoadingToComplete(page);
      263 |
      264 |     // Page should still be functional
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:261:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle memory leaks during navigation" classname="comprehensive-error-handling.spec.ts" time="32.1">
<system-out>
<![CDATA[Initial memory: 29.75MB
Final memory: 29.75MB
Memory increase: 0.00MB
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle edge cases in data filtering" classname="comprehensive-error-handling.spec.ts" time="12.271">
<system-out>
<![CDATA[Invalid date range handling: Detected
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle session timeout gracefully" classname="comprehensive-error-handling.spec.ts" time="9.023">
<system-out>
<![CDATA[Session timeout: Redirected to sign-in
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-executive-summary.spec.ts" timestamp="2025-05-30T02:27:11.670Z" hostname="chromium" tests="13" failures="2" skipped="0" time="88.577" errors="0">
<testcase name="Comprehensive Executive Summary Testing › should load executive summary with all components" classname="comprehensive-executive-summary.spec.ts" time="6.783">
<system-out>
<![CDATA[Page title: Create Next App
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should display brand selector and period selector" classname="comprehensive-executive-summary.spec.ts" time="6.474">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should display executive KPI cards with proper formatting" classname="comprehensive-executive-summary.spec.ts" time="6.98">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should switch between cards and slides view" classname="comprehensive-executive-summary.spec.ts" time="7.998">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should display trend charts and analysis" classname="comprehensive-executive-summary.spec.ts" time="8.625">
<failure message="comprehensive-executive-summary.spec.ts:156:7 should display trend charts and analysis" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-executive-summary.spec.ts:156:7 › Comprehensive Executive Summary Testing › should display trend charts and analysis 

    Error: locator.count: Unexpected token "=" while parsing css selector "[data-testid*="trend"], .trend, text=/trend|growth|change/i". Did you mean to CSS.escape it?

      164 |     // Look for trend-specific elements
      165 |     const trendElements = page.locator('[data-testid*="trend"], .trend, text=/trend|growth|change/i');
    > 166 |     const trendCount = await trendElements.count();
          |                                            ^
      167 |
      168 |     // Look for trend indicators (up/down arrows, colors)
      169 |     const trendIndicators = page.locator('.trend-up, .trend-down, .positive, .negative, [class*="increase"], [class*="decrease"]');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:166:44

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-executive-su-d4c94-y-trend-charts-and-analysis-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-executive-su-d4c94-y-trend-charts-and-analysis-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[Found 3 charts with selector: .recharts-wrapper

[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-d4c94-y-trend-charts-and-analysis-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-d4c94-y-trend-charts-and-analysis-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should handle PDF export functionality" classname="comprehensive-executive-summary.spec.ts" time="8.376">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should display period comparison data" classname="comprehensive-executive-summary.spec.ts" time="6.375">
<system-out>
<![CDATA[Found 10 comparison elements and 0 comparison values
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should handle currency switching" classname="comprehensive-executive-summary.spec.ts" time="5.939">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should display methodology and notes if available" classname="comprehensive-executive-summary.spec.ts" time="5.755">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should be responsive across different screen sizes" classname="comprehensive-executive-summary.spec.ts" time="6.976">
<failure message="comprehensive-executive-summary.spec.ts:282:7 should be responsive across different screen sizes" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-executive-summary.spec.ts:282:7 › Comprehensive Executive Summary Testing › should be responsive across different screen sizes 

    ReferenceError: getMainContent is not defined

      295 |
      296 |       // Verify main elements are still visible
    > 297 |       const mainContent = await getMainContent(page);
          |                           ^
      298 |       await expect(mainContent).toBeVisible();
      299 |
      300 |       // Check KPI cards layout on different screens
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:297:27

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-executive-su-1b50d-ross-different-screen-sizes-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-executive-su-1b50d-ross-different-screen-sizes-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-1b50d-ross-different-screen-sizes-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-executive-su-1b50d-ross-different-screen-sizes-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should have proper accessibility features" classname="comprehensive-executive-summary.spec.ts" time="6.224">
<system-out>
<![CDATA[Executive Summary landmarks: 0
Executive Summary accessibility info: { ariaLabels: [33m29[39m, roles: [33m13[39m, headings: [33m11[39m, landmarks: [33m0[39m }
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should load within performance thresholds" classname="comprehensive-executive-summary.spec.ts" time="5.825">
<system-out>
<![CDATA[Executive Summary loaded in 1173ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should not have critical console errors" classname="comprehensive-executive-summary.spec.ts" time="6.247">
<system-out>
<![CDATA[Critical console errors found: [
  [32m'Error fetching saved views: TypeError: Failed to fetch\n'[39m +
    [32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\n'[39m +
    [32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\n'[39m +
    [32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'[39m,
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9578:33)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9581:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)'[39m,
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'[39m
]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-marketing-dashboard.spec.ts" timestamp="2025-05-30T02:27:11.670Z" hostname="chromium" tests="13" failures="2" skipped="0" time="84.581" errors="0">
<testcase name="Comprehensive Marketing Dashboard Testing › should load marketing dashboard with all components" classname="comprehensive-marketing-dashboard.spec.ts" time="6.299">
<system-out>
<![CDATA[Page title: Create Next App
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should display marketing KPI cards" classname="comprehensive-marketing-dashboard.spec.ts" time="6.286">
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should display campaign data table with proper functionality" classname="comprehensive-marketing-dashboard.spec.ts" time="6.114">
<system-out>
<![CDATA[Campaign table has 12 headers and 50 rows
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should render spend breakdown charts" classname="comprehensive-marketing-dashboard.spec.ts" time="8.037">
<failure message="comprehensive-marketing-dashboard.spec.ts:94:7 should render spend breakdown charts" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-marketing-dashboard.spec.ts:94:7 › Comprehensive Marketing Dashboard Testing › should render spend breakdown charts 

    Error: locator.count: Unexpected token "=" while parsing css selector "[data-testid*="spend"], [class*="spend"], text=/spend breakdown/i". Did you mean to CSS.escape it?

      102 |     // Look for spend breakdown specific charts
      103 |     const spendCharts = page.locator('[data-testid*="spend"], [class*="spend"], text=/spend breakdown/i');
    > 104 |     const spendChartCount = await spendCharts.count();
          |                                               ^
      105 |
      106 |     console.log(`Found ${spendChartCount} spend-related chart elements`);
      107 |     await takeScreenshot(page, 'marketing-dashboard-spend-charts');
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts:104:47

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-80b02-nder-spend-breakdown-charts-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-80b02-nder-spend-breakdown-charts-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[Found 9 charts with selector: .recharts-wrapper

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-80b02-nder-spend-breakdown-charts-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-80b02-nder-spend-breakdown-charts-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should have functional marketing filters" classname="comprehensive-marketing-dashboard.spec.ts" time="8.291">
<system-out>
<![CDATA[Found 1 active filters
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should allow campaign search and filtering" classname="comprehensive-marketing-dashboard.spec.ts" time="5.976">
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should navigate to campaign details" classname="comprehensive-marketing-dashboard.spec.ts" time="6.16">
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should display proper marketing metrics calculations" classname="comprehensive-marketing-dashboard.spec.ts" time="6.275">
<system-out>
<![CDATA[Found 2 marketing metrics out of 8 expected
Found 57 percentage values and 168 currency values
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should handle data export functionality" classname="comprehensive-marketing-dashboard.spec.ts" time="5.76">
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should be responsive across different screen sizes" classname="comprehensive-marketing-dashboard.spec.ts" time="6.881">
<failure message="comprehensive-marketing-dashboard.spec.ts:291:7 should be responsive across different screen sizes" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-marketing-dashboard.spec.ts:291:7 › Comprehensive Marketing Dashboard Testing › should be responsive across different screen sizes 

    ReferenceError: getMainContent is not defined

      304 |
      305 |       // Verify main elements are still visible
    > 306 |       const mainContent = await getMainContent(page);
          |                           ^
      307 |       await expect(mainContent).toBeVisible();
      308 |
      309 |       // Check table responsiveness
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts:306:27

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-de393-ross-different-screen-sizes-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-marketing-da-de393-ross-different-screen-sizes-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-de393-ross-different-screen-sizes-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-marketing-da-de393-ross-different-screen-sizes-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should have proper accessibility features" classname="comprehensive-marketing-dashboard.spec.ts" time="6.244">
<system-out>
<![CDATA[Marketing Dashboard landmarks: 0
Marketing Dashboard accessibility info: { ariaLabels: [33m1[39m, roles: [33m9[39m, headings: [33m4[39m, landmarks: [33m0[39m }
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should load within performance thresholds" classname="comprehensive-marketing-dashboard.spec.ts" time="5.883">
<system-out>
<![CDATA[Marketing Dashboard loaded in 1240ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should not have critical console errors" classname="comprehensive-marketing-dashboard.spec.ts" time="6.375">
<system-out>
<![CDATA[Critical console errors found: [
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9578:33)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9581:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\n'[39m +
    [32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\n'[39m +
    [32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)'[39m,
  [32m'Error fetching saved views: TypeError: Failed to fetch\n'[39m +
    [32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\n'[39m +
    [32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\n'[39m +
    [32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'[39m,
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'[39m
]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-performance.spec.ts" timestamp="2025-05-30T02:27:11.670Z" hostname="chromium" tests="11" failures="0" skipped="0" time="130.019" errors="0">
<testcase name="Comprehensive Performance Testing › should measure page load performance across all main pages" classname="comprehensive-performance.spec.ts" time="13.193">
<system-out>
<![CDATA[Dashboard: 2009ms (max: 8000ms)
Brand Deep Dive: 1206ms (max: 12000ms)
Marketing Dashboard: 1068ms (max: 15000ms)
Executive Summary: 1032ms (max: 10000ms)
Budget: 817ms (max: 8000ms)
AI Assistant: 1342ms (max: 6000ms)
Admin Dashboard: 789ms (max: 8000ms)

=== Performance Summary ===
✅ PASS Dashboard: 2009ms
✅ PASS Brand Deep Dive: 1206ms
✅ PASS Marketing Dashboard: 1068ms
✅ PASS Executive Summary: 1032ms
✅ PASS Budget: 817ms
✅ PASS AI Assistant: 1342ms
✅ PASS Admin Dashboard: 789ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should measure Time to First Contentful Paint (FCP)" classname="comprehensive-performance.spec.ts" time="21.385">
</testcase>
<testcase name="Comprehensive Performance Testing › should measure Largest Contentful Paint (LCP)" classname="comprehensive-performance.spec.ts" time="27.132">
</testcase>
<testcase name="Comprehensive Performance Testing › should measure JavaScript bundle sizes" classname="comprehensive-performance.spec.ts" time="7.605">
<system-out>
<![CDATA[JS Bundle: %5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_fd44f5a4._.js - 1.01KB
JS Bundle: node_modules_%40swc_helpers_cjs_00636ac3._.js - 1.25KB
JS Bundle: node_modules_next_dist_2ecbf5fa._.js - 17.73KB
JS Bundle: node_modules_next_dist_client_8f19e6fb._.js - 166.58KB
JS Bundle: node_modules_next_dist_compiled_2ce9398a._.js - 192.75KB
JS Bundle: _e69f0d32._.js - 0.81KB
JS Bundle: _93808211._.js - 16.07KB
JS Bundle: app_favicon_ico_mjs_659ce808._.js - 0.55KB
JS Bundle: node_modules_next_dist_1a6ee436._.js - 18.44KB
JS Bundle: app_layout_tsx_c0237562._.js - 0.58KB
JS Bundle: _45166852._.js - 17.95KB
JS Bundle: node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js - 17.12KB
JS Bundle: node_modules_lodash_f240f67a._.js - 32.65KB
JS Bundle: node_modules_%40floating-ui_9ec1fa39._.js - 21.34KB
JS Bundle: _5e541529._.js - 70.10KB
JS Bundle: app_dashboard_page_tsx_63d9a548._.js - 0.87KB
JS Bundle: node_modules_%40radix-ui_fead58fd._.js - 55.73KB
JS Bundle: node_modules_recharts_es6_98d167ba._.js - 117.77KB
JS Bundle: node_modules_53dbc141._.js - 142.33KB
JS Bundle: %5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js - 4.05KB
JS Bundle: %5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_66796270._.js - 0.57KB
Total JS Bundle Size: 896.22KB
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test memory usage during navigation" classname="comprehensive-performance.spec.ts" time="10.039">
<system-out>
<![CDATA[/dashboard Memory: 35.57MB used
/brand-deep-dive Memory: 35.57MB used
/marketing-dashboard Memory: 35.57MB used
/executive-summary Memory: 35.57MB used

=== Memory Usage Summary ===
/dashboard: 35.57MB used, 110.63MB total
/brand-deep-dive: 35.57MB used, 110.63MB total
/marketing-dashboard: 35.57MB used, 110.63MB total
/executive-summary: 35.57MB used, 110.63MB total
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test network performance and resource loading" classname="comprehensive-performance.spec.ts" time="7.385">
<system-out>
<![CDATA[Network Performance:
Domain Lookup: 0.00ms
Connection: 0.00ms
Request: 259.30ms
Response: 19.60ms
DOM Processing: 16.30ms
Total Resources: 36
Slow Resources (>1s): 0
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test performance under different network conditions" classname="comprehensive-performance.spec.ts" time="8.024">
<system-out>
<![CDATA[Dashboard load time with slow network: 2546ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test performance with large datasets" classname="comprehensive-performance.spec.ts" time="13.183">
<system-out>
<![CDATA[/marketing-dashboard with data rendering: 4384ms
/brand-deep-dive with data rendering: 4084ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test scroll performance on long pages" classname="comprehensive-performance.spec.ts" time="7.208">
<system-out>
<![CDATA[Scroll performance: 1005ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test chart rendering performance" classname="comprehensive-performance.spec.ts" time="6.836">
<system-out>
<![CDATA[Chart rendering time: 1ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test accessibility performance" classname="comprehensive-performance.spec.ts" time="8.029">
<system-out>
<![CDATA[/dashboard accessibility scan: 1286ms
Accessibility elements found: { ariaLabels: [33m21[39m, roles: [33m26[39m, headings: [33m3[39m, landmarks: [33m0[39m }
/dashboard landmarks: 0
/marketing-dashboard accessibility scan: 991ms
Accessibility elements found: { ariaLabels: [33m1[39m, roles: [33m9[39m, headings: [33m4[39m, landmarks: [33m0[39m }
/marketing-dashboard landmarks: 0
/admin accessibility scan: 747ms
Accessibility elements found: { ariaLabels: [33m1[39m, roles: [33m2[39m, headings: [33m4[39m, landmarks: [33m0[39m }
/admin landmarks: 0
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-test-runner.spec.ts" timestamp="2025-05-30T02:27:11.670Z" hostname="chromium" tests="8" failures="1" skipped="0" time="89.005" errors="0">
<testcase name="Comprehensive Application Test Suite › should run complete application smoke test" classname="comprehensive-test-runner.spec.ts" time="14.949">
<system-out>
<![CDATA[🚀 Starting comprehensive application smoke test...
📄 Testing Dashboard...
✅ Dashboard: 1166ms
📄 Testing Brand Deep Dive...
✅ Brand Deep Dive: 965ms
📄 Testing Marketing Dashboard...
✅ Marketing Dashboard: 988ms
📄 Testing Executive Summary...
✅ Executive Summary: 1003ms
📄 Testing Budget...
✅ Budget: 768ms
📄 Testing AI Assistant...
✅ AI Assistant: 1014ms
🔐 Testing admin pages...
✅ Admin Dashboard: Accessible
✅ Admin Users: Accessible
✅ Admin Roles: Accessible
✅ Admin Permissions: Accessible

📊 SMOKE TEST SUMMARY:
========================
✅ Dashboard: 1166ms
✅ Brand Deep Dive: 965ms
✅ Marketing Dashboard: 988ms
✅ Executive Summary: 1003ms
✅ Budget: 768ms
✅ AI Assistant: 1014ms

🎯 Results: 6/6 tests passed
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify authentication system works correctly" classname="comprehensive-test-runner.spec.ts" time="19.165">
<failure message="comprehensive-test-runner.spec.ts:122:7 should verify authentication system works correctly" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-test-runner.spec.ts:122:7 › Comprehensive Application Test Suite › should verify authentication system works correctly 

    TimeoutError: locator.waitFor: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible


       at utils/test-helpers.ts:46

      44 |
      45 |     // Wait for the button to be visible
    > 46 |     await credentialsTab.waitFor({ state: 'visible', timeout: 10000 });
         |                          ^
      47 |     await credentialsTab.click();
      48 |     await page.waitForTimeout(1500); // Wait for tab switch animation
      49 |
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Testing authentication system...
Sign in failed: locator.waitFor: Timeout 10000ms exceeded.
Call log:
[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible[22m

    at signInWithCredentials [90m(/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/utils/test-helpers.ts:46:26[90m)[39m
    at signInAsAdmin [90m(/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/utils/test-helpers.ts:75:3[90m)[39m
    at [90m/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/comprehensive-test-runner.spec.ts:127:7 {
  name: [32m'TimeoutError'[39m,
  [[32mSymbol(step)[39m]: {
    stepId: [32m'pw:api@25'[39m,
    location: {
      file: [32m'/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts'[39m,
      line: [33m46[39m,
      column: [33m26[39m,
      function: [32m'signInWithCredentials'[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m'locator.waitFor(button >> internal:has-text="Username/Password"i)'[39m,
    apiName: [32m'locator.waitFor'[39m,
    params: {
      selector: [32m'button >> internal:has-text="Username/Password"i'[39m,
      strict: [33mtrue[39m,
      omitReturnValue: [33mtrue[39m,
      state: [32m'visible'[39m,
      timeout: [33m10000[39m
    },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@25'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1748572176497[39m,
    error: {
      message: [32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\x1B[22m\n"[39m,
      stack: [32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\x1B[22m\n"[39m +
        [32m'\n'[39m +
        [32m'    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)\n'[39m +
        [32m'    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)\n'[39m +
        [32m'    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7'[39m,
      cause: [90mundefined[39m
    }
  }
}
❌ Authentication test failed: locator.waitFor: Timeout 10000ms exceeded.
Call log:
[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible[22m

    at signInWithCredentials [90m(/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/utils/test-helpers.ts:46:26[90m)[39m
    at signInAsAdmin [90m(/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/utils/test-helpers.ts:75:3[90m)[39m
    at [90m/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/comprehensive-test-runner.spec.ts:127:7 {
  name: [32m'TimeoutError'[39m,
  [[32mSymbol(step)[39m]: {
    stepId: [32m'pw:api@25'[39m,
    location: {
      file: [32m'/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts'[39m,
      line: [33m46[39m,
      column: [33m26[39m,
      function: [32m'signInWithCredentials'[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m'locator.waitFor(button >> internal:has-text="Username/Password"i)'[39m,
    apiName: [32m'locator.waitFor'[39m,
    params: {
      selector: [32m'button >> internal:has-text="Username/Password"i'[39m,
      strict: [33mtrue[39m,
      omitReturnValue: [33mtrue[39m,
      state: [32m'visible'[39m,
      timeout: [33m10000[39m
    },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@25'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1748572176497[39m,
    error: {
      message: [32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\x1B[22m\n"[39m,
      stack: [32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\x1B[22m\n"[39m +
        [32m'\n'[39m +
        [32m'    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)\n'[39m +
        [32m'    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)\n'[39m +
        [32m'    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7'[39m,
      cause: [90mundefined[39m
    }
  }
}

[[ATTACHMENT|e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify all navigation links work" classname="comprehensive-test-runner.spec.ts" time="10.686">
<system-out>
<![CDATA[🧭 Testing navigation system...
✅ Navigation to Dashboard: Working
✅ Navigation to Marketing Dashboard: Working
✅ Navigation to Executive Summary: Working
✅ Navigation to Budget: Working
✅ Navigation to AI Assistant: Working
🎯 Navigation: 5/6 links working
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify data loading and display" classname="comprehensive-test-runner.spec.ts" time="8.295">
<system-out>
<![CDATA[📊 Testing data loading and display...
📈 Dashboard KPIs: 0 data elements found
⚠️ Dashboard KPIs: No data elements found
📈 Marketing Campaigns: 1 data elements found
✅ Marketing Campaigns: Data loaded successfully
📈 Brand Data: 0 data elements found
⚠️ Brand Data: No data elements found
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify responsive design works" classname="comprehensive-test-runner.spec.ts" time="9.735">
<system-out>
<![CDATA[📱 Testing responsive design...
✅ Desktop (1920x1080): Layout working
✅ Tablet (1024x768): Layout working
✅ Mobile (375x667): Layout working
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify accessibility standards" classname="comprehensive-test-runner.spec.ts" time="8.161">
<system-out>
<![CDATA[♿ Testing accessibility standards...
🔍 /dashboard accessibility:
  - Headings: 3
  - ARIA labels: 21
  - Landmarks: 0
⚠️ /dashboard: No landmarks found, but continuing test
✅ /dashboard: Accessibility standards met
🔍 /marketing-dashboard accessibility:
  - Headings: 4
  - ARIA labels: 1
  - Landmarks: 0
⚠️ /marketing-dashboard: No landmarks found, but continuing test
✅ /marketing-dashboard: Accessibility standards met
🔍 /admin accessibility:
  - Headings: 4
  - ARIA labels: 1
  - Landmarks: 0
⚠️ /admin: No landmarks found, but continuing test
✅ /admin: Accessibility standards met
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify performance benchmarks" classname="comprehensive-test-runner.spec.ts" time="7.697">
<system-out>
<![CDATA[⚡ Testing performance benchmarks...
✅ Dashboard: 1134ms (under 8000ms limit)
✅ Marketing Dashboard: 1005ms (under 15000ms limit)
✅ Admin Dashboard: 704ms (under 8000ms limit)
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should run final comprehensive validation" classname="comprehensive-test-runner.spec.ts" time="10.317">
<system-out>
<![CDATA[🎯 Running final comprehensive validation...
✅ Journey step: /brand-deep-dive completed successfully
✅ Journey step: /marketing-dashboard completed successfully
✅ Journey step: /executive-summary completed successfully
✅ Journey step: /admin completed successfully
🎉 Comprehensive test suite completed successfully!
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>