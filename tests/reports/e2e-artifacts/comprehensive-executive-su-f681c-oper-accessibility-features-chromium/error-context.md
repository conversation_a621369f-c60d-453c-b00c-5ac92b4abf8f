# Test info

- Name: Comprehensive Executive Summary Testing >> should have proper accessibility features
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:310:7

# Error details

```
Error: expect(received).toBe<PERSON><PERSON>r<PERSON>han(expected)

Expected: > 0
Received:   0
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:318:41
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Executive Summary":
      - /url: /executive-summary
  - main:
    - heading "Executive Summary" [level=1]
    - paragraph: Performance highlights for All Brands
    - group:
      - radio "Slide View" [checked]: Slides
      - radio "Grid View": Grid
    - button "Export PDF"
    - text: "Brand:"
    - button "Brand"
    - text: "Period:"
    - button "Month"
    - button "Quarter"
    - button "Year"
    - text: "Month:"
    - combobox: April 2025
    - text: "Currency:"
    - combobox: CAD
    - region "KPI Slides":
      - 'article "Slide 1: Total Revenue"':
        - heading "Total Revenue" [level=2]
        - paragraph: April 2025 • Slide 1 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$5,323,953 Current month value 30.6% vs Budget Budget comparison -12.8% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$21,554,437 Last Updated: 10:07 PM"
          - region "Chart Visualization":
            - text: 📊
            - heading "Chart Data Unavailable" [level=3]
            - paragraph: Time series data is not available for this KPI. The current value and comparisons are shown in the metrics panel.
        - text: Current period data only Scroll down for next KPI
        - img
      - 'article "Slide 2: Net Revenue"':
        - heading "Net Revenue" [level=2]
        - paragraph: April 2025 • Slide 2 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$4,454,872 Current month value 20.3% vs Budget Budget comparison -17.5% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$18,506,104 Last Updated: 10:07 PM"
          - region "Chart Visualization":
            - heading "Net Revenue Trend" [level=3]
            - paragraph: April 2025 • 30 data points
            - img: Apr 1 Apr 5 Apr 9 Apr 13 Apr 17 Apr 21 Apr 25 Apr 29 CA$0 CA$50,000 CA$100,000 CA$150,000 CA$200,000
            - text: "Data aggregated by day Total: CA$4,454,872"
        - text: 30 data points available Scroll down for next KPI
        - img
      - 'article "Slide 3: Gross Profit"':
        - heading "Gross Profit" [level=2]
        - paragraph: April 2025 • Slide 3 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$2,934,228 Current month value 56.4% vs Budget Budget comparison -15.2% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$11,848,239 Last Updated: 10:07 PM"
          - region "Chart Visualization":
            - text: 📊
            - heading "Chart Data Unavailable" [level=3]
            - paragraph: Time series data is not available for this KPI. The current value and comparisons are shown in the metrics panel.
        - text: Current period data only Scroll down for next KPI
        - img
      - 'article "Slide 4: Contribution Margin"':
        - heading "Contribution Margin" [level=2]
        - paragraph: April 2025 • Slide 4 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$4,032,120 Current month value 233.9% vs Budget Budget comparison -7.7% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$16,292,596 Last Updated: 10:07 PM"
          - region "Chart Visualization":
            - heading "Contribution Margin Trend" [level=3]
            - paragraph: April 2025 • 30 data points
            - img: Apr 1 Apr 5 Apr 9 Apr 13 Apr 17 Apr 21 Apr 25 Apr 29 CA$0 CA$40,000 CA$80,000 CA$120,000 CA$160,000
            - text: "Data aggregated by day Total: CA$4,032,120"
        - text: 30 data points available Scroll down for next KPI
        - img
      - 'article "Slide 5: Adspend"':
        - heading "Adspend" [level=2]
        - paragraph: April 2025 • Slide 5 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$1,097,892 Current month value 16.8% vs Budget Budget comparison 20.7% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$4,444,358 Last Updated: 10:07 PM"
          - region "Chart Visualization":
            - heading "Adspend Trend" [level=3]
            - paragraph: April 2025 • 30 data points
            - img: Apr 1 Apr 5 Apr 9 Apr 13 Apr 17 Apr 21 Apr 25 Apr 29 CA$0 CA$15,000 CA$30,000 CA$45,000 CA$60,000
            - text: "Data aggregated by day Total: CA$1,097,892"
        - text: 30 data points available
      - 'button "Go to slide 1: Total Revenue"'
      - 'button "Go to slide 2: Net Revenue"'
      - 'button "Go to slide 3: Gross Profit"'
      - 'button "Go to slide 4: Contribution Margin"'
      - 'button "Go to slide 5: Adspend"'
      - text: Scrollable slide view with 5 KPI slides
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
  218 |     if (comparisonCount > 0) {
  219 |       await takeScreenshot(page, 'executive-summary-comparisons');
  220 |     }
  221 |   });
  222 |
  223 |   test('should handle currency switching', async ({ page }) => {
  224 |     await page.goto('/executive-summary');
  225 |     await waitForPageLoad(page);
  226 |     await waitForLoadingToComplete(page);
  227 |
  228 |     // Test currency selector
  229 |     const currencySelector = page.locator('select[name*="currency"], [data-testid*="currency"]');
  230 |     
  231 |     if (await currencySelector.isVisible()) {
  232 |       await currencySelector.click();
  233 |       await page.waitForTimeout(500);
  234 |       
  235 |       const currencyOptions = page.locator('option, [role="option"]');
  236 |       const optionCount = await currencyOptions.count();
  237 |       
  238 |       if (optionCount > 1) {
  239 |         // Get current currency values before switching
  240 |         const beforeValues = await page.locator('text=/\\$[0-9,]+|€[0-9,]+|£[0-9,]+/').allTextContents();
  241 |         
  242 |         // Switch currency
  243 |         await currencyOptions.nth(1).click();
  244 |         await page.waitForTimeout(3000);
  245 |         await waitForLoadingToComplete(page);
  246 |         
  247 |         // Get currency values after switching
  248 |         const afterValues = await page.locator('text=/\\$[0-9,]+|€[0-9,]+|£[0-9,]+/').allTextContents();
  249 |         
  250 |         console.log(`Currency switched - Before: ${beforeValues.length} values, After: ${afterValues.length} values`);
  251 |         await takeScreenshot(page, 'executive-summary-currency-switched');
  252 |       }
  253 |     }
  254 |   });
  255 |
  256 |   test('should display methodology and notes if available', async ({ page }) => {
  257 |     await page.goto('/executive-summary');
  258 |     await waitForPageLoad(page);
  259 |     await waitForLoadingToComplete(page);
  260 |
  261 |     // Look for methodology or notes sections
  262 |     const methodologySection = page.locator('text=/methodology|notes|calculation|formula/i');
  263 |     const methodologyCount = await methodologySection.count();
  264 |     
  265 |     if (methodologyCount > 0) {
  266 |       // Look for expandable sections or help buttons
  267 |       const helpButtons = page.locator('button:has-text("?"), [data-testid*="help"], .help-button');
  268 |       const helpCount = await helpButtons.count();
  269 |       
  270 |       if (helpCount > 0) {
  271 |         await helpButtons.first().click();
  272 |         await page.waitForTimeout(1000);
  273 |         await takeScreenshot(page, 'executive-summary-methodology');
  274 |       }
  275 |       
  276 |       console.log(`Found ${methodologyCount} methodology elements and ${helpCount} help buttons`);
  277 |     }
  278 |   });
  279 |
  280 |   test('should be responsive across different screen sizes', async ({ page }) => {
  281 |     await page.goto('/executive-summary');
  282 |     await waitForPageLoad(page);
  283 |
  284 |     const viewports = [
  285 |       { width: 1920, height: 1080, name: 'desktop' },
  286 |       { width: 1024, height: 768, name: 'tablet' },
  287 |       { width: 375, height: 667, name: 'mobile' }
  288 |     ];
  289 |
  290 |     for (const viewport of viewports) {
  291 |       await page.setViewportSize(viewport);
  292 |       await page.waitForTimeout(1000);
  293 |       
  294 |       // Verify main elements are still visible
  295 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
  296 |       
  297 |       // Check KPI cards layout on different screens
  298 |       const kpiCards = page.locator('[data-testid*="kpi"], .kpi-card');
  299 |       const cardCount = await kpiCards.count();
  300 |       
  301 |       if (cardCount > 0) {
  302 |         // Verify cards are still visible and properly arranged
  303 |         await expect(kpiCards.first()).toBeVisible();
  304 |       }
  305 |       
  306 |       await takeScreenshot(page, `executive-summary-responsive-${viewport.name}`);
  307 |     }
  308 |   });
  309 |
  310 |   test('should have proper accessibility features', async ({ page }) => {
  311 |     await page.goto('/executive-summary');
  312 |     await waitForPageLoad(page);
  313 |
  314 |     const accessibilityInfo = await testAccessibility(page);
  315 |     
  316 |     // Verify minimum accessibility requirements
  317 |     expect(accessibilityInfo.headings).toBeGreaterThan(0);
> 318 |     expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
      |                                         ^ Error: expect(received).toBeGreaterThan(expected)
  319 |     
  320 |     console.log('Executive Summary accessibility info:', accessibilityInfo);
  321 |     await takeScreenshot(page, 'executive-summary-accessibility');
  322 |   });
  323 |
  324 |   test('should load within performance thresholds', async ({ page }) => {
  325 |     const startTime = Date.now();
  326 |     
  327 |     await page.goto('/executive-summary');
  328 |     await waitForPageLoad(page);
  329 |     await waitForLoadingToComplete(page);
  330 |     
  331 |     const loadTime = Date.now() - startTime;
  332 |     
  333 |     // Executive Summary should load within 12 seconds
  334 |     expect(loadTime).toBeLessThan(12000);
  335 |     
  336 |     console.log(`Executive Summary loaded in ${loadTime}ms`);
  337 |   });
  338 |
  339 |   test('should not have critical console errors', async ({ page }) => {
  340 |     const consoleErrors: string[] = [];
  341 |     
  342 |     page.on('console', (msg) => {
  343 |       if (msg.type() === 'error') {
  344 |         consoleErrors.push(msg.text());
  345 |       }
  346 |     });
  347 |
  348 |     await page.goto('/executive-summary');
  349 |     await waitForPageLoad(page);
  350 |     await waitForLoadingToComplete(page);
  351 |
  352 |     // Filter out acceptable errors
  353 |     const criticalErrors = consoleErrors.filter(error =>
  354 |       !error.includes('favicon') &&
  355 |       !error.includes('404') &&
  356 |       !error.includes('net::ERR_FAILED') &&
  357 |       !error.includes('ChunkLoadError') &&
  358 |       !error.includes('ResizeObserver')
  359 |     );
  360 |
  361 |     if (criticalErrors.length > 0) {
  362 |       console.log('Critical console errors found:', criticalErrors);
  363 |     }
  364 |
  365 |     // For now, log errors but don't fail the test
  366 |     // expect(criticalErrors).toHaveLength(0);
  367 |   });
  368 | });
  369 |
```