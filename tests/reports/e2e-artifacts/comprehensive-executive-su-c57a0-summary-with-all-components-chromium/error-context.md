# Test info

- Name: Comprehensive Executive Summary Testing >> should load executive summary with all components
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:20:7

# Error details

```
Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)

Locator: locator(':root')
Expected pattern: /Executive Summary|NOLK/
Received string:  "Create Next App"
Call log:
  - expect.toHaveTitle with timeout 15000ms
  - waiting for locator(':root')
    19 × locator resolved to <html lang="en">…</html>
       - unexpected value "Create Next App"

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:26:24
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Executive Summary":
      - /url: /executive-summary
  - main:
    - heading "Executive Summary" [level=1]
    - paragraph: Performance highlights for All Brands
    - group:
      - radio "Slide View" [checked]: Slides
      - radio "Grid View": Grid
    - button "Export PDF"
    - text: "Brand:"
    - button "Brand"
    - text: "Period:"
    - button "Month"
    - button "Quarter"
    - button "Year"
    - text: "Month:"
    - combobox: April 2025
    - text: "Currency:"
    - combobox: CAD
    - region "KPI Slides":
      - 'article "Slide 1: Total Revenue"':
        - heading "Total Revenue" [level=2]
        - paragraph: April 2025 • Slide 1 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$5,323,953 Current month value 30.6% vs Budget Budget comparison -12.8% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$21,554,437 Last Updated: 10:06 PM"
          - region "Chart Visualization":
            - text: 📊
            - heading "Chart Data Unavailable" [level=3]
            - paragraph: Time series data is not available for this KPI. The current value and comparisons are shown in the metrics panel.
        - text: Current period data only Scroll down for next KPI
        - img
      - 'article "Slide 2: Net Revenue"':
        - heading "Net Revenue" [level=2]
        - paragraph: April 2025 • Slide 2 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$4,454,872 Current month value 20.3% vs Budget Budget comparison -17.5% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$18,506,104 Last Updated: 10:06 PM"
          - region "Chart Visualization":
            - heading "Net Revenue Trend" [level=3]
            - paragraph: April 2025 • 30 data points
            - img: Apr 1 Apr 5 Apr 9 Apr 13 Apr 17 Apr 21 Apr 25 Apr 29 CA$0 CA$50,000 CA$100,000 CA$150,000 CA$200,000
            - text: "Data aggregated by day Total: CA$4,454,872"
        - text: 30 data points available Scroll down for next KPI
        - img
      - 'article "Slide 3: Gross Profit"':
        - heading "Gross Profit" [level=2]
        - paragraph: April 2025 • Slide 3 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$2,934,228 Current month value 56.4% vs Budget Budget comparison -15.2% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$11,848,239 Last Updated: 10:06 PM"
          - region "Chart Visualization":
            - text: 📊
            - heading "Chart Data Unavailable" [level=3]
            - paragraph: Time series data is not available for this KPI. The current value and comparisons are shown in the metrics panel.
        - text: Current period data only Scroll down for next KPI
        - img
      - 'article "Slide 4: Contribution Margin"':
        - heading "Contribution Margin" [level=2]
        - paragraph: April 2025 • Slide 4 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$4,032,120 Current month value 233.9% vs Budget Budget comparison -7.7% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$16,292,596 Last Updated: 10:06 PM"
          - region "Chart Visualization":
            - heading "Contribution Margin Trend" [level=3]
            - paragraph: April 2025 • 30 data points
            - img: Apr 1 Apr 5 Apr 9 Apr 13 Apr 17 Apr 21 Apr 25 Apr 29 CA$0 CA$40,000 CA$80,000 CA$120,000 CA$160,000
            - text: "Data aggregated by day Total: CA$4,032,120"
        - text: 30 data points available Scroll down for next KPI
        - img
      - 'article "Slide 5: Adspend"':
        - heading "Adspend" [level=2]
        - paragraph: April 2025 • Slide 5 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$1,097,892 Current month value 16.8% vs Budget Budget comparison 20.7% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$4,444,358 Last Updated: 10:06 PM"
          - region "Chart Visualization":
            - heading "Adspend Trend" [level=3]
            - paragraph: April 2025 • 30 data points
            - img: Apr 1 Apr 5 Apr 9 Apr 13 Apr 17 Apr 21 Apr 25 Apr 29 CA$0 CA$15,000 CA$30,000 CA$45,000 CA$60,000
            - text: "Data aggregated by day Total: CA$1,097,892"
        - text: 30 data points available
      - 'button "Go to slide 1: Total Revenue"'
      - 'button "Go to slide 2: Net Revenue"'
      - 'button "Go to slide 3: Gross Profit"'
      - 'button "Go to slide 4: Contribution Margin"'
      - 'button "Go to slide 5: Adspend"'
      - text: Scrollable slide view with 5 KPI slides
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 | import {
   3 |   waitForPageLoad,
   4 |   signInAsAdmin,
   5 |   takeScreenshot,
   6 |   testChartRendering,
   7 |   testFilters,
   8 |   waitForLoadingToComplete,
   9 |   testAccessibility,
   10 |   verifyPageElements,
   11 |   testModal
   12 | } from './utils/test-helpers';
   13 |
   14 | test.describe('Comprehensive Executive Summary Testing', () => {
   15 |   test.beforeEach(async ({ page }) => {
   16 |     // Sign in as admin for all tests
   17 |     await signInAsAdmin(page);
   18 |   });
   19 |
   20 |   test('should load executive summary with all components', async ({ page }) => {
   21 |     await page.goto('/executive-summary');
   22 |     await waitForPageLoad(page);
   23 |     await waitForLoadingToComplete(page);
   24 |
   25 |     // Verify page title and main elements
>  26 |     await expect(page).toHaveTitle(/Executive Summary|NOLK/);
      |                        ^ Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)
   27 |     
   28 |     // Check for main components
   29 |     const expectedElements = [
   30 |       'main, [role="main"]',
   31 |       'nav, [role="navigation"]',
   32 |       'h1, h2, [data-testid*="title"]'
   33 |     ];
   34 |     
   35 |     await verifyPageElements(page, expectedElements);
   36 |     await takeScreenshot(page, 'executive-summary-main');
   37 |   });
   38 |
   39 |   test('should display brand selector and period selector', async ({ page }) => {
   40 |     await page.goto('/executive-summary');
   41 |     await waitForPageLoad(page);
   42 |     await waitForLoadingToComplete(page);
   43 |
   44 |     // Test brand selector
   45 |     const brandSelector = page.locator('[data-testid*="brand-selector"], select[name*="brand"], .brand-selector');
   46 |     
   47 |     if (await brandSelector.isVisible()) {
   48 |       await brandSelector.click();
   49 |       await page.waitForTimeout(1000);
   50 |       
   51 |       const brandOptions = page.locator('option, [role="option"], [data-testid*="brand-option"]');
   52 |       const optionCount = await brandOptions.count();
   53 |       
   54 |       if (optionCount > 1) {
   55 |         await brandOptions.nth(1).click();
   56 |         await page.waitForTimeout(2000);
   57 |         await waitForLoadingToComplete(page);
   58 |         console.log(`Selected brand from ${optionCount} available options`);
   59 |       }
   60 |     }
   61 |
   62 |     // Test period selector
   63 |     const periodSelector = page.locator('[data-testid*="period"], select[name*="period"], .period-selector');
   64 |     
   65 |     if (await periodSelector.isVisible()) {
   66 |       await periodSelector.click();
   67 |       await page.waitForTimeout(1000);
   68 |       
   69 |       const periodOptions = page.locator('option, [role="option"]');
   70 |       const periodCount = await periodOptions.count();
   71 |       
   72 |       if (periodCount > 1) {
   73 |         await periodOptions.nth(1).click();
   74 |         await page.waitForTimeout(2000);
   75 |         await waitForLoadingToComplete(page);
   76 |         console.log(`Selected period from ${periodCount} available options`);
   77 |       }
   78 |     }
   79 |
   80 |     await takeScreenshot(page, 'executive-summary-selectors');
   81 |   });
   82 |
   83 |   test('should display executive KPI cards with proper formatting', async ({ page }) => {
   84 |     await page.goto('/executive-summary');
   85 |     await waitForPageLoad(page);
   86 |     await waitForLoadingToComplete(page);
   87 |
   88 |     // Look for executive-level KPI cards
   89 |     const kpiCards = page.locator('[data-testid*="kpi"], .kpi-card, [class*="kpi"]');
   90 |     const cardCount = await kpiCards.count();
   91 |
   92 |     if (cardCount > 0) {
   93 |       // Verify KPI cards are visible
   94 |       await expect(kpiCards.first()).toBeVisible();
   95 |       
   96 |       // Check for executive-level metrics
   97 |       const executiveMetrics = page.locator('text=/revenue|profit|margin|growth|performance/i');
   98 |       const metricsCount = await executiveMetrics.count();
   99 |       
  100 |       // Check for proper number formatting
  101 |       const currencyValues = page.locator('text=/\\$[0-9,]+|€[0-9,]+|£[0-9,]+/');
  102 |       const percentageValues = page.locator('text=/[0-9]+\\.?[0-9]*%/');
  103 |       
  104 |       const currencyCount = await currencyValues.count();
  105 |       const percentageCount = await percentageValues.count();
  106 |       
  107 |       console.log(`Found ${cardCount} KPI cards, ${metricsCount} metrics, ${currencyCount} currency values, ${percentageCount} percentages`);
  108 |     }
  109 |
  110 |     await takeScreenshot(page, 'executive-summary-kpi-cards');
  111 |   });
  112 |
  113 |   test('should switch between cards and slides view', async ({ page }) => {
  114 |     await page.goto('/executive-summary');
  115 |     await waitForPageLoad(page);
  116 |     await waitForLoadingToComplete(page);
  117 |
  118 |     // Look for view toggle buttons
  119 |     const cardsViewButton = page.locator('button:has-text("Cards"), [data-testid*="cards-view"]');
  120 |     const slidesViewButton = page.locator('button:has-text("Slides"), [data-testid*="slides-view"]');
  121 |
  122 |     // Test cards view
  123 |     if (await cardsViewButton.isVisible()) {
  124 |       await cardsViewButton.click();
  125 |       await page.waitForTimeout(1000);
  126 |       await waitForLoadingToComplete(page);
```