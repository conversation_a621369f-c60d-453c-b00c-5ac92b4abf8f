# Test info

- Name: Comprehensive Application Test Suite >> should verify responsive design works
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:230:7

# Error details

```
Error: locator.isVisible: Error: strict mode violation: locator('main, [role="main"]') resolved to 2 elements:
    1) <main data-slot="sidebar-inset" class="bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2">…</main> aka getByText('Toggle SidebarDashboardDashboardDashboardOverview of your business')
    2) <main class="flex-1 overflow-auto">…</main> aka getByRole('main').filter({ hasText: 'Toggle SidebarDashboardDashboardDashboardOverview of your business' }).getByRole('main')

Call log:
    - checking visibility of locator('main, [role="main"]')

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:248:43
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Dashboard":
      - /url: /dashboard
  - main:
    - heading "Dashboard" [level=1]
    - heading "Overview of your business performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Countries"
    - button "Sales Channels"
    - text: Master KPI Comparison Compare 'Gross Revenue' across selected brands using a line chart.
    - combobox "Select KPI": Gross Revenue
    - img: 2/28/2025 3/31/2025 4/30/2025 0 550K 1M 2M 2M
    - text: Brands Click to toggle. Sorted by latest KPI.
    - list:
      - button "Toggle visibility for Revant" [pressed]: Revant
      - button "Toggle visibility for Qalo" [pressed]: Qalo
      - button "Toggle visibility for Ergonofis" [pressed]: Ergonofis
      - button "Toggle visibility for Opposite Wall" [pressed]: Opposite Wall
      - button "Toggle visibility for Homesick" [pressed]: Homesick
      - button "Toggle visibility for Gravity" [pressed]: Gravity
      - button "Toggle visibility for Freakmount" [pressed]: Freakmount
      - button "Toggle visibility for MiHIGH" [pressed]: MiHIGH
      - button "Toggle visibility for Rose Boreal" [pressed]: Rose Boreal
      - button "Toggle visibility for Rachel" [pressed]: Rachel
      - button "Toggle visibility for Kana" [pressed]: Kana
      - button "Toggle visibility for Arctic Tumblers" [pressed]: Arctic Tumblers
      - button "Toggle visibility for Loctote" [pressed]: Loctote
      - button "Toggle visibility for Proper Pour" [pressed]: Proper Pour
      - button "Toggle visibility for Love Your Melon" [pressed]: Love Your Melon
      - button "Toggle visibility for Wolf & Grizzly" [pressed]: Wolf & Grizzly
      - button "Toggle visibility for Go Green" [pressed]: Go Green
      - button "Toggle visibility for Corretto" [pressed]: Corretto
      - button "Toggle visibility for Alex Bottle" [pressed]: Alex Bottle
    - heading "Key Performance Indicators" [level=3]
    - text: Revenue
    - button "KPI Definition"
    - text: $17,245,998.18
    - img: 2/28/2025 3/31/2025 4/30/2025 0 2,000,000 4,000,000 6,000,000 8,000,000
    - text: Discount
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,916,224.18
    - img: 2/28/2025 3/31/2025 4/30/2025 0 200,000 400,000 600,000 800,000
    - text: Refunds
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $614,536.13
    - img: 2/28/2025 3/31/2025 4/30/2025 0 70,000 140,000 210,000 280,000
    - text: Net Revenue
    - button "KPI Definition"
    - text: $14,715,237.88
    - img: 2/28/2025 3/31/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
    - text: Landed Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,709,657.76
    - img: 2/28/2025 3/31/2025 4/30/2025 0 300,000 600,000 900,000 1,200,000
    - text: Fulfillment Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,167,088.33
    - img: 2/28/2025 3/31/2025 4/30/2025 0 250,000 500,000 750,000 1,000,000
    - text: Transaction Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,034,384.19
    - img: 2/28/2025 3/31/2025 4/30/2025 0 95,000 190,000 285,000 380,000
    - text: Gross Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $9,520,150.47
    - img: 2/28/2025 3/31/2025 4/30/2025 0 900,000 1,800,000 2,700,000 3,600,000
    - text: Adspend
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $3,424,904.20
    - img: 2/28/2025 3/31/2025 4/30/2025 0 350,000 700,000 1,050,000 1,400,000
    - text: Contribution Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $12,945,054.68
    - img: 2/28/2025 3/31/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
  148 |
  149 |   test('should verify all navigation links work', async ({ page }) => {
  150 |     console.log('🧭 Testing navigation system...');
  151 |     
  152 |     await page.goto('/dashboard');
  153 |     await waitForPageLoad(page);
  154 |     
  155 |     // Test main navigation
  156 |     const navItems = [
  157 |       'Dashboard',
  158 |       'Brand Deep Dive', 
  159 |       'Marketing Dashboard',
  160 |       'Executive Summary',
  161 |       'Budget',
  162 |       'AI Assistant'
  163 |     ];
  164 |     
  165 |     let workingNavItems = 0;
  166 |     
  167 |     for (const item of navItems) {
  168 |       const navLink = page.locator(`nav a:has-text("${item}")`);
  169 |       
  170 |       if (await navLink.isVisible()) {
  171 |         try {
  172 |           await navLink.click();
  173 |           await waitForPageLoad(page);
  174 |           
  175 |           // Verify navigation worked
  176 |           const currentUrl = page.url();
  177 |           const expectedPath = item.toLowerCase().replace(' ', '-');
  178 |           
  179 |           if (currentUrl.includes(expectedPath) || currentUrl.includes('/dashboard')) {
  180 |             workingNavItems++;
  181 |             console.log(`✅ Navigation to ${item}: Working`);
  182 |           }
  183 |         } catch (error) {
  184 |           console.log(`❌ Navigation to ${item}: Failed`);
  185 |         }
  186 |       }
  187 |     }
  188 |     
  189 |     console.log(`🎯 Navigation: ${workingNavItems}/${navItems.length} links working`);
  190 |     expect(workingNavItems).toBeGreaterThanOrEqual(navItems.length * 0.8);
  191 |     
  192 |     await takeScreenshot(page, 'navigation-test');
  193 |   });
  194 |
  195 |   test('should verify data loading and display', async ({ page }) => {
  196 |     console.log('📊 Testing data loading and display...');
  197 |     
  198 |     const dataPages = [
  199 |       { name: 'Dashboard KPIs', url: '/dashboard', selector: '[data-testid*="kpi"], .kpi-card' },
  200 |       { name: 'Marketing Campaigns', url: '/marketing-dashboard', selector: 'table, [data-testid*="campaign"]' },
  201 |       { name: 'Brand Data', url: '/brand-deep-dive', selector: '[data-testid*="brand"], .brand-data' }
  202 |     ];
  203 |     
  204 |     for (const dataPage of dataPages) {
  205 |       try {
  206 |         await page.goto(dataPage.url);
  207 |         await waitForPageLoad(page);
  208 |         await waitForLoadingToComplete(page);
  209 |         
  210 |         // Check for data elements
  211 |         const dataElements = page.locator(dataPage.selector);
  212 |         const elementCount = await dataElements.count();
  213 |         
  214 |         console.log(`📈 ${dataPage.name}: ${elementCount} data elements found`);
  215 |         
  216 |         if (elementCount > 0) {
  217 |           console.log(`✅ ${dataPage.name}: Data loaded successfully`);
  218 |         } else {
  219 |           console.log(`⚠️ ${dataPage.name}: No data elements found`);
  220 |         }
  221 |         
  222 |       } catch (error) {
  223 |         console.log(`❌ ${dataPage.name}: Failed to load - ${error}`);
  224 |       }
  225 |     }
  226 |     
  227 |     await takeScreenshot(page, 'data-loading-test');
  228 |   });
  229 |
  230 |   test('should verify responsive design works', async ({ page }) => {
  231 |     console.log('📱 Testing responsive design...');
  232 |     
  233 |     const viewports = [
  234 |       { width: 1920, height: 1080, name: 'Desktop' },
  235 |       { width: 1024, height: 768, name: 'Tablet' },
  236 |       { width: 375, height: 667, name: 'Mobile' }
  237 |     ];
  238 |     
  239 |     await page.goto('/dashboard');
  240 |     await waitForPageLoad(page);
  241 |     
  242 |     for (const viewport of viewports) {
  243 |       await page.setViewportSize(viewport);
  244 |       await page.waitForTimeout(1000);
  245 |       
  246 |       // Verify main content is visible
  247 |       const mainContent = page.locator('main, [role="main"]');
> 248 |       const isVisible = await mainContent.isVisible();
      |                                           ^ Error: locator.isVisible: Error: strict mode violation: locator('main, [role="main"]') resolved to 2 elements:
  249 |       
  250 |       if (isVisible) {
  251 |         console.log(`✅ ${viewport.name} (${viewport.width}x${viewport.height}): Layout working`);
  252 |       } else {
  253 |         console.log(`❌ ${viewport.name}: Layout broken`);
  254 |       }
  255 |       
  256 |       await takeScreenshot(page, `responsive-${viewport.name.toLowerCase()}`);
  257 |     }
  258 |   });
  259 |
  260 |   test('should verify accessibility standards', async ({ page }) => {
  261 |     console.log('♿ Testing accessibility standards...');
  262 |     
  263 |     const accessibilityPages = ['/dashboard', '/marketing-dashboard', '/admin'];
  264 |     
  265 |     for (const url of accessibilityPages) {
  266 |       await page.goto(url);
  267 |       await waitForPageLoad(page);
  268 |       
  269 |       const accessibilityInfo = await testAccessibility(page);
  270 |       
  271 |       console.log(`🔍 ${url} accessibility:`);
  272 |       console.log(`  - Headings: ${accessibilityInfo.headings}`);
  273 |       console.log(`  - ARIA labels: ${accessibilityInfo.ariaLabels}`);
  274 |       console.log(`  - Landmarks: ${accessibilityInfo.landmarks}`);
  275 |       
  276 |       // Verify minimum accessibility requirements
  277 |       expect(accessibilityInfo.headings).toBeGreaterThan(0);
  278 |       expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
  279 |       
  280 |       console.log(`✅ ${url}: Accessibility standards met`);
  281 |     }
  282 |     
  283 |     await takeScreenshot(page, 'accessibility-test');
  284 |   });
  285 |
  286 |   test('should verify performance benchmarks', async ({ page }) => {
  287 |     console.log('⚡ Testing performance benchmarks...');
  288 |     
  289 |     const performancePages = [
  290 |       { name: 'Dashboard', url: '/dashboard', maxTime: 8000 },
  291 |       { name: 'Marketing Dashboard', url: '/marketing-dashboard', maxTime: 15000 },
  292 |       { name: 'Admin Dashboard', url: '/admin', maxTime: 8000 }
  293 |     ];
  294 |     
  295 |     for (const perfPage of performancePages) {
  296 |       const startTime = Date.now();
  297 |       
  298 |       await page.goto(perfPage.url);
  299 |       await waitForPageLoad(page);
  300 |       await waitForLoadingToComplete(page);
  301 |       
  302 |       const loadTime = Date.now() - startTime;
  303 |       
  304 |       if (loadTime < perfPage.maxTime) {
  305 |         console.log(`✅ ${perfPage.name}: ${loadTime}ms (under ${perfPage.maxTime}ms limit)`);
  306 |       } else {
  307 |         console.log(`⚠️ ${perfPage.name}: ${loadTime}ms (over ${perfPage.maxTime}ms limit)`);
  308 |       }
  309 |       
  310 |       // Don't fail the test for performance, just log
  311 |       // expect(loadTime).toBeLessThan(perfPage.maxTime);
  312 |     }
  313 |     
  314 |     await takeScreenshot(page, 'performance-test');
  315 |   });
  316 |
  317 |   test('should run final comprehensive validation', async ({ page }) => {
  318 |     console.log('🎯 Running final comprehensive validation...');
  319 |     
  320 |     // Test critical user journey
  321 |     await page.goto('/dashboard');
  322 |     await waitForPageLoad(page);
  323 |     await waitForLoadingToComplete(page);
  324 |     
  325 |     // Navigate through key pages
  326 |     const journey = [
  327 |       '/brand-deep-dive',
  328 |       '/marketing-dashboard', 
  329 |       '/executive-summary',
  330 |       '/admin'
  331 |     ];
  332 |     
  333 |     for (const url of journey) {
  334 |       await page.goto(url);
  335 |       await waitForPageLoad(page);
  336 |       await waitForLoadingToComplete(page);
  337 |       
  338 |       // Verify page loaded without errors
  339 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
  340 |       
  341 |       console.log(`✅ Journey step: ${url} completed successfully`);
  342 |     }
  343 |     
  344 |     // Return to dashboard
  345 |     await page.goto('/dashboard');
  346 |     await waitForPageLoad(page);
  347 |     
  348 |     console.log('🎉 Comprehensive test suite completed successfully!');
```