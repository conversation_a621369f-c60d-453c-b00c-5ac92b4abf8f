# Test info

- Name: Comprehensive Error Handling and Edge Cases >> should handle 404 errors gracefully
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:15:7

# Error details

```
Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:39:50
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
    - link "invalid-campaign-id":
      - /url: /marketing-dashboard/invalid-campaign-id
  - main:
    - link "Back to Dashboard":
      - /url: /marketing-dashboard
      - button "Back to Dashboard"
    - heading "Campaign Details" [level=1]
    - heading "Unable to Load Campaign" [level=3]
    - paragraph: "Could not load campaign details: Failed to fetch campaign details"
- alert
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 1 Issue
- button "Collapse issues badge":
  - img
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 | import {
   3 |   waitForPageLoad,
   4 |   signInAsAdmin,
   5 |   takeScreenshot,
   6 |   waitForLoadingToComplete
   7 | } from './utils/test-helpers';
   8 |
   9 | test.describe('Comprehensive Error Handling and Edge Cases', () => {
   10 |   test.beforeEach(async ({ page }) => {
   11 |     // Sign in as admin for most tests
   12 |     await signInAsAdmin(page);
   13 |   });
   14 |
   15 |   test('should handle 404 errors gracefully', async ({ page }) => {
   16 |     const invalidUrls = [
   17 |       '/nonexistent-page',
   18 |       '/dashboard/invalid',
   19 |       '/admin/nonexistent',
   20 |       '/marketing-dashboard/invalid-campaign-id',
   21 |       '/brand-deep-dive/invalid'
   22 |     ];
   23 |
   24 |     for (const url of invalidUrls) {
   25 |       await page.goto(url);
   26 |       await waitForPageLoad(page);
   27 |
   28 |       // Should either show 404 page or redirect to valid page
   29 |       const currentUrl = page.url();
   30 |       console.log(`${url} -> ${currentUrl}`);
   31 |
   32 |       // Check for 404 page elements or redirect
   33 |       const is404 = currentUrl.includes('404') ||
   34 |                    await page.locator('text=/404|not found|page not found/i').isVisible();
   35 |       const isRedirect = !currentUrl.includes(url.split('/')[1]);
   36 |       const isValidPage = currentUrl.includes('/dashboard') || currentUrl.includes('/auth');
   37 |
   38 |       // Either shows 404, redirects, or shows a valid page (some routes might be valid)
>  39 |       expect(is404 || isRedirect || isValidPage).toBe(true);
      |                                                  ^ Error: expect(received).toBe(expected) // Object.is equality
   40 |       await takeScreenshot(page, `error-404-${url.replace(/\//g, '-')}`);
   41 |     }
   42 |   });
   43 |
   44 |   test('should handle network errors and offline scenarios', async ({ page }) => {
   45 |     await page.goto('/dashboard');
   46 |     await waitForPageLoad(page);
   47 |
   48 |     // Simulate network failure
   49 |     await page.context().setOffline(true);
   50 |
   51 |     // Try to navigate to another page
   52 |     try {
   53 |       await page.goto('/marketing-dashboard');
   54 |       await page.waitForTimeout(3000);
   55 |     } catch (error) {
   56 |       console.log('Expected network error:', error.message);
   57 |     }
   58 |
   59 |     // Should show offline message or handle gracefully
   60 |     const offlineIndicator = page.locator('text=/offline|network error|connection failed/i');
   61 |     const isOfflineHandled = await offlineIndicator.isVisible();
   62 |
   63 |     console.log(`Offline handling: ${isOfflineHandled ? 'Detected' : 'Not detected'}`);
   64 |     await takeScreenshot(page, 'network-offline');
   65 |
   66 |     // Restore network
   67 |     await page.context().setOffline(false);
   68 |     await page.waitForTimeout(2000);
   69 |
   70 |     // Should recover when network is restored
   71 |     await page.reload();
   72 |     await waitForPageLoad(page);
   73 |     await takeScreenshot(page, 'network-restored');
   74 |   });
   75 |
   76 |   test('should handle API errors gracefully', async ({ page }) => {
   77 |     // Intercept API calls and return errors
   78 |     await page.route('**/api/dashboard/**', route => {
   79 |       route.fulfill({
   80 |         status: 500,
   81 |         contentType: 'application/json',
   82 |         body: JSON.stringify({ error: 'Internal Server Error' })
   83 |       });
   84 |     });
   85 |
   86 |     await page.goto('/dashboard');
   87 |     await waitForPageLoad(page);
   88 |     await waitForLoadingToComplete(page);
   89 |
   90 |     // Should show error message or fallback content
   91 |     const errorMessage = page.locator('text=/error|failed|something went wrong/i');
   92 |     const hasErrorHandling = await errorMessage.isVisible();
   93 |
   94 |     console.log(`API error handling: ${hasErrorHandling ? 'Detected' : 'Not detected'}`);
   95 |     await takeScreenshot(page, 'api-error-500');
   96 |   });
   97 |
   98 |   test('should handle authentication errors', async ({ page }) => {
   99 |     // Test without authentication
  100 |     const unauthenticatedPage = await page.context().newPage();
  101 |
  102 |     const protectedPages = [
  103 |       '/dashboard',
  104 |       '/marketing-dashboard',
  105 |       '/brand-deep-dive',
  106 |       '/executive-summary',
  107 |       '/budget',
  108 |       '/ai-assistant',
  109 |       '/admin'
  110 |     ];
  111 |
  112 |     for (const url of protectedPages) {
  113 |       await unauthenticatedPage.goto(url);
  114 |       await waitForPageLoad(unauthenticatedPage);
  115 |
  116 |       // Should redirect to sign-in page or stay on dashboard if already authenticated
  117 |       const currentUrl = unauthenticatedPage.url();
  118 |       const isOnSignIn = currentUrl.includes('/auth/signin');
  119 |       const isOnDashboard = currentUrl.includes('/dashboard');
  120 |
  121 |       // Either redirected to sign-in or stayed on dashboard (if session is still valid)
  122 |       expect(isOnSignIn || isOnDashboard).toBe(true);
  123 |
  124 |       console.log(`${url} -> redirected to sign-in`);
  125 |     }
  126 |
  127 |     await takeScreenshot(unauthenticatedPage, 'auth-redirect');
  128 |     await unauthenticatedPage.close();
  129 |   });
  130 |
  131 |   test('should handle form validation errors', async ({ page }) => {
  132 |     await page.goto('/admin/users');
  133 |     await waitForPageLoad(page);
  134 |
  135 |     // Look for add user button
  136 |     const addButton = page.locator('button:has-text("Add"), button:has-text("Create")');
  137 |
  138 |     if (await addButton.isVisible()) {
  139 |       await addButton.click();
```