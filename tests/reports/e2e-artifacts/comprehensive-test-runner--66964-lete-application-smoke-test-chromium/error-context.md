# Test info

- Name: Comprehensive Application Test Suite >> should run complete application smoke test
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:16:7

# Error details

```
Error: expect(received).toBeGreaterThanOrEqual(expected)

Expected: >= 0.8
Received:    0
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:116:38
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "Admin":
      - /url: /admin
      - img
      - text: Admin
- list:
  - listitem:
    - link "Back to Dashboard":
      - /url: /dashboard
      - img
      - text: Back to Dashboard
  - listitem:
    - link "Dashboard":
      - /url: /admin
      - img
      - text: Dashboard
  - listitem:
    - link "Users":
      - /url: /admin/users
      - img
      - text: Users
  - listitem:
    - link "Roles":
      - /url: /admin/roles
      - img
      - text: Roles
  - listitem:
    - link "Permissions":
      - /url: /admin/permissions
      - img
      - text: Permissions
  - listitem:
    - link "Groups":
      - /url: /admin/groups
      - img
      - text: Groups
  - listitem:
    - link "Brands":
      - /url: /admin/brands
      - img
      - text: Brands
  - listitem:
    - link "DB Structure":
      - /url: /admin/db-structure
      - img
      - text: DB Structure
  - listitem:
    - link "Backups":
      - /url: /admin/backups
      - img
      - text: Backups
- list:
  - listitem:
    - link "Main Dashboard":
      - /url: /dashboard
      - img
      - text: Main Dashboard
  - listitem:
    - link "Settings":
      - /url: /admin/settings
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Admin":
      - /url: /admin
    - link "Permissions":
      - /url: /admin/permissions
  - main:
    - region "Notifications alt+T"
    - heading "Permission Management" [level=1]
    - heading "Manage application permissions" [level=2]
    - button "Create Permission"
    - button "Columns":
      - img
      - text: Columns
    - table:
      - rowgroup:
        - row "ID Name Resource Description Allowed Sales Channels Actions":
          - cell "ID"
          - cell "Name"
          - cell "Resource"
          - cell "Description"
          - cell "Allowed Sales Channels"
          - cell "Actions"
      - rowgroup:
        - row "2 Amazon manager brands Manage amazon brands only - Edit Delete":
          - cell "2"
          - cell "Amazon manager"
          - cell "brands"
          - cell "Manage amazon brands only"
          - cell "-"
          - cell "Edit Delete":
            - button "Edit"
            - button "Delete"
        - row "1 view brands Manage brands - Edit Delete":
          - cell "1"
          - cell "view"
          - cell "brands"
          - cell "Manage brands"
          - cell "-"
          - cell "Edit Delete":
            - button "Edit"
            - button "Delete"
    - text: 2 row(s).
    - paragraph: Rows per page
    - combobox: "50"
    - text: Page 1 of 1
    - button "Go to first page" [disabled]:
      - text: Go to first page
      - img
    - button "Go to previous page" [disabled]:
      - text: Go to previous page
      - img
    - button "Go to next page" [disabled]:
      - text: Go to next page
      - img
    - button "Go to last page" [disabled]:
      - text: Go to last page
      - img
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   16 |   test('should run complete application smoke test', async ({ page }) => {
   17 |     console.log('🚀 Starting comprehensive application smoke test...');
   18 |     
   19 |     // Test all main application pages
   20 |     const mainPages = [
   21 |       { name: 'Dashboard', url: '/dashboard' },
   22 |       { name: 'Brand Deep Dive', url: '/brand-deep-dive' },
   23 |       { name: 'Marketing Dashboard', url: '/marketing-dashboard' },
   24 |       { name: 'Executive Summary', url: '/executive-summary' },
   25 |       { name: 'Budget', url: '/budget' },
   26 |       { name: 'AI Assistant', url: '/ai-assistant' }
   27 |     ];
   28 |
   29 |     const results = [];
   30 |
   31 |     for (const pageInfo of mainPages) {
   32 |       console.log(`📄 Testing ${pageInfo.name}...`);
   33 |       
   34 |       try {
   35 |         const startTime = Date.now();
   36 |         
   37 |         // Navigate to page
   38 |         await page.goto(pageInfo.url);
   39 |         await waitForPageLoad(page);
   40 |         await waitForLoadingToComplete(page);
   41 |         
   42 |         const loadTime = Date.now() - startTime;
   43 |         
   44 |         // Verify page loaded successfully
   45 |         await expect(page.locator('main, [role="main"]')).toBeVisible();
   46 |         
   47 |         // Check for critical errors
   48 |         const errorElements = page.locator('text=/error|failed|something went wrong/i');
   49 |         const hasErrors = await errorElements.count() > 0;
   50 |         
   51 |         // Take screenshot
   52 |         await takeScreenshot(page, `smoke-test-${pageInfo.name.toLowerCase().replace(' ', '-')}`);
   53 |         
   54 |         results.push({
   55 |           page: pageInfo.name,
   56 |           url: pageInfo.url,
   57 |           loadTime,
   58 |           hasErrors,
   59 |           status: 'PASS'
   60 |         });
   61 |         
   62 |         console.log(`✅ ${pageInfo.name}: ${loadTime}ms`);
   63 |         
   64 |       } catch (error) {
   65 |         console.log(`❌ ${pageInfo.name}: FAILED - ${error}`);
   66 |         results.push({
   67 |           page: pageInfo.name,
   68 |           url: pageInfo.url,
   69 |           loadTime: -1,
   70 |           hasErrors: true,
   71 |           status: 'FAIL',
   72 |           error: error.toString()
   73 |         });
   74 |       }
   75 |     }
   76 |
   77 |     // Test admin pages
   78 |     console.log('🔐 Testing admin pages...');
   79 |     const adminPages = [
   80 |       { name: 'Admin Dashboard', url: '/admin' },
   81 |       { name: 'Admin Users', url: '/admin/users' },
   82 |       { name: 'Admin Roles', url: '/admin/roles' },
   83 |       { name: 'Admin Permissions', url: '/admin/permissions' }
   84 |     ];
   85 |
   86 |     for (const adminPage of adminPages) {
   87 |       try {
   88 |         await page.goto(adminPage.url);
   89 |         await waitForPageLoad(page);
   90 |         
   91 |         // Verify admin access
   92 |         expect(page.url()).toContain('/admin');
   93 |         expect(page.url()).not.toContain('/auth/signin');
   94 |         
   95 |         await takeScreenshot(page, `smoke-test-${adminPage.name.toLowerCase().replace(' ', '-')}`);
   96 |         console.log(`✅ ${adminPage.name}: Accessible`);
   97 |         
   98 |       } catch (error) {
   99 |         console.log(`❌ ${adminPage.name}: FAILED - ${error}`);
  100 |       }
  101 |     }
  102 |
  103 |     // Print summary
  104 |     console.log('\n📊 SMOKE TEST SUMMARY:');
  105 |     console.log('========================');
  106 |     results.forEach(result => {
  107 |       const status = result.status === 'PASS' ? '✅' : '❌';
  108 |       console.log(`${status} ${result.page}: ${result.loadTime}ms`);
  109 |     });
  110 |
  111 |     const passedTests = results.filter(r => r.status === 'PASS').length;
  112 |     const totalTests = results.length;
  113 |     console.log(`\n🎯 Results: ${passedTests}/${totalTests} tests passed`);
  114 |     
  115 |     // Ensure at least 80% of tests pass
> 116 |     expect(passedTests / totalTests).toBeGreaterThanOrEqual(0.8);
      |                                      ^ Error: expect(received).toBeGreaterThanOrEqual(expected)
  117 |   });
  118 |
  119 |   test('should verify authentication system works correctly', async ({ page }) => {
  120 |     console.log('🔐 Testing authentication system...');
  121 |     
  122 |     // Test sign out
  123 |     await page.goto('/dashboard');
  124 |     await waitForPageLoad(page);
  125 |     
  126 |     // Look for user menu or sign out option
  127 |     const userMenu = page.locator('[data-testid="user-menu"], .user-menu, button:has-text("Sign out")');
  128 |     
  129 |     if (await userMenu.isVisible()) {
  130 |       await userMenu.click();
  131 |       await page.waitForTimeout(1000);
  132 |       
  133 |       const signOutButton = page.locator('button:has-text("Sign out"), [data-testid="sign-out"]');
  134 |       if (await signOutButton.isVisible()) {
  135 |         await signOutButton.click();
  136 |         await page.waitForURL('**/auth/signin');
  137 |         console.log('✅ Sign out: Working');
  138 |       }
  139 |     }
  140 |     
  141 |     // Test sign in again
  142 |     await signInAsAdmin(page);
  143 |     expect(page.url()).toContain('/dashboard');
  144 |     console.log('✅ Sign in: Working');
  145 |     
  146 |     await takeScreenshot(page, 'auth-system-test');
  147 |   });
  148 |
  149 |   test('should verify all navigation links work', async ({ page }) => {
  150 |     console.log('🧭 Testing navigation system...');
  151 |     
  152 |     await page.goto('/dashboard');
  153 |     await waitForPageLoad(page);
  154 |     
  155 |     // Test main navigation
  156 |     const navItems = [
  157 |       'Dashboard',
  158 |       'Brand Deep Dive', 
  159 |       'Marketing Dashboard',
  160 |       'Executive Summary',
  161 |       'Budget',
  162 |       'AI Assistant'
  163 |     ];
  164 |     
  165 |     let workingNavItems = 0;
  166 |     
  167 |     for (const item of navItems) {
  168 |       const navLink = page.locator(`nav a:has-text("${item}")`);
  169 |       
  170 |       if (await navLink.isVisible()) {
  171 |         try {
  172 |           await navLink.click();
  173 |           await waitForPageLoad(page);
  174 |           
  175 |           // Verify navigation worked
  176 |           const currentUrl = page.url();
  177 |           const expectedPath = item.toLowerCase().replace(' ', '-');
  178 |           
  179 |           if (currentUrl.includes(expectedPath) || currentUrl.includes('/dashboard')) {
  180 |             workingNavItems++;
  181 |             console.log(`✅ Navigation to ${item}: Working`);
  182 |           }
  183 |         } catch (error) {
  184 |           console.log(`❌ Navigation to ${item}: Failed`);
  185 |         }
  186 |       }
  187 |     }
  188 |     
  189 |     console.log(`🎯 Navigation: ${workingNavItems}/${navItems.length} links working`);
  190 |     expect(workingNavItems).toBeGreaterThanOrEqual(navItems.length * 0.8);
  191 |     
  192 |     await takeScreenshot(page, 'navigation-test');
  193 |   });
  194 |
  195 |   test('should verify data loading and display', async ({ page }) => {
  196 |     console.log('📊 Testing data loading and display...');
  197 |     
  198 |     const dataPages = [
  199 |       { name: 'Dashboard KPIs', url: '/dashboard', selector: '[data-testid*="kpi"], .kpi-card' },
  200 |       { name: 'Marketing Campaigns', url: '/marketing-dashboard', selector: 'table, [data-testid*="campaign"]' },
  201 |       { name: 'Brand Data', url: '/brand-deep-dive', selector: '[data-testid*="brand"], .brand-data' }
  202 |     ];
  203 |     
  204 |     for (const dataPage of dataPages) {
  205 |       try {
  206 |         await page.goto(dataPage.url);
  207 |         await waitForPageLoad(page);
  208 |         await waitForLoadingToComplete(page);
  209 |         
  210 |         // Check for data elements
  211 |         const dataElements = page.locator(dataPage.selector);
  212 |         const elementCount = await dataElements.count();
  213 |         
  214 |         console.log(`📈 ${dataPage.name}: ${elementCount} data elements found`);
  215 |         
  216 |         if (elementCount > 0) {
```