# Test info

- Name: Comprehensive AI Assistant Testing >> should load AI assistant page with all components
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:129:7

# Error details

```
Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)

Locator: locator(':root')
Expected pattern: /AI Assistant|NOLK/
Received string:  "Create Next App"
Call log:
  - expect.toHaveTitle with timeout 15000ms
  - waiting for locator(':root')
    19 × locator resolved to <html lang="en">…</html>
       - unexpected value "Create Next App"

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:135:24
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Ai Assistant":
      - /url: /ai-assistant
  - main:
    - heading "AI Assistant" [level=1]
    - heading "Get instant answers and insights from your data" [level=2]
    - text: AI Assistant Get instant answers and insights from your data
    - paragraph: Ask questions about your business data, generate reports, or get recommendations based on your e-commerce performance.
    - text: Chat with NOLK AI Your AI-powered business assistant
    - paragraph: Hello! I'm your NOLK AI assistant. How can I help you today? You can ask me about your business data, such as 'Show me the gross revenue for the last 30 days' or 'Compare net revenue across brands for the last quarter'.
    - textbox "Ask a question..."
    - button "Send" [disabled]
    - button "Show gross revenue"
    - button "Compare brands"
    - button "Margin trends"
    - button "Adspend analysis"
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   35 |     ];
   36 |     
   37 |     await verifyPageElements(page, expectedElements);
   38 |     await takeScreenshot(page, 'budget-page-main');
   39 |   });
   40 |
   41 |   test('should display budget data and charts', async ({ page }) => {
   42 |     await page.goto('/budget');
   43 |     await waitForPageLoad(page);
   44 |     await waitForLoadingToComplete(page);
   45 |
   46 |     // Test budget data table
   47 |     const tableInfo = await testDataTable(page);
   48 |     if (tableInfo.headerCount > 0) {
   49 |       console.log(`Budget table has ${tableInfo.headerCount} headers and ${tableInfo.rowCount} rows`);
   50 |     }
   51 |
   52 |     // Test budget charts
   53 |     const charts = await testChartRendering(page);
   54 |     
   55 |     // Look for budget-specific elements
   56 |     const budgetElements = page.locator('text=/budget|allocated|spent|remaining|variance/i');
   57 |     const budgetCount = await budgetElements.count();
   58 |     console.log(`Found ${budgetCount} budget-related elements`);
   59 |
   60 |     await takeScreenshot(page, 'budget-data-charts');
   61 |   });
   62 |
   63 |   test('should handle budget filters and brand selection', async ({ page }) => {
   64 |     await page.goto('/budget');
   65 |     await waitForPageLoad(page);
   66 |     await waitForLoadingToComplete(page);
   67 |
   68 |     // Test filter functionality
   69 |     const activeFilters = await testFilters(page);
   70 |     console.log(`Found ${activeFilters.length} active filters`);
   71 |
   72 |     // Test brand filter for budget
   73 |     const brandFilter = page.locator('select[name*="brand"], [data-testid*="brand-filter"]');
   74 |     if (await brandFilter.isVisible()) {
   75 |       await brandFilter.click();
   76 |       await page.waitForTimeout(1000);
   77 |       
   78 |       const options = page.locator('option, [role="option"]');
   79 |       const optionCount = await options.count();
   80 |       
   81 |       if (optionCount > 1) {
   82 |         await options.nth(1).click();
   83 |         await page.waitForTimeout(2000);
   84 |         await waitForLoadingToComplete(page);
   85 |         await takeScreenshot(page, 'budget-brand-filter-applied');
   86 |       }
   87 |     }
   88 |   });
   89 |
   90 |   test('should test budget API endpoint', async ({ page }) => {
   91 |     await page.goto('/budget');
   92 |     await waitForPageLoad(page);
   93 |
   94 |     // Test budget API
   95 |     try {
   96 |       const response = await testApiEndpoint(page, '/api/budget');
   97 |       console.log(`Budget API response: ${response.status()}`);
   98 |     } catch (error) {
   99 |       console.log(`Budget API failed: ${error}`);
  100 |     }
  101 |   });
  102 |
  103 |   test('should be responsive on different screen sizes', async ({ page }) => {
  104 |     await page.goto('/budget');
  105 |     await waitForPageLoad(page);
  106 |
  107 |     const viewports = [
  108 |       { width: 1920, height: 1080, name: 'desktop' },
  109 |       { width: 1024, height: 768, name: 'tablet' },
  110 |       { width: 375, height: 667, name: 'mobile' }
  111 |     ];
  112 |
  113 |     for (const viewport of viewports) {
  114 |       await page.setViewportSize(viewport);
  115 |       await page.waitForTimeout(1000);
  116 |       
  117 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
  118 |       await takeScreenshot(page, `budget-responsive-${viewport.name}`);
  119 |     }
  120 |   });
  121 | });
  122 |
  123 | test.describe('Comprehensive AI Assistant Testing', () => {
  124 |   test.beforeEach(async ({ page }) => {
  125 |     // Sign in as admin for all tests
  126 |     await signInAsAdmin(page);
  127 |   });
  128 |
  129 |   test('should load AI assistant page with all components', async ({ page }) => {
  130 |     await page.goto('/ai-assistant');
  131 |     await waitForPageLoad(page);
  132 |     await waitForLoadingToComplete(page);
  133 |
  134 |     // Verify page title and main elements
> 135 |     await expect(page).toHaveTitle(/AI Assistant|NOLK/);
      |                        ^ Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)
  136 |     
  137 |     // Check for main components
  138 |     const expectedElements = [
  139 |       'main, [role="main"]',
  140 |       'nav, [role="navigation"]',
  141 |       'h1, h2, [data-testid*="title"]'
  142 |     ];
  143 |     
  144 |     await verifyPageElements(page, expectedElements);
  145 |     await takeScreenshot(page, 'ai-assistant-main');
  146 |   });
  147 |
  148 |   test('should display chat interface', async ({ page }) => {
  149 |     await page.goto('/ai-assistant');
  150 |     await waitForPageLoad(page);
  151 |     await waitForLoadingToComplete(page);
  152 |
  153 |     // Look for chat interface elements
  154 |     const chatElements = [
  155 |       'textarea, input[type="text"]', // Message input
  156 |       'button:has-text("Send")', // Send button
  157 |       '.chat, .messages, [data-testid*="chat"]', // Chat container
  158 |       '.message, [data-testid*="message"]' // Message elements
  159 |     ];
  160 |
  161 |     let foundChatElements = 0;
  162 |     for (const selector of chatElements) {
  163 |       const element = page.locator(selector);
  164 |       if (await element.isVisible()) {
  165 |         foundChatElements++;
  166 |       }
  167 |     }
  168 |
  169 |     console.log(`Found ${foundChatElements} chat interface elements`);
  170 |     await takeScreenshot(page, 'ai-assistant-chat-interface');
  171 |   });
  172 |
  173 |   test('should handle message input and sending', async ({ page }) => {
  174 |     await page.goto('/ai-assistant');
  175 |     await waitForPageLoad(page);
  176 |     await waitForLoadingToComplete(page);
  177 |
  178 |     // Look for message input
  179 |     const messageInput = page.locator('textarea, input[type="text"], [data-testid*="message-input"]');
  180 |     
  181 |     if (await messageInput.isVisible()) {
  182 |       // Type a test message
  183 |       await messageInput.fill('What is the total revenue for this month?');
  184 |       await page.waitForTimeout(500);
  185 |       
  186 |       // Look for send button
  187 |       const sendButton = page.locator('button:has-text("Send"), [data-testid*="send"]');
  188 |       
  189 |       if (await sendButton.isVisible()) {
  190 |         await sendButton.click();
  191 |         await page.waitForTimeout(2000);
  192 |         
  193 |         // Look for response or loading indicator
  194 |         const loadingIndicator = page.locator('.loading, .thinking, text=/thinking|processing/i');
  195 |         if (await loadingIndicator.isVisible()) {
  196 |           console.log('AI is processing the message');
  197 |           await page.waitForTimeout(5000); // Wait for response
  198 |         }
  199 |         
  200 |         await takeScreenshot(page, 'ai-assistant-message-sent');
  201 |       }
  202 |     }
  203 |   });
  204 |
  205 |   test('should display conversation history', async ({ page }) => {
  206 |     await page.goto('/ai-assistant');
  207 |     await waitForPageLoad(page);
  208 |     await waitForLoadingToComplete(page);
  209 |
  210 |     // Look for conversation history
  211 |     const messages = page.locator('.message, [data-testid*="message"], .chat-message');
  212 |     const messageCount = await messages.count();
  213 |     
  214 |     console.log(`Found ${messageCount} messages in conversation history`);
  215 |     
  216 |     if (messageCount > 0) {
  217 |       // Check for user and AI messages
  218 |       const userMessages = page.locator('.user-message, [data-testid*="user-message"]');
  219 |       const aiMessages = page.locator('.ai-message, [data-testid*="ai-message"], .assistant-message');
  220 |       
  221 |       const userCount = await userMessages.count();
  222 |       const aiCount = await aiMessages.count();
  223 |       
  224 |       console.log(`Found ${userCount} user messages and ${aiCount} AI messages`);
  225 |     }
  226 |
  227 |     await takeScreenshot(page, 'ai-assistant-conversation-history');
  228 |   });
  229 |
  230 |   test('should handle AI assistant features', async ({ page }) => {
  231 |     await page.goto('/ai-assistant');
  232 |     await waitForPageLoad(page);
  233 |     await waitForLoadingToComplete(page);
  234 |
  235 |     // Look for AI assistant specific features
```