# Test info

- Name: Comprehensive Dashboard Testing >> should have proper accessibility features
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts:129:7

# Error details

```
Error: expect(received).toBe<PERSON><PERSON>r<PERSON><PERSON>(expected)

Expected: > 0
Received:   0
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts:137:41
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Dashboard":
      - /url: /dashboard
  - main:
    - heading "Dashboard" [level=1]
    - heading "Overview of your business performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Countries"
    - button "Sales Channels"
    - text: Master KPI Comparison Compare 'Gross Revenue' across selected brands using a line chart.
    - combobox "Select KPI": Gross Revenue
    - img: 2/28/2025 3/31/2025 4/30/2025 0 550K 1M 2M 2M
    - text: Brands Click to toggle. Sorted by latest KPI.
    - list:
      - button "Toggle visibility for Revant" [pressed]: Revant
      - button "Toggle visibility for Qalo" [pressed]: Qalo
      - button "Toggle visibility for Ergonofis" [pressed]: Ergonofis
      - button "Toggle visibility for Opposite Wall" [pressed]: Opposite Wall
      - button "Toggle visibility for Homesick" [pressed]: Homesick
      - button "Toggle visibility for Gravity" [pressed]: Gravity
      - button "Toggle visibility for Freakmount" [pressed]: Freakmount
      - button "Toggle visibility for MiHIGH" [pressed]: MiHIGH
      - button "Toggle visibility for Rose Boreal" [pressed]: Rose Boreal
      - button "Toggle visibility for Rachel" [pressed]: Rachel
      - button "Toggle visibility for Kana" [pressed]: Kana
      - button "Toggle visibility for Arctic Tumblers" [pressed]: Arctic Tumblers
      - button "Toggle visibility for Loctote" [pressed]: Loctote
      - button "Toggle visibility for Proper Pour" [pressed]: Proper Pour
      - button "Toggle visibility for Love Your Melon" [pressed]: Love Your Melon
      - button "Toggle visibility for Wolf & Grizzly" [pressed]: Wolf & Grizzly
      - button "Toggle visibility for Go Green" [pressed]: Go Green
      - button "Toggle visibility for Corretto" [pressed]: Corretto
      - button "Toggle visibility for Alex Bottle" [pressed]: Alex Bottle
    - heading "Key Performance Indicators" [level=3]
    - text: Revenue
    - button "KPI Definition"
    - text: N/A
    - img
    - text: Discount
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Refunds
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Net Revenue
    - button "KPI Definition"
    - text: N/A
    - img
    - text: Landed Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Fulfillment Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Transaction Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Gross Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Adspend
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Contribution Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   37 |   });
   38 |
   39 |   test('should display and interact with KPI cards', async ({ page }) => {
   40 |     await page.goto('/dashboard');
   41 |     await waitForPageLoad(page);
   42 |     await waitForLoadingToComplete(page);
   43 |
   44 |     // Look for KPI cards
   45 |     const kpiCards = page.locator('[data-testid*="kpi"], .kpi-card, [class*="kpi"]');
   46 |     const cardCount = await kpiCards.count();
   47 |
   48 |     if (cardCount > 0) {
   49 |       // Verify first KPI card is visible
   50 |       await expect(kpiCards.first()).toBeVisible();
   51 |       
   52 |       // Check for KPI values and labels
   53 |       const kpiValues = page.locator('[data-testid*="kpi-value"], .kpi-value');
   54 |       const kpiLabels = page.locator('[data-testid*="kpi-label"], .kpi-label');
   55 |       
   56 |       expect(await kpiValues.count()).toBeGreaterThan(0);
   57 |       expect(await kpiLabels.count()).toBeGreaterThan(0);
   58 |       
   59 |       console.log(`Found ${cardCount} KPI cards`);
   60 |     }
   61 |
   62 |     await takeScreenshot(page, 'dashboard-kpi-cards');
   63 |   });
   64 |
   65 |   test('should render charts correctly', async ({ page }) => {
   66 |     await page.goto('/dashboard');
   67 |     await waitForPageLoad(page);
   68 |     await waitForLoadingToComplete(page);
   69 |
   70 |     // Test chart rendering
   71 |     const charts = await testChartRendering(page);
   72 |     await takeScreenshot(page, 'dashboard-charts');
   73 |   });
   74 |
   75 |   test('should have functional filters', async ({ page }) => {
   76 |     await page.goto('/dashboard');
   77 |     await waitForPageLoad(page);
   78 |     await waitForLoadingToComplete(page);
   79 |
   80 |     // Test filter functionality
   81 |     const activeFilters = await testFilters(page);
   82 |     console.log(`Found ${activeFilters.length} active filters:`, activeFilters);
   83 |
   84 |     // Test date filter if present
   85 |     const dateFilter = page.locator('input[type="date"]');
   86 |     if (await dateFilter.isVisible()) {
   87 |       await dateFilter.fill('2024-01-01');
   88 |       await page.waitForTimeout(2000);
   89 |       await takeScreenshot(page, 'dashboard-date-filter-applied');
   90 |     }
   91 |
   92 |     // Test brand filter if present
   93 |     const brandFilter = page.locator('select[name*="brand"], [data-testid*="brand-filter"]');
   94 |     if (await brandFilter.isVisible()) {
   95 |       await brandFilter.click();
   96 |       await page.waitForTimeout(1000);
   97 |       
   98 |       const options = page.locator('option, [role="option"]');
   99 |       const optionCount = await options.count();
  100 |       
  101 |       if (optionCount > 1) {
  102 |         await options.nth(1).click();
  103 |         await page.waitForTimeout(2000);
  104 |         await takeScreenshot(page, 'dashboard-brand-filter-applied');
  105 |       }
  106 |     }
  107 |   });
  108 |
  109 |   test('should handle responsive design', async ({ page }) => {
  110 |     await page.goto('/dashboard');
  111 |     await waitForPageLoad(page);
  112 |
  113 |     const viewports = [
  114 |       { width: 1920, height: 1080, name: 'desktop' },
  115 |       { width: 1024, height: 768, name: 'tablet' },
  116 |       { width: 375, height: 667, name: 'mobile' }
  117 |     ];
  118 |
  119 |     for (const viewport of viewports) {
  120 |       await page.setViewportSize(viewport);
  121 |       await page.waitForTimeout(1000);
  122 |       
  123 |       // Verify main elements are still visible
  124 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
  125 |       await takeScreenshot(page, `dashboard-responsive-${viewport.name}`);
  126 |     }
  127 |   });
  128 |
  129 |   test('should have proper accessibility features', async ({ page }) => {
  130 |     await page.goto('/dashboard');
  131 |     await waitForPageLoad(page);
  132 |
  133 |     const accessibilityInfo = await testAccessibility(page);
  134 |     
  135 |     // Verify minimum accessibility requirements
  136 |     expect(accessibilityInfo.headings).toBeGreaterThan(0);
> 137 |     expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
      |                                         ^ Error: expect(received).toBeGreaterThan(expected)
  138 |     
  139 |     console.log('Accessibility info:', accessibilityInfo);
  140 |     await takeScreenshot(page, 'dashboard-accessibility');
  141 |   });
  142 |
  143 |   test('should handle navigation between dashboard sections', async ({ page }) => {
  144 |     await page.goto('/dashboard');
  145 |     await waitForPageLoad(page);
  146 |
  147 |     // Test navigation to other main sections
  148 |     const navLinks = [
  149 |       { text: 'Brand Deep Dive', url: '/brand-deep-dive' },
  150 |       { text: 'Marketing Dashboard', url: '/marketing-dashboard' },
  151 |       { text: 'Executive Summary', url: '/executive-summary' },
  152 |       { text: 'Budget', url: '/budget' },
  153 |       { text: 'AI Assistant', url: '/ai-assistant' }
  154 |     ];
  155 |
  156 |     for (const link of navLinks) {
  157 |       const navElement = page.locator(`nav a:has-text("${link.text}")`);
  158 |       
  159 |       if (await navElement.isVisible()) {
  160 |         await navElement.click();
  161 |         await page.waitForURL(`**${link.url}`);
  162 |         await waitForPageLoad(page);
  163 |         
  164 |         expect(page.url()).toContain(link.url);
  165 |         await takeScreenshot(page, `navigation-to-${link.text.toLowerCase().replace(' ', '-')}`);
  166 |         
  167 |         // Navigate back to dashboard
  168 |         await page.goto('/dashboard');
  169 |         await waitForPageLoad(page);
  170 |       }
  171 |     }
  172 |   });
  173 |
  174 |   test('should handle data refresh and updates', async ({ page }) => {
  175 |     await page.goto('/dashboard');
  176 |     await waitForPageLoad(page);
  177 |     await waitForLoadingToComplete(page);
  178 |
  179 |     // Test page refresh
  180 |     await page.reload();
  181 |     await waitForPageLoad(page);
  182 |     await waitForLoadingToComplete(page);
  183 |
  184 |     // Verify dashboard still works after refresh
  185 |     await expect(page.locator('main, [role="main"]')).toBeVisible();
  186 |     await takeScreenshot(page, 'dashboard-after-refresh');
  187 |   });
  188 |
  189 |   test('should not have critical console errors', async ({ page }) => {
  190 |     const consoleErrors: string[] = [];
  191 |     
  192 |     page.on('console', (msg) => {
  193 |       if (msg.type() === 'error') {
  194 |         consoleErrors.push(msg.text());
  195 |       }
  196 |     });
  197 |
  198 |     await page.goto('/dashboard');
  199 |     await waitForPageLoad(page);
  200 |     await waitForLoadingToComplete(page);
  201 |
  202 |     // Filter out acceptable errors
  203 |     const criticalErrors = consoleErrors.filter(error =>
  204 |       !error.includes('favicon') &&
  205 |       !error.includes('404') &&
  206 |       !error.includes('net::ERR_FAILED') &&
  207 |       !error.includes('ChunkLoadError') &&
  208 |       !error.includes('ResizeObserver')
  209 |     );
  210 |
  211 |     if (criticalErrors.length > 0) {
  212 |       console.log('Critical console errors found:', criticalErrors);
  213 |     }
  214 |
  215 |     // For now, log errors but don't fail the test
  216 |     // expect(criticalErrors).toHaveLength(0);
  217 |   });
  218 |
  219 |   test('should load within performance thresholds', async ({ page }) => {
  220 |     const startTime = Date.now();
  221 |     
  222 |     await page.goto('/dashboard');
  223 |     await waitForPageLoad(page);
  224 |     await waitForLoadingToComplete(page);
  225 |     
  226 |     const loadTime = Date.now() - startTime;
  227 |     
  228 |     // Dashboard should load within 10 seconds
  229 |     expect(loadTime).toBeLessThan(10000);
  230 |     
  231 |     console.log(`Dashboard loaded in ${loadTime}ms`);
  232 |   });
  233 | });
  234 |
```