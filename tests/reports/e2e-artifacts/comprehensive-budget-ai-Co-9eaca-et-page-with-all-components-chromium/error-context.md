# Test info

- Name: Comprehensive Budget Page Testing >> should load budget page with all components
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:22:7

# Error details

```
Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)

Locator: locator(':root')
Expected pattern: /Budget|NOLK/
Received string:  "Create Next App"
Call log:
  - expect.toHaveTitle with timeout 15000ms
  - waiting for locator(':root')
    19 × locator resolved to <html lang="en">…</html>
       - unexpected value "Create Next App"

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:28:24
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Budget":
      - /url: /budget
  - main:
    - heading "Budget Data" [level=1]
    - heading "Manage and view budget allocations" [level=2]
    - text: Budget Data
    - combobox: Select Brand
    - button "Import from CSV"
    - table:
      - rowgroup:
        - row "Month B2B Content Contribution Margin D2C Marketing Deferred Revenue Direct to Customer Fulfillment Fees Gross Margin Gross Revenue Landed Costs Marketplace Net Revenue Other Revenue Paid Marketing Marketplaces Pay-Per-Click Transaction Fees":
          - cell "Month"
          - cell "B2B"
          - cell "Content"
          - cell "Contribution Margin"
          - cell "D2C Marketing"
          - cell "Deferred Revenue"
          - cell "Direct to Customer"
          - cell "Fulfillment Fees"
          - cell "Gross Margin"
          - cell "Gross Revenue"
          - cell "Landed Costs"
          - cell "Marketplace"
          - cell "Net Revenue"
          - cell "Other Revenue"
          - cell "Paid Marketing Marketplaces"
          - cell "Pay-Per-Click"
          - cell "Transaction Fees"
      - rowgroup:
        - row "Jan-25 $112,325.84 $75,518.36 $783,333.61 $138,920.66 $12,608.16 $2,307,787.18 $617,188.37 $1,324,380.72 $3,428,989.67 $860,263.91 $996,268.49 $3,027,928.02 - $85,613.86 $379,914.88 $226,095.03":
          - cell "Jan-25"
          - cell "$112,325.84"
          - cell "$75,518.36"
          - cell "$783,333.61"
          - cell "$138,920.66"
          - cell "$12,608.16"
          - cell "$2,307,787.18"
          - cell "$617,188.37"
          - cell "$1,324,380.72"
          - cell "$3,428,989.67"
          - cell "$860,263.91"
          - cell "$996,268.49"
          - cell "$3,027,928.02"
          - cell "-"
          - cell "$85,613.86"
          - cell "$379,914.88"
          - cell "$226,095.03"
        - row "Feb-25 $115,493.38 $98,895.29 $690,342.85 $170,073.97 $108,093.02 $2,006,121.06 $462,780.30 $1,226,691.56 $3,043,169.21 $793,026.84 $811,821.86 $2,668,832.30 $1,639.89 $96,427.86 $341,025.55 $186,333.59":
          - cell "Feb-25"
          - cell "$115,493.38"
          - cell "$98,895.29"
          - cell "$690,342.85"
          - cell "$170,073.97"
          - cell "$108,093.02"
          - cell "$2,006,121.06"
          - cell "$462,780.30"
          - cell "$1,226,691.56"
          - cell "$3,043,169.21"
          - cell "$793,026.84"
          - cell "$811,821.86"
          - cell "$2,668,832.30"
          - cell "$1,639.89"
          - cell "$96,427.86"
          - cell "$341,025.55"
          - cell "$186,333.59"
        - row "Mar-25 $70,622.87 $134,650.79 $916,948.79 $232,856.18 -$138,993.00 $2,665,060.15 $542,930.94 $1,567,558.89 $3,591,946.88 $889,279.02 $995,256.85 $3,226,473.38 - $96,094.25 $419,865.06 $226,204.52":
          - cell "Mar-25"
          - cell "$70,622.87"
          - cell "$134,650.79"
          - cell "$916,948.79"
          - cell "$232,856.18"
          - cell "-$138,993.00"
          - cell "$2,665,060.15"
          - cell "$542,930.94"
          - cell "$1,567,558.89"
          - cell "$3,591,946.88"
          - cell "$889,279.02"
          - cell "$995,256.85"
          - cell "$3,226,473.38"
          - cell "-"
          - cell "$96,094.25"
          - cell "$419,865.06"
          - cell "$226,204.52"
        - row "Apr-25 $66,882.93 $100,364.92 $1,207,656.08 $272,263.07 $51,919.00 $2,885,619.40 $600,355.92 $1,875,743.43 $4,077,007.95 $972,456.73 $1,072,586.61 $3,703,230.33 - $104,910.91 $462,811.53 $254,174.25":
          - cell "Apr-25"
          - cell "$66,882.93"
          - cell "$100,364.92"
          - cell "$1,207,656.08"
          - cell "$272,263.07"
          - cell "$51,919.00"
          - cell "$2,885,619.40"
          - cell "$600,355.92"
          - cell "$1,875,743.43"
          - cell "$4,077,007.95"
          - cell "$972,456.73"
          - cell "$1,072,586.61"
          - cell "$3,703,230.33"
          - cell "-"
          - cell "$104,910.91"
          - cell "$462,811.53"
          - cell "$254,174.25"
        - row "May-25 $79,499.10 $101,791.79 $1,433,805.32 $354,449.75 $25,221.00 $3,370,088.55 $696,336.49 $2,202,337.75 $4,773,975.91 $1,088,181.04 $1,299,167.24 $4,293,683.40 - $129,206.08 $537,534.56 $306,328.11":
          - cell "May-25"
          - cell "$79,499.10"
          - cell "$101,791.79"
          - cell "$1,433,805.32"
          - cell "$354,449.75"
          - cell "$25,221.00"
          - cell "$3,370,088.55"
          - cell "$696,336.49"
          - cell "$2,202,337.75"
          - cell "$4,773,975.91"
          - cell "$1,088,181.04"
          - cell "$1,299,167.24"
          - cell "$4,293,683.40"
          - cell "-"
          - cell "$129,206.08"
          - cell "$537,534.56"
          - cell "$306,328.11"
        - row "Jun-25 $137,253.65 $91,039.34 $1,525,134.26 $336,638.12 -$147,869.00 $3,670,863.00 $696,947.37 $2,288,363.13 $4,927,494.95 $1,176,024.81 $1,267,247.30 $4,471,309.83 - $127,993.18 $544,196.36 $309,474.54":
          - cell "Jun-25"
          - cell "$137,253.65"
          - cell "$91,039.34"
          - cell "$1,525,134.26"
          - cell "$336,638.12"
          - cell "-$147,869.00"
          - cell "$3,670,863.00"
          - cell "$696,947.37"
          - cell "$2,288,363.13"
          - cell "$4,927,494.95"
          - cell "$1,176,024.81"
          - cell "$1,267,247.30"
          - cell "$4,471,309.83"
          - cell "-"
          - cell "$127,993.18"
          - cell "$544,196.36"
          - cell "$309,474.54"
        - row "Jul-25 $152,148.84 $126,828.09 $1,605,563.03 $375,857.72 $133,944.00 $3,738,402.04 $792,559.36 $2,535,722.86 $5,531,104.09 $1,243,991.11 $1,506,609.21 $4,912,098.64 - $165,607.29 $637,724.43 $339,325.31":
          - cell "Jul-25"
          - cell "$152,148.84"
          - cell "$126,828.09"
          - cell "$1,605,563.03"
          - cell "$375,857.72"
          - cell "$133,944.00"
          - cell "$3,738,402.04"
          - cell "$792,559.36"
          - cell "$2,535,722.86"
          - cell "$5,531,104.09"
          - cell "$1,243,991.11"
          - cell "$1,506,609.21"
          - cell "$4,912,098.64"
          - cell "-"
          - cell "$165,607.29"
          - cell "$637,724.43"
          - cell "$339,325.31"
        - row "Aug-25 $180,217.41 $93,860.28 $1,444,033.90 $298,404.95 -$13,321.00 $3,387,485.06 $697,631.67 $2,214,830.85 $4,943,949.94 $1,098,229.91 $1,389,568.47 $4,318,160.93 - $137,582.49 $539,354.18 $306,968.51":
          - cell "Aug-25"
          - cell "$180,217.41"
          - cell "$93,860.28"
          - cell "$1,444,033.90"
          - cell "$298,404.95"
          - cell "-$13,321.00"
          - cell "$3,387,485.06"
          - cell "$697,631.67"
          - cell "$2,214,830.85"
          - cell "$4,943,949.94"
          - cell "$1,098,229.91"
          - cell "$1,389,568.47"
          - cell "$4,318,160.93"
          - cell "-"
          - cell "$137,582.49"
          - cell "$539,354.18"
          - cell "$306,968.51"
        - row "Sep-25 $192,123.18 $98,483.96 $1,425,470.19 $240,255.27 -$151,112.00 $3,608,495.47 $718,154.66 $2,171,276.33 $4,863,689.04 $1,206,708.93 $1,214,182.38 $4,389,146.22 - $116,221.18 $531,101.00 $292,506.31":
          - cell "Sep-25"
          - cell "$192,123.18"
          - cell "$98,483.96"
          - cell "$1,425,470.19"
          - cell "$240,255.27"
          - cell "-$151,112.00"
          - cell "$3,608,495.47"
          - cell "$718,154.66"
          - cell "$2,171,276.33"
          - cell "$4,863,689.04"
          - cell "$1,206,708.93"
          - cell "$1,214,182.38"
          - cell "$4,389,146.22"
          - cell "-"
          - cell "$116,221.18"
          - cell "$531,101.00"
          - cell "$292,506.31"
        - row "Oct-25 $190,012.66 $99,513.04 $1,377,936.99 $196,109.31 $139,663.00 $3,250,122.38 $733,554.01 $2,078,438.26 $4,727,406.69 $1,202,191.13 $1,147,608.67 $4,290,359.30 - $120,027.91 $480,960.32 $275,675.88":
          - cell "Oct-25"
          - cell "$190,012.66"
          - cell "$99,513.04"
          - cell "$1,377,936.99"
          - cell "$196,109.31"
          - cell "$139,663.00"
          - cell "$3,250,122.38"
          - cell "$733,554.01"
          - cell "$2,078,438.26"
          - cell "$4,727,406.69"
          - cell "$1,202,191.13"
          - cell "$1,147,608.67"
          - cell "$4,290,359.30"
          - cell "-"
          - cell "$120,027.91"
          - cell "$480,960.32"
          - cell "$275,675.88"
        - row "Nov-25 $319,822.88 $145,536.20 $1,514,738.71 $168,470.86 -$523,706.00 $5,335,278.24 $1,044,725.09 $2,598,607.22 $6,402,493.56 $1,743,014.03 $1,271,098.45 $5,747,305.85 - $131,727.74 $806,604.57 $360,459.54":
          - cell "Nov-25"
          - cell "$319,822.88"
          - cell "$145,536.20"
          - cell "$1,514,738.71"
          - cell "$168,470.86"
          - cell "-$523,706.00"
          - cell "$5,335,278.24"
          - cell "$1,044,725.09"
          - cell "$2,598,607.22"
          - cell "$6,402,493.56"
          - cell "$1,743,014.03"
          - cell "$1,271,098.45"
          - cell "$5,747,305.85"
          - cell "-"
          - cell "$131,727.74"
          - cell "$806,604.57"
          - cell "$360,459.54"
        - row "Dec-25 $230,575.99 $111,136.99 $1,528,135.15 $159,505.49 $190,926.00 $4,410,196.83 $924,503.66 $2,389,399.87 $5,870,082.83 $1,638,116.84 $1,038,384.04 $5,278,144.49 - $106,731.28 $643,396.46 $325,624.10":
          - cell "Dec-25"
          - cell "$230,575.99"
          - cell "$111,136.99"
          - cell "$1,528,135.15"
          - cell "$159,505.49"
          - cell "$190,926.00"
          - cell "$4,410,196.83"
          - cell "$924,503.66"
          - cell "$2,389,399.87"
          - cell "$5,870,082.83"
          - cell "$1,638,116.84"
          - cell "$1,038,384.04"
          - cell "$5,278,144.49"
          - cell "-"
          - cell "$106,731.28"
          - cell "$643,396.46"
          - cell "$325,624.10"
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 | import {
   3 |   waitForPageLoad,
   4 |   signInAsAdmin,
   5 |   takeScreenshot,
   6 |   testChartRendering,
   7 |   testFilters,
   8 |   testDataTable,
   9 |   testFormSubmission,
   10 |   waitForLoadingToComplete,
   11 |   testAccessibility,
   12 |   verifyPageElements,
   13 |   testApiEndpoint
   14 | } from './utils/test-helpers';
   15 |
   16 | test.describe('Comprehensive Budget Page Testing', () => {
   17 |   test.beforeEach(async ({ page }) => {
   18 |     // Sign in as admin for all tests
   19 |     await signInAsAdmin(page);
   20 |   });
   21 |
   22 |   test('should load budget page with all components', async ({ page }) => {
   23 |     await page.goto('/budget');
   24 |     await waitForPageLoad(page);
   25 |     await waitForLoadingToComplete(page);
   26 |
   27 |     // Verify page title and main elements
>  28 |     await expect(page).toHaveTitle(/Budget|NOLK/);
      |                        ^ Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)
   29 |     
   30 |     // Check for main components
   31 |     const expectedElements = [
   32 |       'main, [role="main"]',
   33 |       'nav, [role="navigation"]',
   34 |       'h1, h2, [data-testid*="title"]'
   35 |     ];
   36 |     
   37 |     await verifyPageElements(page, expectedElements);
   38 |     await takeScreenshot(page, 'budget-page-main');
   39 |   });
   40 |
   41 |   test('should display budget data and charts', async ({ page }) => {
   42 |     await page.goto('/budget');
   43 |     await waitForPageLoad(page);
   44 |     await waitForLoadingToComplete(page);
   45 |
   46 |     // Test budget data table
   47 |     const tableInfo = await testDataTable(page);
   48 |     if (tableInfo.headerCount > 0) {
   49 |       console.log(`Budget table has ${tableInfo.headerCount} headers and ${tableInfo.rowCount} rows`);
   50 |     }
   51 |
   52 |     // Test budget charts
   53 |     const charts = await testChartRendering(page);
   54 |     
   55 |     // Look for budget-specific elements
   56 |     const budgetElements = page.locator('text=/budget|allocated|spent|remaining|variance/i');
   57 |     const budgetCount = await budgetElements.count();
   58 |     console.log(`Found ${budgetCount} budget-related elements`);
   59 |
   60 |     await takeScreenshot(page, 'budget-data-charts');
   61 |   });
   62 |
   63 |   test('should handle budget filters and brand selection', async ({ page }) => {
   64 |     await page.goto('/budget');
   65 |     await waitForPageLoad(page);
   66 |     await waitForLoadingToComplete(page);
   67 |
   68 |     // Test filter functionality
   69 |     const activeFilters = await testFilters(page);
   70 |     console.log(`Found ${activeFilters.length} active filters`);
   71 |
   72 |     // Test brand filter for budget
   73 |     const brandFilter = page.locator('select[name*="brand"], [data-testid*="brand-filter"]');
   74 |     if (await brandFilter.isVisible()) {
   75 |       await brandFilter.click();
   76 |       await page.waitForTimeout(1000);
   77 |       
   78 |       const options = page.locator('option, [role="option"]');
   79 |       const optionCount = await options.count();
   80 |       
   81 |       if (optionCount > 1) {
   82 |         await options.nth(1).click();
   83 |         await page.waitForTimeout(2000);
   84 |         await waitForLoadingToComplete(page);
   85 |         await takeScreenshot(page, 'budget-brand-filter-applied');
   86 |       }
   87 |     }
   88 |   });
   89 |
   90 |   test('should test budget API endpoint', async ({ page }) => {
   91 |     await page.goto('/budget');
   92 |     await waitForPageLoad(page);
   93 |
   94 |     // Test budget API
   95 |     try {
   96 |       const response = await testApiEndpoint(page, '/api/budget');
   97 |       console.log(`Budget API response: ${response.status()}`);
   98 |     } catch (error) {
   99 |       console.log(`Budget API failed: ${error}`);
  100 |     }
  101 |   });
  102 |
  103 |   test('should be responsive on different screen sizes', async ({ page }) => {
  104 |     await page.goto('/budget');
  105 |     await waitForPageLoad(page);
  106 |
  107 |     const viewports = [
  108 |       { width: 1920, height: 1080, name: 'desktop' },
  109 |       { width: 1024, height: 768, name: 'tablet' },
  110 |       { width: 375, height: 667, name: 'mobile' }
  111 |     ];
  112 |
  113 |     for (const viewport of viewports) {
  114 |       await page.setViewportSize(viewport);
  115 |       await page.waitForTimeout(1000);
  116 |       
  117 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
  118 |       await takeScreenshot(page, `budget-responsive-${viewport.name}`);
  119 |     }
  120 |   });
  121 | });
  122 |
  123 | test.describe('Comprehensive AI Assistant Testing', () => {
  124 |   test.beforeEach(async ({ page }) => {
  125 |     // Sign in as admin for all tests
  126 |     await signInAsAdmin(page);
  127 |   });
  128 |
```