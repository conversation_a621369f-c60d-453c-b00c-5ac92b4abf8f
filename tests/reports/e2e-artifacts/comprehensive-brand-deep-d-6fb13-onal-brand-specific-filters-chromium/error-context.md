# Test info

- Name: Comprehensive Brand Deep Dive Testing >> should have functional brand-specific filters
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:140:7

# Error details

```
Error: locator.isVisible: Error: strict mode violation: locator('input[type="date"]') resolved to 2 elements:
    1) <input type="date" id="start-date" data-slot="input" value="2025-03-01" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 …/> aka getByRole('textbox', { name: 'Start Date' })
    2) <input type="date" id="end-date" data-slot="input" value="2025-05-30" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md…/> aka getByRole('textbox', { name: 'End Date' })

Call log:
    - checking visibility of locator('input[type="date"]')

    at testFilters (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:338:23)
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:146:27
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
  - main:
    - heading "Brand Deep Dive" [level=1]
    - heading "Detailed analysis of brand performance metrics" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Week
    - text: Data Filters
    - button "Brand"
    - button "Countries"
    - button "Sales Channels"
    - text: 📊
    - heading "No Brand Selected" [level=2]
    - paragraph: Please select a brand from the dropdown above to view detailed performance metrics and analysis.
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
  238 |     { path: '/admin', name: 'Admin' }
  239 |   ];
  240 |
  241 |   for (const section of sections) {
  242 |     await page.goto(section.path);
  243 |     await waitForPageLoad(page);
  244 |
  245 |     // Verify we reached the intended page
  246 |     const currentUrl = page.url();
  247 |     expect(currentUrl).toContain(section.path);
  248 |
  249 |     // Take screenshot for visual verification
  250 |     await takeScreenshot(page, `navigation-${section.name.toLowerCase()}`);
  251 |   }
  252 | }
  253 |
  254 | /**
  255 |  * Verify all expected UI elements are present on a page
  256 |  */
  257 | export async function verifyPageElements(page: Page, expectedElements: string[]) {
  258 |   for (const element of expectedElements) {
  259 |     const locator = page.locator(element).first();
  260 |     await expect(locator).toBeVisible({ timeout: 5000 });
  261 |   }
  262 | }
  263 |
  264 | /**
  265 |  * Test API endpoint response
  266 |  */
  267 | export async function testApiEndpoint(page: Page, endpoint: string, expectedStatus = 200) {
  268 |   const response = await page.request.get(endpoint);
  269 |   expect(response.status()).toBe(expectedStatus);
  270 |   return response;
  271 | }
  272 |
  273 | /**
  274 |  * Test form submission
  275 |  */
  276 | export async function testFormSubmission(page: Page, formSelector: string, formData: Record<string, string>) {
  277 |   const form = page.locator(formSelector);
  278 |   await expect(form).toBeVisible();
  279 |
  280 |   // Fill form fields
  281 |   for (const [field, value] of Object.entries(formData)) {
  282 |     await page.fill(`${formSelector} input[name="${field}"], ${formSelector} select[name="${field}"]`, value);
  283 |   }
  284 |
  285 |   // Submit form
  286 |   await page.click(`${formSelector} button[type="submit"]`);
  287 | }
  288 |
  289 | /**
  290 |  * Test data table functionality
  291 |  */
  292 | export async function testDataTable(page: Page, tableSelector = 'table') {
  293 |   const table = page.locator(tableSelector);
  294 |   await expect(table).toBeVisible();
  295 |
  296 |   // Check for headers
  297 |   const headers = table.locator('thead th');
  298 |   const headerCount = await headers.count();
  299 |   expect(headerCount).toBeGreaterThan(0);
  300 |
  301 |   // Check for data rows
  302 |   const rows = table.locator('tbody tr');
  303 |   const rowCount = await rows.count();
  304 |
  305 |   return { headerCount, rowCount };
  306 | }
  307 |
  308 | /**
  309 |  * Test chart rendering
  310 |  */
  311 | export async function testChartRendering(page: Page, chartSelector = 'svg, canvas, .recharts-wrapper') {
  312 |   const chart = page.locator(chartSelector);
  313 |   await expect(chart).toBeVisible({ timeout: 10000 });
  314 |
  315 |   // Wait for chart to fully render
  316 |   await page.waitForTimeout(2000);
  317 |
  318 |   return chart;
  319 | }
  320 |
  321 | /**
  322 |  * Test filter functionality
  323 |  */
  324 | export async function testFilters(page: Page) {
  325 |   // Look for common filter elements
  326 |   const filterElements = [
  327 |     'select[name*="brand"]',
  328 |     'input[type="date"]',
  329 |     'select[name*="currency"]',
  330 |     'select[name*="country"]',
  331 |     '[data-testid*="filter"]'
  332 |   ];
  333 |
  334 |   const activeFilters = [];
  335 |
  336 |   for (const selector of filterElements) {
  337 |     const element = page.locator(selector);
> 338 |     if (await element.isVisible()) {
      |                       ^ Error: locator.isVisible: Error: strict mode violation: locator('input[type="date"]') resolved to 2 elements:
  339 |       activeFilters.push(selector);
  340 |     }
  341 |   }
  342 |
  343 |   return activeFilters;
  344 | }
  345 |
  346 | /**
  347 |  * Test pagination if present
  348 |  */
  349 | export async function testPagination(page: Page) {
  350 |   const pagination = page.locator('[data-testid="pagination"], .pagination, nav[aria-label*="pagination"]');
  351 |
  352 |   if (await pagination.isVisible()) {
  353 |     const nextButton = pagination.locator('button:has-text("Next"), button[aria-label*="next"]');
  354 |     const prevButton = pagination.locator('button:has-text("Previous"), button[aria-label*="previous"]');
  355 |
  356 |     return {
  357 |       hasPagination: true,
  358 |       hasNext: await nextButton.isVisible(),
  359 |       hasPrevious: await prevButton.isVisible()
  360 |     };
  361 |   }
  362 |
  363 |   return { hasPagination: false };
  364 | }
  365 |
  366 | /**
  367 |  * Test search functionality
  368 |  */
  369 | export async function testSearch(page: Page, searchTerm: string) {
  370 |   const searchInput = page.locator('input[type="search"], input[placeholder*="search"], input[name*="search"]');
  371 |
  372 |   if (await searchInput.isVisible()) {
  373 |     await searchInput.fill(searchTerm);
  374 |     await page.keyboard.press('Enter');
  375 |     await page.waitForTimeout(1000);
  376 |     return true;
  377 |   }
  378 |
  379 |   return false;
  380 | }
  381 |
  382 | /**
  383 |  * Test modal/dialog functionality
  384 |  */
  385 | export async function testModal(page: Page, triggerSelector: string) {
  386 |   await page.click(triggerSelector);
  387 |
  388 |   const modal = page.locator('[role="dialog"], .modal, [data-testid*="modal"]');
  389 |   await expect(modal).toBeVisible();
  390 |
  391 |   // Test close functionality
  392 |   const closeButton = modal.locator('button[aria-label*="close"], button:has-text("Close"), [data-testid*="close"]');
  393 |   if (await closeButton.isVisible()) {
  394 |     await closeButton.click();
  395 |     await expect(modal).not.toBeVisible();
  396 |   }
  397 | }
  398 |
  399 | /**
  400 |  * Test loading states
  401 |  */
  402 | export async function waitForLoadingToComplete(page: Page) {
  403 |   // Wait for common loading indicators to disappear
  404 |   const loadingSelectors = [
  405 |     '.loading',
  406 |     '.spinner',
  407 |     '[data-testid*="loading"]',
  408 |     '.animate-spin',
  409 |     'text="Loading"'
  410 |   ];
  411 |
  412 |   for (const selector of loadingSelectors) {
  413 |     try {
  414 |       await page.waitForSelector(selector, { state: 'hidden', timeout: 5000 });
  415 |     } catch {
  416 |       // Ignore if selector doesn't exist
  417 |     }
  418 |   }
  419 | }
  420 |
  421 | /**
  422 |  * Test accessibility features
  423 |  */
  424 | export async function testAccessibility(page: Page) {
  425 |   // Check for basic accessibility attributes
  426 |   const elementsWithAriaLabels = await page.locator('[aria-label]').count();
  427 |   const elementsWithRoles = await page.locator('[role]').count();
  428 |   const headings = await page.locator('h1, h2, h3, h4, h5, h6').count();
  429 |   const landmarks = await page.locator('[role="main"], [role="navigation"], [role="banner"], [role="contentinfo"]').count();
  430 |
  431 |   return {
  432 |     ariaLabels: elementsWithAriaLabels,
  433 |     roles: elementsWithRoles,
  434 |     headings,
  435 |     landmarks
  436 |   };
  437 | }
  438 |
```