# Test info

- Name: Comprehensive Dashboard Testing >> should load dashboard with all core components
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts:22:7

# Error details

```
Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)

Locator: locator(':root')
Expected pattern: /Dashboard|NOLK/
Received string:  "Create Next App"
Call log:
  - expect.toHaveTitle with timeout 15000ms
  - waiting for locator(':root')
    19 × locator resolved to <html lang="en">…</html>
       - unexpected value "Create Next App"

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts:28:24
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Dashboard":
      - /url: /dashboard
  - main:
    - heading "Dashboard" [level=1]
    - heading "Overview of your business performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Countries"
    - button "Sales Channels"
    - text: Master KPI Comparison Compare 'Gross Revenue' across selected brands using a line chart.
    - combobox "Select KPI": Gross Revenue
    - img: 2/28/2025 3/31/2025 4/30/2025 0 550K 1M 2M 2M
    - text: Brands Click to toggle. Sorted by latest KPI.
    - list:
      - button "Toggle visibility for Revant" [pressed]: Revant
      - button "Toggle visibility for Qalo" [pressed]: Qalo
      - button "Toggle visibility for Ergonofis" [pressed]: Ergonofis
      - button "Toggle visibility for Opposite Wall" [pressed]: Opposite Wall
      - button "Toggle visibility for Homesick" [pressed]: Homesick
      - button "Toggle visibility for Gravity" [pressed]: Gravity
      - button "Toggle visibility for Freakmount" [pressed]: Freakmount
      - button "Toggle visibility for MiHIGH" [pressed]: MiHIGH
      - button "Toggle visibility for Rose Boreal" [pressed]: Rose Boreal
      - button "Toggle visibility for Rachel" [pressed]: Rachel
      - button "Toggle visibility for Kana" [pressed]: Kana
      - button "Toggle visibility for Arctic Tumblers" [pressed]: Arctic Tumblers
      - button "Toggle visibility for Loctote" [pressed]: Loctote
      - button "Toggle visibility for Proper Pour" [pressed]: Proper Pour
      - button "Toggle visibility for Love Your Melon" [pressed]: Love Your Melon
      - button "Toggle visibility for Wolf & Grizzly" [pressed]: Wolf & Grizzly
      - button "Toggle visibility for Go Green" [pressed]: Go Green
      - button "Toggle visibility for Corretto" [pressed]: Corretto
      - button "Toggle visibility for Alex Bottle" [pressed]: Alex Bottle
    - heading "Key Performance Indicators" [level=3]
    - text: Revenue
    - button "KPI Definition"
    - text: $17,245,998.18
    - img: 2/28/2025 4/30/2025 0 2,000,000 4,000,000 6,000,000 8,000,000
    - text: Discount
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,916,224.18
    - img: 2/28/2025 4/30/2025 0 200,000 400,000 600,000 800,000
    - text: Refunds
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $614,536.13
    - img: 2/28/2025 4/30/2025 0 70,000 140,000 210,000 280,000
    - text: Net Revenue
    - button "KPI Definition"
    - text: $14,715,237.88
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
    - text: Landed Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,709,657.76
    - img: 2/28/2025 4/30/2025 0 300,000 600,000 900,000 1,200,000
    - text: Fulfillment Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,167,088.33
    - img: 2/28/2025 4/30/2025 0 250,000 500,000 750,000 1,000,000
    - text: Transaction Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,034,384.19
    - img: 2/28/2025 4/30/2025 0 95,000 190,000 285,000 380,000
    - text: Gross Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $9,520,150.47
    - img: 2/28/2025 4/30/2025 0 900,000 1,800,000 2,700,000 3,600,000
    - text: Adspend
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $3,424,904.20
    - img: 2/28/2025 4/30/2025 0 350,000 700,000 1,050,000 1,400,000
    - text: Contribution Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $12,945,054.68
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 | import {
   3 |   waitForPageLoad,
   4 |   signInAsAdmin,
   5 |   takeScreenshot,
   6 |   testChartRendering,
   7 |   testFilters,
   8 |   testDataTable,
   9 |   waitForLoadingToComplete,
   10 |   testAccessibility,
   11 |   verifyPageElements,
   12 |   getMainContent,
   13 |   getNavLink
   14 | } from './utils/test-helpers';
   15 |
   16 | test.describe('Comprehensive Dashboard Testing', () => {
   17 |   test.beforeEach(async ({ page }) => {
   18 |     // Sign in as admin for all tests
   19 |     await signInAsAdmin(page);
   20 |   });
   21 |
   22 |   test('should load dashboard with all core components', async ({ page }) => {
   23 |     await page.goto('/dashboard');
   24 |     await waitForPageLoad(page);
   25 |     await waitForLoadingToComplete(page);
   26 |
   27 |     // Verify page title and main elements
>  28 |     await expect(page).toHaveTitle(/Dashboard|NOLK/);
      |                        ^ Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)
   29 |
   30 |     // Check for main dashboard components
   31 |     const mainContent = await getMainContent(page);
   32 |     await expect(mainContent).toBeVisible();
   33 |
   34 |     const expectedElements = [
   35 |       'nav, [role="navigation"]',
   36 |       'h1, h2, [data-testid*="title"]'
   37 |     ];
   38 |
   39 |     await verifyPageElements(page, expectedElements);
   40 |     await takeScreenshot(page, 'dashboard-main-components');
   41 |   });
   42 |
   43 |   test('should display and interact with KPI cards', async ({ page }) => {
   44 |     await page.goto('/dashboard');
   45 |     await waitForPageLoad(page);
   46 |     await waitForLoadingToComplete(page);
   47 |
   48 |     // Look for KPI cards
   49 |     const kpiCards = page.locator('[data-testid*="kpi"], .kpi-card, [class*="kpi"]');
   50 |     const cardCount = await kpiCards.count();
   51 |
   52 |     if (cardCount > 0) {
   53 |       // Verify first KPI card is visible
   54 |       await expect(kpiCards.first()).toBeVisible();
   55 |
   56 |       // Check for KPI values and labels
   57 |       const kpiValues = page.locator('[data-testid*="kpi-value"], .kpi-value');
   58 |       const kpiLabels = page.locator('[data-testid*="kpi-label"], .kpi-label');
   59 |
   60 |       expect(await kpiValues.count()).toBeGreaterThan(0);
   61 |       expect(await kpiLabels.count()).toBeGreaterThan(0);
   62 |
   63 |       console.log(`Found ${cardCount} KPI cards`);
   64 |     }
   65 |
   66 |     await takeScreenshot(page, 'dashboard-kpi-cards');
   67 |   });
   68 |
   69 |   test('should render charts correctly', async ({ page }) => {
   70 |     await page.goto('/dashboard');
   71 |     await waitForPageLoad(page);
   72 |     await waitForLoadingToComplete(page);
   73 |
   74 |     // Test chart rendering
   75 |     const charts = await testChartRendering(page);
   76 |     await takeScreenshot(page, 'dashboard-charts');
   77 |   });
   78 |
   79 |   test('should have functional filters', async ({ page }) => {
   80 |     await page.goto('/dashboard');
   81 |     await waitForPageLoad(page);
   82 |     await waitForLoadingToComplete(page);
   83 |
   84 |     // Test filter functionality
   85 |     const activeFilters = await testFilters(page);
   86 |     console.log(`Found ${activeFilters.length} active filters:`, activeFilters);
   87 |
   88 |     // Test date filter if present
   89 |     const dateFilter = page.locator('input[type="date"]');
   90 |     if (await dateFilter.isVisible()) {
   91 |       await dateFilter.fill('2024-01-01');
   92 |       await page.waitForTimeout(2000);
   93 |       await takeScreenshot(page, 'dashboard-date-filter-applied');
   94 |     }
   95 |
   96 |     // Test brand filter if present
   97 |     const brandFilter = page.locator('select[name*="brand"], [data-testid*="brand-filter"]');
   98 |     if (await brandFilter.isVisible()) {
   99 |       await brandFilter.click();
  100 |       await page.waitForTimeout(1000);
  101 |
  102 |       const options = page.locator('option, [role="option"]');
  103 |       const optionCount = await options.count();
  104 |
  105 |       if (optionCount > 1) {
  106 |         await options.nth(1).click();
  107 |         await page.waitForTimeout(2000);
  108 |         await takeScreenshot(page, 'dashboard-brand-filter-applied');
  109 |       }
  110 |     }
  111 |   });
  112 |
  113 |   test('should handle responsive design', async ({ page }) => {
  114 |     await page.goto('/dashboard');
  115 |     await waitForPageLoad(page);
  116 |
  117 |     const viewports = [
  118 |       { width: 1920, height: 1080, name: 'desktop' },
  119 |       { width: 1024, height: 768, name: 'tablet' },
  120 |       { width: 375, height: 667, name: 'mobile' }
  121 |     ];
  122 |
  123 |     for (const viewport of viewports) {
  124 |       await page.setViewportSize(viewport);
  125 |       await page.waitForTimeout(1000);
  126 |
  127 |       // Verify main elements are still visible
  128 |       const mainContent = await getMainContent(page);
```