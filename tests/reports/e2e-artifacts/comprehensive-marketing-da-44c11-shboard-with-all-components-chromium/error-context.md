# Test info

- Name: Comprehensive Marketing Dashboard Testing >> should load marketing dashboard with all components
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts:22:7

# Error details

```
Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)

Locator: locator(':root')
Expected pattern: /Marketing Dashboard|NOLK/
Received string:  "Create Next App"
Call log:
  - expect.toHaveTitle with timeout 15000ms
  - waiting for locator(':root')
    19 × locator resolved to <html lang="en">…</html>
       - unexpected value "Create Next App"

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts:28:24
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
  - main:
    - heading "Marketing Dashboard" [level=1]
    - heading "Track and analyze your marketing performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Sales Channels"
    - heading "Overall KPI Summary" [level=3]
    - text: Total Spend
    - button "KPI Definition"
    - text: $3,405,148.13
    - img: 4/30/2025 $0.00 $350K $700K $1.1M $1.4M
    - text: Total Impressions
    - button "KPI Definition"
    - text: 258,220,477
    - img: 4/30/2025 0 25M 50M 75M 100M
    - text: Total Clicks
    - button "KPI Definition"
    - text: 2,177,675
    - img: 4/30/2025 0 200K 400K 600K 800K
    - text: Total Conversions
    - button "KPI Definition"
    - text: 131,557
    - img: 4/30/2025 0 15K 30K 45K 60K
    - text: Total Conversion Value
    - button "KPI Definition"
    - text: $12,005,933.44
    - img: 4/30/2025 $0.00 $1.5M $3.0M $4.5M $6.0M
    - text: Overall ROAS
    - button "KPI Definition"
    - text: 3.53x
    - img: 4/30/2025 0.0x 0.9x 1.9x 2.9x 3.8x
    - text: Overall CPA
    - button "KPI Definition"
    - text: $25.88
    - img: 4/30/2025 $0.00 $7.00 $14.00 $21.00 $28.00
    - text: Overall CTR
    - button "KPI Definition"
    - text: 0.84%
    - img: 4/30/2025 0% 0.3% 0.5% 0.8% 1%
    - heading "Spend Breakdown" [level=3]
    - text: "Spend Breakdown By Sales Channel (Aggregation: month)"
    - img:
      - img
      - img
    - list:
      - listitem:
        - img
        - text: Shopify
      - listitem:
        - img
        - text: Amazon
    - text: Campaign Performance Details
    - button "Columns":
      - img
      - text: Columns
    - table:
      - rowgroup:
        - row "Date Campaign Name Brand Sales Channel Total Spend Impressions Clicks CTR Conversions Conversion Value ROAS CPA":
          - cell "Date"
          - cell "Campaign Name"
          - cell "Brand"
          - cell "Sales Channel"
          - cell "Total Spend"
          - cell "Impressions"
          - cell "Clicks"
          - cell "CTR"
          - cell "Conversions"
          - cell "Conversion Value"
          - cell "ROAS"
          - cell "CPA"
      - rowgroup:
        - row "4/30/2025 (mint.) COLD Acquisition ROC Ergonofis Shopify $234.86 81,525 267 0.33% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "(mint.) COLD Acquisition ROC":
            - link "(mint.) COLD Acquisition ROC":
              - /url: /marketing-dashboard/(mint.)%20-%20COLD%20-%20Acquisition%20-%20ROC
          - cell "Ergonofis"
          - cell "Shopify"
          - cell "$234.86"
          - cell "81,525"
          - cell "267"
          - cell "0.33%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 (mint.) COLD Acquisition USA Ergonofis Shopify $563.68 127,975 445 0.35% 1 $0.00 0.00x $563.68":
          - cell "4/30/2025"
          - cell "(mint.) COLD Acquisition USA":
            - link "(mint.) COLD Acquisition USA":
              - /url: /marketing-dashboard/(mint.)%20-%20COLD%20-%20Acquisition%20-%20USA
          - cell "Ergonofis"
          - cell "Shopify"
          - cell "$563.68"
          - cell "127,975"
          - cell "445"
          - cell "0.35%"
          - cell "1"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$563.68"
        - row "4/30/2025 (mint.) WARM Remarketing ROC Ergonofis Shopify $266.98 95,378 358 0.38% 2 $3,715.59 13.92x $133.49":
          - cell "4/30/2025"
          - cell "(mint.) WARM Remarketing ROC":
            - link "(mint.) WARM Remarketing ROC":
              - /url: /marketing-dashboard/(mint.)%20-%20WARM%20-%20Remarketing%20-%20ROC
          - cell "Ergonofis"
          - cell "Shopify"
          - cell "$266.98"
          - cell "95,378"
          - cell "358"
          - cell "0.38%"
          - cell "2"
          - cell "$3,715.59"
          - cell "13.92x"
          - cell "$133.49"
        - row "4/30/2025 (mint.) WARM Remarketing USA Ergonofis Shopify $238.60 15,463 52 0.34% 1 $1,379.31 5.78x $238.60":
          - cell "4/30/2025"
          - cell "(mint.) WARM Remarketing USA":
            - link "(mint.) WARM Remarketing USA":
              - /url: /marketing-dashboard/(mint.)%20-%20WARM%20-%20Remarketing%20-%20USA
          - cell "Ergonofis"
          - cell "Shopify"
          - cell "$238.60"
          - cell "15,463"
          - cell "52"
          - cell "0.34%"
          - cell "1"
          - cell "$1,379.31"
          - cell "5.78x"
          - cell "$238.60"
        - row "4/30/2025 (mint.) ROC HOT Catalog sales Ergonofis Shopify $127.93 10,005 28 0.28% 1 $390.00 3.05x $127.93":
          - cell "4/30/2025"
          - cell "(mint.) ROC HOT Catalog sales":
            - link "(mint.) ROC HOT Catalog sales":
              - /url: /marketing-dashboard/(mint.)%20ROC%20-%20HOT%20-%20Catalog%20sales
          - cell "Ergonofis"
          - cell "Shopify"
          - cell "$127.93"
          - cell "10,005"
          - cell "28"
          - cell "0.28%"
          - cell "1"
          - cell "$390.00"
          - cell "3.05x"
          - cell "$127.93"
        - row "4/30/2025 (mint.) US HOT Catalog sales Ergonofis Shopify $209.62 10,692 24 0.22% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "(mint.) US HOT Catalog sales":
            - link "(mint.) US HOT Catalog sales":
              - /url: /marketing-dashboard/(mint.)%20US%20-%20HOT%20-%20Catalog%20sales
          - cell "Ergonofis"
          - cell "Shopify"
          - cell "$209.62"
          - cell "10,692"
          - cell "24"
          - cell "0.22%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 19x27 OAK Perpetua SP Auto ... Opposite Wall Amazon $120.49 26,408 129 0.49% 6 $444.74 3.69x $20.08":
          - cell "4/30/2025"
          - cell "19x27 OAK Perpetua SP Auto ...":
            - link "19x27 OAK Perpetua SP Auto ...":
              - /url: /marketing-dashboard/19x27%20OAK%20-%20Perpetua%20-%20SP%20-%20Auto%20-%20aahnqbbh
          - cell "Opposite Wall"
          - cell "Amazon"
          - cell "$120.49"
          - cell "26,408"
          - cell "129"
          - cell "0.49%"
          - cell "6"
          - cell "$444.74"
          - cell "3.69x"
          - cell "$20.08"
        - row "4/30/2025 19x27 OAK Perpetua SP Manua... Opposite Wall Amazon $2,016.48 87,674 1,334 1.52% 143 $11,631.11 5.77x $14.10":
          - cell "4/30/2025"
          - cell "19x27 OAK Perpetua SP Manua...":
            - link "19x27 OAK Perpetua SP Manua...":
              - /url: /marketing-dashboard/19x27%20OAK%20-%20Perpetua%20-%20SP%20-%20Manual%20-%20aahnqbbh
          - cell "Opposite Wall"
          - cell "Amazon"
          - cell "$2,016.48"
          - cell "87,674"
          - cell "1,334"
          - cell "1.52%"
          - cell "143"
          - cell "$11,631.11"
          - cell "5.77x"
          - cell "$14.10"
        - row "4/30/2025 19x27 OAK Perpetua SP PAT a... Opposite Wall Amazon $230.27 14,090 172 1.22% 18 $1,453.86 6.31x $12.79":
          - cell "4/30/2025"
          - cell "19x27 OAK Perpetua SP PAT a...":
            - link "19x27 OAK Perpetua SP PAT a...":
              - /url: /marketing-dashboard/19x27%20OAK%20-%20Perpetua%20-%20SP%20-%20PAT%20-%20aahnqbbh
          - cell "Opposite Wall"
          - cell "Amazon"
          - cell "$230.27"
          - cell "14,090"
          - cell "172"
          - cell "1.22%"
          - cell "18"
          - cell "$1,453.86"
          - cell "6.31x"
          - cell "$12.79"
        - row "4/30/2025 20 oz Tumblers Brands Store... Arctic Tumblers Amazon $0.00 0 0 0.00% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "20 oz Tumblers Brands Store...":
            - link "20 oz Tumblers Brands Store...":
              - /url: /marketing-dashboard/20%20oz%20Tumblers%20%7C%20Brands%20%7C%20Storefront
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$0.00"
          - cell "0"
          - cell "0"
          - cell "0.00%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 20 oz Brands Storefront Arctic Tumblers Amazon $0.00 0 0 0.00% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "20 oz Brands Storefront":
            - link "20 oz Brands Storefront":
              - /url: /marketing-dashboard/20%20oz%20%7C%20Brands%20%7C%20Storefront
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$0.00"
          - cell "0"
          - cell "0"
          - cell "0.00%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 2022-11-07 Intérêt Activewe... Rose Boreal Shopify $0.00 41 0 0.00% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "2022-11-07 Intérêt Activewe...":
            - link "2022-11-07 Intérêt Activewe...":
              - /url: /marketing-dashboard/2022-11-07%20%7C%20Int%C3%A9r%C3%AAt%20%7C%20Activewear%20created%20and
          - cell "Rose Boreal"
          - cell "Shopify"
          - cell "$0.00"
          - cell "41"
          - cell "0"
          - cell "0.00%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 2023 Branded USA Wolf & Grizzly Shopify $135.70 7,246 137 1.89% 5 $131.15 0.97x $27.14":
          - cell "4/30/2025"
          - cell "2023 Branded USA":
            - link "2023 Branded USA":
              - /url: /marketing-dashboard/2023%20Branded_USA
          - cell "Wolf & Grizzly"
          - cell "Shopify"
          - cell "$135.70"
          - cell "7,246"
          - cell "137"
          - cell "1.89%"
          - cell "5"
          - cell "$131.15"
          - cell "0.97x"
          - cell "$27.14"
        - row "4/30/2025 2023-05-23 Intérêt Discover... Rose Boreal Shopify $0.00 4 0 0.00% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "2023-05-23 Intérêt Discover...":
            - link "2023-05-23 Intérêt Discover...":
              - /url: /marketing-dashboard/2023-05-23%20%7C%20Int%C3%A9r%C3%AAt%20%7C%20Discover%20our%20new
          - cell "Rose Boreal"
          - cell "Shopify"
          - cell "$0.00"
          - cell "4"
          - cell "0"
          - cell "0.00%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 20oz Perpetua SP Auto 73111d9c Arctic Tumblers Amazon $45.05 30,060 140 0.47% 10 $254.64 5.65x $4.51":
          - cell "4/30/2025"
          - cell "20oz Perpetua SP Auto 73111d9c":
            - link "20oz Perpetua SP Auto 73111d9c":
              - /url: /marketing-dashboard/20oz%20-%20Perpetua%20-%20SP%20-%20Auto%20-%2073111d9c
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$45.05"
          - cell "30,060"
          - cell "140"
          - cell "0.47%"
          - cell "10"
          - cell "$254.64"
          - cell "5.65x"
          - cell "$4.51"
        - row "4/30/2025 20oz Perpetua SP Auto zhxt0siv Arctic Tumblers Amazon $36.55 53,556 132 0.25% 6 $167.70 4.59x $6.09":
          - cell "4/30/2025"
          - cell "20oz Perpetua SP Auto zhxt0siv":
            - link "20oz Perpetua SP Auto zhxt0siv":
              - /url: /marketing-dashboard/20oz%20-%20Perpetua%20-%20SP%20-%20Auto%20-%20zhxt0siv
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$36.55"
          - cell "53,556"
          - cell "132"
          - cell "0.25%"
          - cell "6"
          - cell "$167.70"
          - cell "4.59x"
          - cell "$6.09"
        - row "4/30/2025 20oz Perpetua SP Manual 731... Arctic Tumblers Amazon $249.34 96,096 593 0.62% 70 $2,243.73 9.00x $3.56":
          - cell "4/30/2025"
          - cell "20oz Perpetua SP Manual 731...":
            - link "20oz Perpetua SP Manual 731...":
              - /url: /marketing-dashboard/20oz%20-%20Perpetua%20-%20SP%20-%20Manual%20-%2073111d9c
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$249.34"
          - cell "96,096"
          - cell "593"
          - cell "0.62%"
          - cell "70"
          - cell "$2,243.73"
          - cell "9.00x"
          - cell "$3.56"
        - row "4/30/2025 20oz Perpetua SP Manual zhx... Arctic Tumblers Amazon $105.57 86,650 362 0.42% 19 $518.05 4.91x $5.56":
          - cell "4/30/2025"
          - cell "20oz Perpetua SP Manual zhx...":
            - link "20oz Perpetua SP Manual zhx...":
              - /url: /marketing-dashboard/20oz%20-%20Perpetua%20-%20SP%20-%20Manual%20-%20zhxt0siv
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$105.57"
          - cell "86,650"
          - cell "362"
          - cell "0.42%"
          - cell "19"
          - cell "$518.05"
          - cell "4.91x"
          - cell "$5.56"
        - row "4/30/2025 20oz Perpetua SP PAT 73111d9c Arctic Tumblers Amazon $88.30 26,388 213 0.81% 24 $633.35 7.17x $3.68":
          - cell "4/30/2025"
          - cell "20oz Perpetua SP PAT 73111d9c":
            - link "20oz Perpetua SP PAT 73111d9c":
              - /url: /marketing-dashboard/20oz%20-%20Perpetua%20-%20SP%20-%20PAT%20-%2073111d9c
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$88.30"
          - cell "26,388"
          - cell "213"
          - cell "0.81%"
          - cell "24"
          - cell "$633.35"
          - cell "7.17x"
          - cell "$3.68"
        - row "4/30/2025 20oz Perpetua SP PAT zhxt0siv Arctic Tumblers Amazon $41.18 36,182 131 0.36% 1 $26.95 0.65x $41.18":
          - cell "4/30/2025"
          - cell "20oz Perpetua SP PAT zhxt0siv":
            - link "20oz Perpetua SP PAT zhxt0siv":
              - /url: /marketing-dashboard/20oz%20-%20Perpetua%20-%20SP%20-%20PAT%20-%20zhxt0siv
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$41.18"
          - cell "36,182"
          - cell "131"
          - cell "0.36%"
          - cell "1"
          - cell "$26.95"
          - cell "0.65x"
          - cell "$41.18"
        - row "4/30/2025 30 oz Headline Ad v2 to pro... Arctic Tumblers Amazon $0.00 0 0 0.00% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "30 oz Headline Ad v2 to pro...":
            - link "30 oz Headline Ad v2 to pro...":
              - /url: /marketing-dashboard/30%20oz%20Headline%20Ad%20v2%20-%20to%20product%20listing
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$0.00"
          - cell "0"
          - cell "0"
          - cell "0.00%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 30 oz Brands Storefront Arctic Tumblers Amazon $0.00 0 0 0.00% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "30 oz Brands Storefront":
            - link "30 oz Brands Storefront":
              - /url: /marketing-dashboard/30%20oz%20%7C%20Brands%20%7C%20Storefront
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$0.00"
          - cell "0"
          - cell "0"
          - cell "0.00%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 30oz Perpetua SP Auto 7ro0nw7a Arctic Tumblers Amazon $82.36 89,126 252 0.28% 16 $472.52 5.74x $5.15":
          - cell "4/30/2025"
          - cell "30oz Perpetua SP Auto 7ro0nw7a":
            - link "30oz Perpetua SP Auto 7ro0nw7a":
              - /url: /marketing-dashboard/30oz%20-%20Perpetua%20-%20SP%20-%20Auto%20-%207ro0nw7a
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$82.36"
          - cell "89,126"
          - cell "252"
          - cell "0.28%"
          - cell "16"
          - cell "$472.52"
          - cell "5.74x"
          - cell "$5.15"
        - row "4/30/2025 30oz Perpetua SP Auto wzr0simo Arctic Tumblers Amazon $54.86 93,752 181 0.19% 11 $403.16 7.35x $4.99":
          - cell "4/30/2025"
          - cell "30oz Perpetua SP Auto wzr0simo":
            - link "30oz Perpetua SP Auto wzr0simo":
              - /url: /marketing-dashboard/30oz%20-%20Perpetua%20-%20SP%20-%20Auto%20-%20wzr0simo
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$54.86"
          - cell "93,752"
          - cell "181"
          - cell "0.19%"
          - cell "11"
          - cell "$403.16"
          - cell "7.35x"
          - cell "$4.99"
        - row "4/30/2025 30oz Perpetua SP Manual 7ro... Arctic Tumblers Amazon $758.16 194,211 1,390 0.72% 169 $5,203.27 6.86x $4.49":
          - cell "4/30/2025"
          - cell "30oz Perpetua SP Manual 7ro...":
            - link "30oz Perpetua SP Manual 7ro...":
              - /url: /marketing-dashboard/30oz%20-%20Perpetua%20-%20SP%20-%20Manual%20-%207ro0nw7a
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$758.16"
          - cell "194,211"
          - cell "1,390"
          - cell "0.72%"
          - cell "169"
          - cell "$5,203.27"
          - cell "6.86x"
          - cell "$4.49"
        - row "4/30/2025 30oz Perpetua SP Manual wzr... Arctic Tumblers Amazon $97.05 88,078 323 0.37% 20 $575.00 5.92x $4.85":
          - cell "4/30/2025"
          - cell "30oz Perpetua SP Manual wzr...":
            - link "30oz Perpetua SP Manual wzr...":
              - /url: /marketing-dashboard/30oz%20-%20Perpetua%20-%20SP%20-%20Manual%20-%20wzr0simo
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$97.05"
          - cell "88,078"
          - cell "323"
          - cell "0.37%"
          - cell "20"
          - cell "$575.00"
          - cell "5.92x"
          - cell "$4.85"
        - row "4/30/2025 30oz Perpetua SP PAT 7ro0nw7a Arctic Tumblers Amazon $224.62 80,118 505 0.63% 54 $1,804.29 8.03x $4.16":
          - cell "4/30/2025"
          - cell "30oz Perpetua SP PAT 7ro0nw7a":
            - link "30oz Perpetua SP PAT 7ro0nw7a":
              - /url: /marketing-dashboard/30oz%20-%20Perpetua%20-%20SP%20-%20PAT%20-%207ro0nw7a
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$224.62"
          - cell "80,118"
          - cell "505"
          - cell "0.63%"
          - cell "54"
          - cell "$1,804.29"
          - cell "8.03x"
          - cell "$4.16"
        - row "4/30/2025 30oz Perpetua SP PAT wzr0simo Arctic Tumblers Amazon $14.48 16,034 52 0.32% 3 $83.85 5.79x $4.83":
          - cell "4/30/2025"
          - cell "30oz Perpetua SP PAT wzr0simo":
            - link "30oz Perpetua SP PAT wzr0simo":
              - /url: /marketing-dashboard/30oz%20-%20Perpetua%20-%20SP%20-%20PAT%20-%20wzr0simo
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$14.48"
          - cell "16,034"
          - cell "52"
          - cell "0.32%"
          - cell "3"
          - cell "$83.85"
          - cell "5.79x"
          - cell "$4.83"
        - row "4/30/2025 30oz Tumbler Headline Ad Arctic Tumblers Amazon $0.00 0 0 0.00% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "30oz Tumbler Headline Ad":
            - link "30oz Tumbler Headline Ad":
              - /url: /marketing-dashboard/30oz%20Tumbler%20Headline%20Ad
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$0.00"
          - cell "0"
          - cell "0"
          - cell "0.00%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 7.5 Ounce Competitor Targeting Homesick Amazon $0.00 0 0 0.00% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "7.5 Ounce Competitor Targeting":
            - link "7.5 Ounce Competitor Targeting":
              - /url: /marketing-dashboard/7.5%20Ounce%20Competitor%20Targeting
          - cell "Homesick"
          - cell "Amazon"
          - cell "$0.00"
          - cell "0"
          - cell "0"
          - cell "0.00%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 AEROPRESSF 5623 GVP US DISC... Corretto Amazon $0.00 0 0 0.00% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "AEROPRESSF 5623 GVP US DISC...":
            - link "AEROPRESSF 5623 GVP US DISC...":
              - /url: /marketing-dashboard/AEROPRESSF_5623_GVP_US_DISCOVERY_
          - cell "Corretto"
          - cell "Amazon"
          - cell "$0.00"
          - cell "0"
          - cell "0"
          - cell "0.00%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 ASC+ Shopping Canada Prospe... Rachel Shopify $4,123.80 336,116 4,453 1.32% 121 $14,211.00 3.45x $34.08":
          - cell "4/30/2025"
          - cell "ASC+ Shopping Canada Prospe...":
            - link "ASC+ Shopping Canada Prospe...":
              - /url: /marketing-dashboard/ASC%2B%20Shopping%20%7C%20Canada%20%7C%20Prospecting
          - cell "Rachel"
          - cell "Shopify"
          - cell "$4,123.80"
          - cell "336,116"
          - cell "4,453"
          - cell "1.32%"
          - cell "121"
          - cell "$14,211.00"
          - cell "3.45x"
          - cell "$34.08"
        - row "4/30/2025 AT Brand Defence Storefront... Arctic Tumblers Amazon $0.00 0 0 0.00% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "AT Brand Defence Storefront...":
            - link "AT Brand Defence Storefront...":
              - /url: /marketing-dashboard/AT%20-%20Brand%20Defence%20-%20Storefront%20Headline
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$0.00"
          - cell "0"
          - cell "0"
          - cell "0.00%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 AT All Tumblers (Rank) (Spo... Arctic Tumblers Amazon $0.00 0 0 0.00% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "AT All Tumblers (Rank) (Spo...":
            - link "AT All Tumblers (Rank) (Spo...":
              - /url: /marketing-dashboard/AT%20%7C%20All%20Tumblers%20(Rank)%20%7C%20(Sponsored%20Brand)%20(Storefront%202)
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$0.00"
          - cell "0"
          - cell "0"
          - cell "0.00%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 AT All Tumblers (Rank) (Spo... Arctic Tumblers Amazon $0.00 0 0 0.00% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "AT All Tumblers (Rank) (Spo...":
            - link "AT All Tumblers (Rank) (Spo...":
              - /url: /marketing-dashboard/AT%20%7C%20All%20Tumblers%20(Rank)%20%7C%20(Sponsored%20Brand)%20(Storefront)
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$0.00"
          - cell "0"
          - cell "0"
          - cell "0.00%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 AT Brand Headline Ad (Rank)... Arctic Tumblers Amazon $0.00 0 0 0.00% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "AT Brand Headline Ad (Rank)...":
            - link "AT Brand Headline Ad (Rank)...":
              - /url: /marketing-dashboard/AT%20%7C%20Brand%20Headline%20Ad%20(Rank)%20%7C%20(Manual)%20Keywords
          - cell "Arctic Tumblers"
          - cell "Amazon"
          - cell "$0.00"
          - cell "0"
          - cell "0"
          - cell "0.00%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 Arnette Catch All Revant Amazon $21.17 2,678 25 0.93% 2 $89.40 4.22x $10.59":
          - cell "4/30/2025"
          - cell "Arnette Catch All":
            - link "Arnette Catch All":
              - /url: /marketing-dashboard/Arnette%20Catch%20All
          - cell "Revant"
          - cell "Amazon"
          - cell "$21.17"
          - cell "2,678"
          - cell "25"
          - cell "0.93%"
          - cell "2"
          - cell "$89.40"
          - cell "4.22x"
          - cell "$10.59"
        - row "4/30/2025 B0068709GY Polarized Holbrook Revant Amazon $730.59 141,072 342 0.24% 35 $2,099.80 2.87x $20.87":
          - cell "4/30/2025"
          - cell "B0068709GY Polarized Holbrook":
            - link "B0068709GY Polarized Holbrook":
              - /url: /marketing-dashboard/B0068709GY%20-%20Polarized%20Holbrook
          - cell "Revant"
          - cell "Amazon"
          - cell "$730.59"
          - cell "141,072"
          - cell "342"
          - cell "0.24%"
          - cell "35"
          - cell "$2,099.80"
          - cell "2.87x"
          - cell "$20.87"
        - row "4/30/2025 B0068709GY Polarized Holbro... Revant Amazon $368.99 39,521 86 0.22% 15 $797.13 2.16x $24.60":
          - cell "4/30/2025"
          - cell "B0068709GY Polarized Holbro...":
            - link "B0068709GY Polarized Holbro...":
              - /url: /marketing-dashboard/B0068709GY%20-%20Polarized%20Holbrook%20-%20Manual
          - cell "Revant"
          - cell "Amazon"
          - cell "$368.99"
          - cell "39,521"
          - cell "86"
          - cell "0.22%"
          - cell "15"
          - cell "$797.13"
          - cell "2.16x"
          - cell "$24.60"
        - row "4/30/2025 B Search AU-NZ Non-Brand Plano Revant Shopify $424.96 17,432 437 2.51% 13 $1,282.90 3.02x $32.69":
          - cell "4/30/2025"
          - cell "B Search AU-NZ Non-Brand Plano":
            - link "B Search AU-NZ Non-Brand Plano":
              - /url: /marketing-dashboard/B_Search_AU-NZ_Non-Brand_Plano
          - cell "Revant"
          - cell "Shopify"
          - cell "$424.96"
          - cell "17,432"
          - cell "437"
          - cell "2.51%"
          - cell "13"
          - cell "$1,282.90"
          - cell "3.02x"
          - cell "$32.69"
        - row "4/30/2025 B Search CA DSA Plano Revant Shopify $14.80 507 24 4.73% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "B Search CA DSA Plano":
            - link "B Search CA DSA Plano":
              - /url: /marketing-dashboard/B_Search_CA_DSA_Plano
          - cell "Revant"
          - cell "Shopify"
          - cell "$14.80"
          - cell "507"
          - cell "24"
          - cell "4.73%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 B Search CA Non-Brand Plano Revant Shopify $159.99 11,235 226 2.01% 3 $324.58 2.03x $53.33":
          - cell "4/30/2025"
          - cell "B Search CA Non-Brand Plano":
            - link "B Search CA Non-Brand Plano":
              - /url: /marketing-dashboard/B_Search_CA_Non-Brand_Plano
          - cell "Revant"
          - cell "Shopify"
          - cell "$159.99"
          - cell "11,235"
          - cell "226"
          - cell "2.01%"
          - cell "3"
          - cell "$324.58"
          - cell "2.03x"
          - cell "$53.33"
        - row "4/30/2025 B Search EU-UK Non-Brand Plano Revant Shopify $60.36 22,043 165 0.75% 0 $0.00 0.00x $0.00":
          - cell "4/30/2025"
          - cell "B Search EU-UK Non-Brand Plano":
            - link "B Search EU-UK Non-Brand Plano":
              - /url: /marketing-dashboard/B_Search_EU-UK_Non-Brand_Plano
          - cell "Revant"
          - cell "Shopify"
          - cell "$60.36"
          - cell "22,043"
          - cell "165"
          - cell "0.75%"
          - cell "0"
          - cell "$0.00"
          - cell "0.00x"
          - cell "$0.00"
        - row "4/30/2025 B Search US DSA Plano Revant Shopify $270.58 19,561 253 1.29% 7 $658.73 2.43x $38.65":
          - cell "4/30/2025"
          - cell "B Search US DSA Plano":
            - link "B Search US DSA Plano":
              - /url: /marketing-dashboard/B_Search_US_DSA_Plano
          - cell "Revant"
          - cell "Shopify"
          - cell "$270.58"
          - cell "19,561"
          - cell "253"
          - cell "1.29%"
          - cell "7"
          - cell "$658.73"
          - cell "2.43x"
          - cell "$38.65"
        - row "4/30/2025 B Search US Non-Brand Frame... Revant Shopify $532.27 37,808 601 1.59% 14 $1,251.85 2.35x $38.02":
          - cell "4/30/2025"
          - cell "B Search US Non-Brand Frame...":
            - link "B Search US Non-Brand Frame...":
              - /url: /marketing-dashboard/B_Search_US_Non-Brand_Frames-Plano
          - cell "Revant"
          - cell "Shopify"
          - cell "$532.27"
          - cell "37,808"
          - cell "601"
          - cell "1.59%"
          - cell "14"
          - cell "$1,251.85"
          - cell "2.35x"
          - cell "$38.02"
        - row "4/30/2025 B Search US Non-Brand Plano Revant Shopify $4,772.77 109,814 3,626 3.30% 175 $15,355.68 3.22x $27.27":
          - cell "4/30/2025"
          - cell "B Search US Non-Brand Plano":
            - link "B Search US Non-Brand Plano":
              - /url: /marketing-dashboard/B_Search_US_Non-Brand_Plano
          - cell "Revant"
          - cell "Shopify"
          - cell "$4,772.77"
          - cell "109,814"
          - cell "3,626"
          - cell "3.30%"
          - cell "175"
          - cell "$15,355.68"
          - cell "3.22x"
          - cell "$27.27"
        - row "4/30/2025 B Search US Non-Brand Plano... Revant Shopify $902.72 31,003 867 2.80% 20 $1,563.05 1.73x $45.14":
          - cell "4/30/2025"
          - cell "B Search US Non-Brand Plano...":
            - link "B Search US Non-Brand Plano...":
              - /url: /marketing-dashboard/B_Search_US_Non-Brand_Plano-OtherBrands
          - cell "Revant"
          - cell "Shopify"
          - cell "$902.72"
          - cell "31,003"
          - cell "867"
          - cell "2.80%"
          - cell "20"
          - cell "$1,563.05"
          - cell "1.73x"
          - cell "$45.14"
        - row "4/30/2025 B Search US Non-Brand Plano... Revant Shopify $927.74 8,005 701 8.76% 41 $3,985.04 4.30x $22.63":
          - cell "4/30/2025"
          - cell "B Search US Non-Brand Plano...":
            - link "B Search US Non-Brand Plano...":
              - /url: /marketing-dashboard/B_Search_US_Non-Brand_Plano_Organic-TopPerformer
          - cell "Revant"
          - cell "Shopify"
          - cell "$927.74"
          - cell "8,005"
          - cell "701"
          - cell "8.76%"
          - cell "41"
          - cell "$3,985.04"
          - cell "4.30x"
          - cell "$22.63"
        - row "4/30/2025 B Shopping US Plano Oakley Revant Shopify $2,578.43 46,834 1,025 2.19% 74 $7,073.65 2.74x $34.84":
          - cell "4/30/2025"
          - cell "B Shopping US Plano Oakley":
            - link "B Shopping US Plano Oakley":
              - /url: /marketing-dashboard/B_Shopping_US_Plano_Oakley
          - cell "Revant"
          - cell "Shopify"
          - cell "$2,578.43"
          - cell "46,834"
          - cell "1,025"
          - cell "2.19%"
          - cell "74"
          - cell "$7,073.65"
          - cell "2.74x"
          - cell "$34.84"
        - row "4/30/2025 B SmartShopping INTL Plano Revant Shopify $766.25 40,589 546 1.35% 13 $1,519.25 1.98x $58.94":
          - cell "4/30/2025"
          - cell "B SmartShopping INTL Plano":
            - link "B SmartShopping INTL Plano":
              - /url: /marketing-dashboard/B_SmartShopping_INTL_Plano
          - cell "Revant"
          - cell "Shopify"
          - cell "$766.25"
          - cell "40,589"
          - cell "546"
          - cell "1.35%"
          - cell "13"
          - cell "$1,519.25"
          - cell "1.98x"
          - cell "$58.94"
    - text: 4016 row(s).
    - paragraph: Rows per page
    - combobox: "50"
    - text: Page 1 of 81
    - button "Go to first page" [disabled]:
      - text: Go to first page
      - img
    - button "Go to previous page" [disabled]:
      - text: Go to previous page
      - img
    - button "Go to next page":
      - text: Go to next page
      - img
    - button "Go to last page":
      - text: Go to last page
      - img
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 | import {
   3 |   waitForPageLoad,
   4 |   signInAsAdmin,
   5 |   takeScreenshot,
   6 |   testChartRendering,
   7 |   testFilters,
   8 |   testDataTable,
   9 |   testPagination,
   10 |   testSearch,
   11 |   waitForLoadingToComplete,
   12 |   testAccessibility,
   13 |   verifyPageElements
   14 | } from './utils/test-helpers';
   15 |
   16 | test.describe('Comprehensive Marketing Dashboard Testing', () => {
   17 |   test.beforeEach(async ({ page }) => {
   18 |     // Sign in as admin for all tests
   19 |     await signInAsAdmin(page);
   20 |   });
   21 |
   22 |   test('should load marketing dashboard with all components', async ({ page }) => {
   23 |     await page.goto('/marketing-dashboard');
   24 |     await waitForPageLoad(page);
   25 |     await waitForLoadingToComplete(page);
   26 |
   27 |     // Verify page title and main elements
>  28 |     await expect(page).toHaveTitle(/Marketing Dashboard|NOLK/);
      |                        ^ Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)
   29 |     
   30 |     // Check for main components
   31 |     const expectedElements = [
   32 |       'main, [role="main"]',
   33 |       'nav, [role="navigation"]',
   34 |       'h1, h2, [data-testid*="title"]'
   35 |     ];
   36 |     
   37 |     await verifyPageElements(page, expectedElements);
   38 |     await takeScreenshot(page, 'marketing-dashboard-main');
   39 |   });
   40 |
   41 |   test('should display marketing KPI cards', async ({ page }) => {
   42 |     await page.goto('/marketing-dashboard');
   43 |     await waitForPageLoad(page);
   44 |     await waitForLoadingToComplete(page);
   45 |
   46 |     // Look for marketing-specific KPI cards
   47 |     const kpiCards = page.locator('[data-testid*="kpi"], .kpi-card, [class*="kpi"]');
   48 |     const cardCount = await kpiCards.count();
   49 |
   50 |     if (cardCount > 0) {
   51 |       // Verify KPI cards are visible
   52 |       await expect(kpiCards.first()).toBeVisible();
   53 |       
   54 |       // Check for marketing-specific metrics
   55 |       const marketingMetrics = page.locator('text=/spend|roas|acos|cpc|cpm|impressions|clicks/i');
   56 |       const metricsCount = await marketingMetrics.count();
   57 |       
   58 |       console.log(`Found ${cardCount} marketing KPI cards with ${metricsCount} marketing metrics`);
   59 |     }
   60 |
   61 |     await takeScreenshot(page, 'marketing-dashboard-kpi-cards');
   62 |   });
   63 |
   64 |   test('should display campaign data table with proper functionality', async ({ page }) => {
   65 |     await page.goto('/marketing-dashboard');
   66 |     await waitForPageLoad(page);
   67 |     await waitForLoadingToComplete(page);
   68 |
   69 |     // Test data table functionality
   70 |     const tableInfo = await testDataTable(page);
   71 |     
   72 |     if (tableInfo.headerCount > 0) {
   73 |       console.log(`Campaign table has ${tableInfo.headerCount} headers and ${tableInfo.rowCount} rows`);
   74 |       
   75 |       // Test table sorting if available
   76 |       const sortableHeaders = page.locator('th[role="columnheader"], th.sortable, th[data-sortable]');
   77 |       const sortableCount = await sortableHeaders.count();
   78 |       
   79 |       if (sortableCount > 0) {
   80 |         await sortableHeaders.first().click();
   81 |         await page.waitForTimeout(1000);
   82 |         await takeScreenshot(page, 'marketing-dashboard-table-sorted');
   83 |       }
   84 |       
   85 |       // Test pagination
   86 |       const paginationInfo = await testPagination(page);
   87 |       if (paginationInfo.hasPagination) {
   88 |         console.log('Table has pagination controls');
   89 |         await takeScreenshot(page, 'marketing-dashboard-pagination');
   90 |       }
   91 |     }
   92 |   });
   93 |
   94 |   test('should render spend breakdown charts', async ({ page }) => {
   95 |     await page.goto('/marketing-dashboard');
   96 |     await waitForPageLoad(page);
   97 |     await waitForLoadingToComplete(page);
   98 |
   99 |     // Test chart rendering for marketing data
  100 |     const charts = await testChartRendering(page);
  101 |     
  102 |     // Look for spend breakdown specific charts
  103 |     const spendCharts = page.locator('[data-testid*="spend"], [class*="spend"], text=/spend breakdown/i');
  104 |     const spendChartCount = await spendCharts.count();
  105 |     
  106 |     console.log(`Found ${spendChartCount} spend-related chart elements`);
  107 |     await takeScreenshot(page, 'marketing-dashboard-spend-charts');
  108 |   });
  109 |
  110 |   test('should have functional marketing filters', async ({ page }) => {
  111 |     await page.goto('/marketing-dashboard');
  112 |     await waitForPageLoad(page);
  113 |     await waitForLoadingToComplete(page);
  114 |
  115 |     // Test filter functionality
  116 |     const activeFilters = await testFilters(page);
  117 |     console.log(`Found ${activeFilters.length} active filters`);
  118 |
  119 |     // Test date range filter
  120 |     const dateInputs = page.locator('input[type="date"]');
  121 |     const dateCount = await dateInputs.count();
  122 |     
  123 |     if (dateCount > 0) {
  124 |       await dateInputs.first().fill('2024-01-01');
  125 |       if (dateCount > 1) {
  126 |         await dateInputs.nth(1).fill('2024-12-31');
  127 |       }
  128 |       await page.waitForTimeout(2000);
```