# Test info

- Name: Comprehensive Application Test Suite >> should verify all navigation links work
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:149:7

# Error details

```
Error: locator.isVisible: Error: strict mode violation: locator('nav a:has-text("Dashboard")') resolved to 2 elements:
    1) <a href="/" title="Dashboard" class="text-muted-foreground hover:text-foreground truncate hidden sm:block max-w-[150px]">Dashboard</a> aka getByRole('link', { name: 'Dashboard' }).nth(2)
    2) <a title="Dashboard" href="/dashboard" class="font-medium text-foreground truncate max-w-[200px] sm:max-w-[300px]">Dashboard</a> aka getByRole('link', { name: 'Dashboard' }).nth(3)

Call log:
    - checking visibility of locator('nav a:has-text("Dashboard")')

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:170:25
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Dashboard":
      - /url: /dashboard
  - main:
    - heading "Dashboard" [level=1]
    - heading "Overview of your business performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Countries"
    - button "Sales Channels"
    - text: Master KPI Comparison Compare 'Gross Revenue' across selected brands using a line chart.
    - combobox "Select KPI": Gross Revenue
    - img: 2/28/2025 3/31/2025 4/30/2025 0 550K 1M 2M 2M
    - text: Brands Click to toggle. Sorted by latest KPI.
    - list:
      - button "Toggle visibility for Revant" [pressed]: Revant
      - button "Toggle visibility for Qalo" [pressed]: Qalo
      - button "Toggle visibility for Ergonofis" [pressed]: Ergonofis
      - button "Toggle visibility for Opposite Wall" [pressed]: Opposite Wall
      - button "Toggle visibility for Homesick" [pressed]: Homesick
      - button "Toggle visibility for Gravity" [pressed]: Gravity
      - button "Toggle visibility for Freakmount" [pressed]: Freakmount
      - button "Toggle visibility for MiHIGH" [pressed]: MiHIGH
      - button "Toggle visibility for Rose Boreal" [pressed]: Rose Boreal
      - button "Toggle visibility for Rachel" [pressed]: Rachel
      - button "Toggle visibility for Kana" [pressed]: Kana
      - button "Toggle visibility for Arctic Tumblers" [pressed]: Arctic Tumblers
      - button "Toggle visibility for Loctote" [pressed]: Loctote
      - button "Toggle visibility for Proper Pour" [pressed]: Proper Pour
      - button "Toggle visibility for Love Your Melon" [pressed]: Love Your Melon
      - button "Toggle visibility for Wolf & Grizzly" [pressed]: Wolf & Grizzly
      - button "Toggle visibility for Go Green" [pressed]: Go Green
      - button "Toggle visibility for Corretto" [pressed]: Corretto
      - button "Toggle visibility for Alex Bottle" [pressed]: Alex Bottle
    - heading "Key Performance Indicators" [level=3]
    - text: Revenue
    - button "KPI Definition"
    - text: Discount
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: Refunds
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: Net Revenue
    - button "KPI Definition"
    - text: Landed Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: Fulfillment Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: Transaction Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: Gross Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: Adspend
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: Contribution Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   70 |           hasErrors: true,
   71 |           status: 'FAIL',
   72 |           error: error.toString()
   73 |         });
   74 |       }
   75 |     }
   76 |
   77 |     // Test admin pages
   78 |     console.log('🔐 Testing admin pages...');
   79 |     const adminPages = [
   80 |       { name: 'Admin Dashboard', url: '/admin' },
   81 |       { name: 'Admin Users', url: '/admin/users' },
   82 |       { name: 'Admin Roles', url: '/admin/roles' },
   83 |       { name: 'Admin Permissions', url: '/admin/permissions' }
   84 |     ];
   85 |
   86 |     for (const adminPage of adminPages) {
   87 |       try {
   88 |         await page.goto(adminPage.url);
   89 |         await waitForPageLoad(page);
   90 |         
   91 |         // Verify admin access
   92 |         expect(page.url()).toContain('/admin');
   93 |         expect(page.url()).not.toContain('/auth/signin');
   94 |         
   95 |         await takeScreenshot(page, `smoke-test-${adminPage.name.toLowerCase().replace(' ', '-')}`);
   96 |         console.log(`✅ ${adminPage.name}: Accessible`);
   97 |         
   98 |       } catch (error) {
   99 |         console.log(`❌ ${adminPage.name}: FAILED - ${error}`);
  100 |       }
  101 |     }
  102 |
  103 |     // Print summary
  104 |     console.log('\n📊 SMOKE TEST SUMMARY:');
  105 |     console.log('========================');
  106 |     results.forEach(result => {
  107 |       const status = result.status === 'PASS' ? '✅' : '❌';
  108 |       console.log(`${status} ${result.page}: ${result.loadTime}ms`);
  109 |     });
  110 |
  111 |     const passedTests = results.filter(r => r.status === 'PASS').length;
  112 |     const totalTests = results.length;
  113 |     console.log(`\n🎯 Results: ${passedTests}/${totalTests} tests passed`);
  114 |     
  115 |     // Ensure at least 80% of tests pass
  116 |     expect(passedTests / totalTests).toBeGreaterThanOrEqual(0.8);
  117 |   });
  118 |
  119 |   test('should verify authentication system works correctly', async ({ page }) => {
  120 |     console.log('🔐 Testing authentication system...');
  121 |     
  122 |     // Test sign out
  123 |     await page.goto('/dashboard');
  124 |     await waitForPageLoad(page);
  125 |     
  126 |     // Look for user menu or sign out option
  127 |     const userMenu = page.locator('[data-testid="user-menu"], .user-menu, button:has-text("Sign out")');
  128 |     
  129 |     if (await userMenu.isVisible()) {
  130 |       await userMenu.click();
  131 |       await page.waitForTimeout(1000);
  132 |       
  133 |       const signOutButton = page.locator('button:has-text("Sign out"), [data-testid="sign-out"]');
  134 |       if (await signOutButton.isVisible()) {
  135 |         await signOutButton.click();
  136 |         await page.waitForURL('**/auth/signin');
  137 |         console.log('✅ Sign out: Working');
  138 |       }
  139 |     }
  140 |     
  141 |     // Test sign in again
  142 |     await signInAsAdmin(page);
  143 |     expect(page.url()).toContain('/dashboard');
  144 |     console.log('✅ Sign in: Working');
  145 |     
  146 |     await takeScreenshot(page, 'auth-system-test');
  147 |   });
  148 |
  149 |   test('should verify all navigation links work', async ({ page }) => {
  150 |     console.log('🧭 Testing navigation system...');
  151 |     
  152 |     await page.goto('/dashboard');
  153 |     await waitForPageLoad(page);
  154 |     
  155 |     // Test main navigation
  156 |     const navItems = [
  157 |       'Dashboard',
  158 |       'Brand Deep Dive', 
  159 |       'Marketing Dashboard',
  160 |       'Executive Summary',
  161 |       'Budget',
  162 |       'AI Assistant'
  163 |     ];
  164 |     
  165 |     let workingNavItems = 0;
  166 |     
  167 |     for (const item of navItems) {
  168 |       const navLink = page.locator(`nav a:has-text("${item}")`);
  169 |       
> 170 |       if (await navLink.isVisible()) {
      |                         ^ Error: locator.isVisible: Error: strict mode violation: locator('nav a:has-text("Dashboard")') resolved to 2 elements:
  171 |         try {
  172 |           await navLink.click();
  173 |           await waitForPageLoad(page);
  174 |           
  175 |           // Verify navigation worked
  176 |           const currentUrl = page.url();
  177 |           const expectedPath = item.toLowerCase().replace(' ', '-');
  178 |           
  179 |           if (currentUrl.includes(expectedPath) || currentUrl.includes('/dashboard')) {
  180 |             workingNavItems++;
  181 |             console.log(`✅ Navigation to ${item}: Working`);
  182 |           }
  183 |         } catch (error) {
  184 |           console.log(`❌ Navigation to ${item}: Failed`);
  185 |         }
  186 |       }
  187 |     }
  188 |     
  189 |     console.log(`🎯 Navigation: ${workingNavItems}/${navItems.length} links working`);
  190 |     expect(workingNavItems).toBeGreaterThanOrEqual(navItems.length * 0.8);
  191 |     
  192 |     await takeScreenshot(page, 'navigation-test');
  193 |   });
  194 |
  195 |   test('should verify data loading and display', async ({ page }) => {
  196 |     console.log('📊 Testing data loading and display...');
  197 |     
  198 |     const dataPages = [
  199 |       { name: 'Dashboard KPIs', url: '/dashboard', selector: '[data-testid*="kpi"], .kpi-card' },
  200 |       { name: 'Marketing Campaigns', url: '/marketing-dashboard', selector: 'table, [data-testid*="campaign"]' },
  201 |       { name: 'Brand Data', url: '/brand-deep-dive', selector: '[data-testid*="brand"], .brand-data' }
  202 |     ];
  203 |     
  204 |     for (const dataPage of dataPages) {
  205 |       try {
  206 |         await page.goto(dataPage.url);
  207 |         await waitForPageLoad(page);
  208 |         await waitForLoadingToComplete(page);
  209 |         
  210 |         // Check for data elements
  211 |         const dataElements = page.locator(dataPage.selector);
  212 |         const elementCount = await dataElements.count();
  213 |         
  214 |         console.log(`📈 ${dataPage.name}: ${elementCount} data elements found`);
  215 |         
  216 |         if (elementCount > 0) {
  217 |           console.log(`✅ ${dataPage.name}: Data loaded successfully`);
  218 |         } else {
  219 |           console.log(`⚠️ ${dataPage.name}: No data elements found`);
  220 |         }
  221 |         
  222 |       } catch (error) {
  223 |         console.log(`❌ ${dataPage.name}: Failed to load - ${error}`);
  224 |       }
  225 |     }
  226 |     
  227 |     await takeScreenshot(page, 'data-loading-test');
  228 |   });
  229 |
  230 |   test('should verify responsive design works', async ({ page }) => {
  231 |     console.log('📱 Testing responsive design...');
  232 |     
  233 |     const viewports = [
  234 |       { width: 1920, height: 1080, name: 'Desktop' },
  235 |       { width: 1024, height: 768, name: 'Tablet' },
  236 |       { width: 375, height: 667, name: 'Mobile' }
  237 |     ];
  238 |     
  239 |     await page.goto('/dashboard');
  240 |     await waitForPageLoad(page);
  241 |     
  242 |     for (const viewport of viewports) {
  243 |       await page.setViewportSize(viewport);
  244 |       await page.waitForTimeout(1000);
  245 |       
  246 |       // Verify main content is visible
  247 |       const mainContent = page.locator('main, [role="main"]');
  248 |       const isVisible = await mainContent.isVisible();
  249 |       
  250 |       if (isVisible) {
  251 |         console.log(`✅ ${viewport.name} (${viewport.width}x${viewport.height}): Layout working`);
  252 |       } else {
  253 |         console.log(`❌ ${viewport.name}: Layout broken`);
  254 |       }
  255 |       
  256 |       await takeScreenshot(page, `responsive-${viewport.name.toLowerCase()}`);
  257 |     }
  258 |   });
  259 |
  260 |   test('should verify accessibility standards', async ({ page }) => {
  261 |     console.log('♿ Testing accessibility standards...');
  262 |     
  263 |     const accessibilityPages = ['/dashboard', '/marketing-dashboard', '/admin'];
  264 |     
  265 |     for (const url of accessibilityPages) {
  266 |       await page.goto(url);
  267 |       await waitForPageLoad(page);
  268 |       
  269 |       const accessibilityInfo = await testAccessibility(page);
  270 |       
```