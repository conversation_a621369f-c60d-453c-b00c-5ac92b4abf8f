# Test info

- Name: Comprehensive Error Handling and Edge Cases >> should handle large data sets without crashing
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:154:7

# Error details

```
Error: locator.isVisible: Error: strict mode violation: locator('main') resolved to 2 elements:
    1) <main data-slot="sidebar-inset" class="bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2">…</main> aka getByText('Toggle SidebarDashboardMarketing DashboardMarketing DashboardTrack and analyze')
    2) <main class="flex-1 overflow-auto">…</main> aka getByRole('main').filter({ hasText: 'Toggle' }).getByRole('main')

Call log:
    - checking visibility of locator('main')

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:170:55
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
  - main:
    - heading "Marketing Dashboard" [level=1]
    - heading "Track and analyze your marketing performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2020-01-01
    - text: End Date
    - textbox "End Date": 2024-12-31
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Sales Channels"
    - heading "Overall KPI Summary" [level=3]
    - text: Total Spend
    - button "KPI Definition"
    - text: Total Impressions
    - button "KPI Definition"
    - text: Total Clicks
    - button "KPI Definition"
    - text: Total Conversions
    - button "KPI Definition"
    - text: Total Conversion Value
    - button "KPI Definition"
    - text: Overall ROAS
    - button "KPI Definition"
    - text: Overall CPA
    - button "KPI Definition"
    - text: Overall CTR
    - button "KPI Definition"
    - heading "Spend Breakdown" [level=3]
    - text: Spend Breakdown By Sales Channel Campaign Performance
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   70 |   test('should handle API errors gracefully', async ({ page }) => {
   71 |     // Intercept API calls and return errors
   72 |     await page.route('**/api/dashboard/**', route => {
   73 |       route.fulfill({
   74 |         status: 500,
   75 |         contentType: 'application/json',
   76 |         body: JSON.stringify({ error: 'Internal Server Error' })
   77 |       });
   78 |     });
   79 |     
   80 |     await page.goto('/dashboard');
   81 |     await waitForPageLoad(page);
   82 |     await waitForLoadingToComplete(page);
   83 |     
   84 |     // Should show error message or fallback content
   85 |     const errorMessage = page.locator('text=/error|failed|something went wrong/i');
   86 |     const hasErrorHandling = await errorMessage.isVisible();
   87 |     
   88 |     console.log(`API error handling: ${hasErrorHandling ? 'Detected' : 'Not detected'}`);
   89 |     await takeScreenshot(page, 'api-error-500');
   90 |   });
   91 |
   92 |   test('should handle authentication errors', async ({ page }) => {
   93 |     // Test without authentication
   94 |     const unauthenticatedPage = await page.context().newPage();
   95 |     
   96 |     const protectedPages = [
   97 |       '/dashboard',
   98 |       '/marketing-dashboard',
   99 |       '/brand-deep-dive',
  100 |       '/executive-summary',
  101 |       '/budget',
  102 |       '/ai-assistant',
  103 |       '/admin'
  104 |     ];
  105 |     
  106 |     for (const url of protectedPages) {
  107 |       await unauthenticatedPage.goto(url);
  108 |       await waitForPageLoad(unauthenticatedPage);
  109 |       
  110 |       // Should redirect to sign-in page
  111 |       const currentUrl = unauthenticatedPage.url();
  112 |       expect(currentUrl).toContain('/auth/signin');
  113 |       
  114 |       console.log(`${url} -> redirected to sign-in`);
  115 |     }
  116 |     
  117 |     await takeScreenshot(unauthenticatedPage, 'auth-redirect');
  118 |     await unauthenticatedPage.close();
  119 |   });
  120 |
  121 |   test('should handle form validation errors', async ({ page }) => {
  122 |     await page.goto('/admin/users');
  123 |     await waitForPageLoad(page);
  124 |     
  125 |     // Look for add user button
  126 |     const addButton = page.locator('button:has-text("Add"), button:has-text("Create")');
  127 |     
  128 |     if (await addButton.isVisible()) {
  129 |       await addButton.click();
  130 |       await page.waitForTimeout(1000);
  131 |       
  132 |       // Look for form
  133 |       const form = page.locator('form');
  134 |       
  135 |       if (await form.isVisible()) {
  136 |         // Try to submit empty form
  137 |         const submitButton = form.locator('button[type="submit"], button:has-text("Save"), button:has-text("Create")');
  138 |         
  139 |         if (await submitButton.isVisible()) {
  140 |           await submitButton.click();
  141 |           await page.waitForTimeout(1000);
  142 |           
  143 |           // Should show validation errors
  144 |           const validationErrors = page.locator('.error, .invalid, text=/required|invalid/i');
  145 |           const hasValidation = await validationErrors.count() > 0;
  146 |           
  147 |           console.log(`Form validation: ${hasValidation ? 'Working' : 'Not detected'}`);
  148 |           await takeScreenshot(page, 'form-validation-errors');
  149 |         }
  150 |       }
  151 |     }
  152 |   });
  153 |
  154 |   test('should handle large data sets without crashing', async ({ page }) => {
  155 |     // Test with potentially large datasets
  156 |     await page.goto('/marketing-dashboard');
  157 |     await waitForPageLoad(page);
  158 |     
  159 |     // Set a very large date range to potentially load lots of data
  160 |     const startDateInput = page.locator('input[type="date"]').first();
  161 |     const endDateInput = page.locator('input[type="date"]').last();
  162 |     
  163 |     if (await startDateInput.isVisible() && await endDateInput.isVisible()) {
  164 |       await startDateInput.fill('2020-01-01');
  165 |       await endDateInput.fill('2024-12-31');
  166 |       await page.waitForTimeout(5000);
  167 |       await waitForLoadingToComplete(page);
  168 |       
  169 |       // Page should still be responsive
> 170 |       const isResponsive = await page.locator('main').isVisible();
      |                                                       ^ Error: locator.isVisible: Error: strict mode violation: locator('main') resolved to 2 elements:
  171 |       expect(isResponsive).toBe(true);
  172 |       
  173 |       console.log('Large dataset handling: Page remained responsive');
  174 |       await takeScreenshot(page, 'large-dataset-handling');
  175 |     }
  176 |   });
  177 |
  178 |   test('should handle browser back/forward navigation', async ({ page }) => {
  179 |     // Navigate through several pages
  180 |     await page.goto('/dashboard');
  181 |     await waitForPageLoad(page);
  182 |     
  183 |     await page.goto('/marketing-dashboard');
  184 |     await waitForPageLoad(page);
  185 |     
  186 |     await page.goto('/brand-deep-dive');
  187 |     await waitForPageLoad(page);
  188 |     
  189 |     // Test back navigation
  190 |     await page.goBack();
  191 |     await waitForPageLoad(page);
  192 |     expect(page.url()).toContain('/marketing-dashboard');
  193 |     
  194 |     await page.goBack();
  195 |     await waitForPageLoad(page);
  196 |     expect(page.url()).toContain('/dashboard');
  197 |     
  198 |     // Test forward navigation
  199 |     await page.goForward();
  200 |     await waitForPageLoad(page);
  201 |     expect(page.url()).toContain('/marketing-dashboard');
  202 |     
  203 |     console.log('Browser navigation: Working correctly');
  204 |     await takeScreenshot(page, 'browser-navigation');
  205 |   });
  206 |
  207 |   test('should handle page refresh and state preservation', async ({ page }) => {
  208 |     await page.goto('/dashboard');
  209 |     await waitForPageLoad(page);
  210 |     
  211 |     // Apply some filters if available
  212 |     const brandFilter = page.locator('select[name*="brand"]');
  213 |     if (await brandFilter.isVisible()) {
  214 |       await brandFilter.selectOption({ index: 1 });
  215 |       await page.waitForTimeout(2000);
  216 |     }
  217 |     
  218 |     // Refresh the page
  219 |     await page.reload();
  220 |     await waitForPageLoad(page);
  221 |     await waitForLoadingToComplete(page);
  222 |     
  223 |     // Page should load successfully after refresh
  224 |     const isLoaded = await page.locator('main').isVisible();
  225 |     expect(isLoaded).toBe(true);
  226 |     
  227 |     console.log('Page refresh: Handled successfully');
  228 |     await takeScreenshot(page, 'page-refresh');
  229 |   });
  230 |
  231 |   test('should handle concurrent user actions', async ({ page }) => {
  232 |     await page.goto('/marketing-dashboard');
  233 |     await waitForPageLoad(page);
  234 |     await waitForLoadingToComplete(page);
  235 |     
  236 |     // Simulate rapid user interactions
  237 |     const actions = [
  238 |       () => page.locator('select[name*="currency"]').selectOption({ index: 1 }),
  239 |       () => page.locator('input[type="date"]').first().fill('2024-01-01'),
  240 |       () => page.locator('button').first().click(),
  241 |       () => page.keyboard.press('Tab'),
  242 |       () => page.mouse.move(100, 100)
  243 |     ];
  244 |     
  245 |     // Execute actions rapidly
  246 |     const promises = actions.map(action => action().catch(() => {}));
  247 |     await Promise.all(promises);
  248 |     
  249 |     await page.waitForTimeout(2000);
  250 |     await waitForLoadingToComplete(page);
  251 |     
  252 |     // Page should still be functional
  253 |     const isStillFunctional = await page.locator('main').isVisible();
  254 |     expect(isStillFunctional).toBe(true);
  255 |     
  256 |     console.log('Concurrent actions: Handled successfully');
  257 |     await takeScreenshot(page, 'concurrent-actions');
  258 |   });
  259 |
  260 |   test('should handle memory leaks during navigation', async ({ page }) => {
  261 |     const pages = ['/dashboard', '/marketing-dashboard', '/brand-deep-dive', '/executive-summary'];
  262 |     let initialMemory = 0;
  263 |     
  264 |     // Get initial memory usage
  265 |     const memory = await page.evaluate(() => {
  266 |       if ('memory' in performance) {
  267 |         return (performance as any).memory.usedJSHeapSize;
  268 |       }
  269 |       return 0;
  270 |     });
```