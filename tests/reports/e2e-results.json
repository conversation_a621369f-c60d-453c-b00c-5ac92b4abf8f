{"config": {"configFile": "/Users/<USER>/Projects/NOLK/nolk-v4/playwright.config.ts", "rootDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 8}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "tests/reports/e2e-html"}], ["json", {"outputFile": "tests/reports/e2e-results.json"}], ["junit", {"outputFile": "tests/reports/e2e-junit.xml"}], ["list", null], ["github", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 8, "webServer": {"command": "npm run dev", "url": "http://localhost:6699", "reuseExistingServer": true, "timeout": 120000, "stdout": "ignore", "stderr": "pipe"}}, "suites": [{"title": "comprehensive-admin-dashboard.spec.ts", "file": "comprehensive-admin-dashboard.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Admin Dashboard Testing", "file": "comprehensive-admin-dashboard.spec.ts", "line": 17, "column": 6, "specs": [{"title": "should load admin dashboard with proper authorization", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 27929, "error": {"message": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected pattern: \u001b[32m/Admin|NOLK/\u001b[39m\nReceived string:  \u001b[31m\"Create Next App\"\u001b[39m\nCall log:\n\u001b[2m  - expect.toHaveTitle with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    19 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"Create Next App\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected pattern: \u001b[32m/Admin|NOLK/\u001b[39m\nReceived string:  \u001b[31m\"Create Next App\"\u001b[39m\nCall log:\n\u001b[2m  - expect.toHaveTitle with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    19 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"Create Next App\"\u001b[22m\n\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:33:24", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts", "column": 24, "line": 33}, "snippet": "\u001b[0m \u001b[90m 31 |\u001b[39m\n \u001b[90m 32 |\u001b[39m     \u001b[90m// Verify page title and main elements\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/Admin|NOLK/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m     \n \u001b[90m 35 |\u001b[39m     \u001b[90m// Check for admin-specific elements\u001b[39m\n \u001b[90m 36 |\u001b[39m     \u001b[36mconst\u001b[39m expectedElements \u001b[33m=\u001b[39m [\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts", "column": 24, "line": 33}, "message": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator(':root')\nExpected pattern: \u001b[32m/Admin|NOLK/\u001b[39m\nReceived string:  \u001b[31m\"Create Next App\"\u001b[39m\nCall log:\n\u001b[2m  - expect.toHaveTitle with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator(':root')\u001b[22m\n\u001b[2m    19 × locator resolved to <html lang=\"en\">…</html>\u001b[22m\n\u001b[2m       - unexpected value \"Create Next App\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 31 |\u001b[39m\n \u001b[90m 32 |\u001b[39m     \u001b[90m// Verify page title and main elements\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/Admin|NOLK/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m     \n \u001b[90m 35 |\u001b[39m     \u001b[90m// Check for admin-specific elements\u001b[39m\n \u001b[90m 36 |\u001b[39m     \u001b[36mconst\u001b[39m expectedElements \u001b[33m=\u001b[39m [\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:33:24\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:11.907Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-admin-dashbo-722b6-d-with-proper-authorization-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-admin-dashbo-722b6-d-with-proper-authorization-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-admin-dashbo-722b6-d-with-proper-authorization-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts", "column": 24, "line": 33}}], "status": "unexpected"}], "id": "553aa5e56666af0f27b5-16b85f59f2456bf32e23", "file": "comprehensive-admin-dashboard.spec.ts", "line": 23, "column": 7}, {"title": "should display admin navigation menu", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 12946, "error": {"message": "Error: locator.isVisible: Error: strict mode violation: locator('nav a:has-text(\"Users\"), a[href*=\"users\"]') resolved to 2 elements:\n    1) <a href=\"/admin/users\" data-size=\"default\" data-active=\"false\" data-state=\"closed\" data-sidebar=\"menu-button\" data-slot=\"sidebar-menu-button\" class=\"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left outline-hidden ring-sidebar-ring transition-[width,height,padding] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabl…>…</a> aka getByRole('link', { name: 'Users', exact: true })\n    2) <a href=\"/admin/users\">…</a> aka getByRole('link', { name: 'User Management Manage users' })\n\nCall log:\n\u001b[2m    - checking visibility of locator('nav a:has-text(\"Users\"), a[href*=\"users\"]')\u001b[22m\n", "stack": "Error: locator.isVisible: Error: strict mode violation: locator('nav a:has-text(\"Users\"), a[href*=\"users\"]') resolved to 2 elements:\n    1) <a href=\"/admin/users\" data-size=\"default\" data-active=\"false\" data-state=\"closed\" data-sidebar=\"menu-button\" data-slot=\"sidebar-menu-button\" class=\"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left outline-hidden ring-sidebar-ring transition-[width,height,padding] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabl…>…</a> aka getByRole('link', { name: 'Users', exact: true })\n    2) <a href=\"/admin/users\">…</a> aka getByRole('link', { name: 'User Management Manage users' })\n\nCall log:\n\u001b[2m    - checking visibility of locator('nav a:has-text(\"Users\"), a[href*=\"users\"]')\u001b[22m\n\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:59:25", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts", "column": 25, "line": 59}, "snippet": "\u001b[0m \u001b[90m 57 |\u001b[39m     \u001b[36mfor\u001b[39m (\u001b[36mconst\u001b[39m item \u001b[36mof\u001b[39m adminNavItems) {\n \u001b[90m 58 |\u001b[39m       \u001b[36mconst\u001b[39m navItem \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`nav a:has-text(\"${item}\"), a[href*=\"${item.toLowerCase()}\"]`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 59 |\u001b[39m       \u001b[36mif\u001b[39m (\u001b[36mawait\u001b[39m navItem\u001b[33m.\u001b[39misVisible()) {\n \u001b[90m    |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 60 |\u001b[39m         foundNavItems\u001b[33m++\u001b[39m\u001b[33m;\u001b[39m\n \u001b[90m 61 |\u001b[39m       }\n \u001b[90m 62 |\u001b[39m     }\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts", "column": 25, "line": 59}, "message": "Error: locator.isVisible: Error: strict mode violation: locator('nav a:has-text(\"Users\"), a[href*=\"users\"]') resolved to 2 elements:\n    1) <a href=\"/admin/users\" data-size=\"default\" data-active=\"false\" data-state=\"closed\" data-sidebar=\"menu-button\" data-slot=\"sidebar-menu-button\" class=\"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left outline-hidden ring-sidebar-ring transition-[width,height,padding] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabl…>…</a> aka getByRole('link', { name: 'Users', exact: true })\n    2) <a href=\"/admin/users\">…</a> aka getByRole('link', { name: 'User Management Manage users' })\n\nCall log:\n\u001b[2m    - checking visibility of locator('nav a:has-text(\"Users\"), a[href*=\"users\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 57 |\u001b[39m     \u001b[36mfor\u001b[39m (\u001b[36mconst\u001b[39m item \u001b[36mof\u001b[39m adminNavItems) {\n \u001b[90m 58 |\u001b[39m       \u001b[36mconst\u001b[39m navItem \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`nav a:has-text(\"${item}\"), a[href*=\"${item.toLowerCase()}\"]`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 59 |\u001b[39m       \u001b[36mif\u001b[39m (\u001b[36mawait\u001b[39m navItem\u001b[33m.\u001b[39misVisible()) {\n \u001b[90m    |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 60 |\u001b[39m         foundNavItems\u001b[33m++\u001b[39m\u001b[33m;\u001b[39m\n \u001b[90m 61 |\u001b[39m       }\n \u001b[90m 62 |\u001b[39m     }\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:59:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:11.905Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-admin-dashbo-00f9b-splay-admin-navigation-menu-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-admin-dashbo-00f9b-splay-admin-navigation-menu-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-admin-dashbo-00f9b-splay-admin-navigation-menu-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts", "column": 25, "line": 59}}], "status": "unexpected"}], "id": "553aa5e56666af0f27b5-4792a66ddeeea9a2a3c9", "file": "comprehensive-admin-dashboard.spec.ts", "line": 46, "column": 7}, {"title": "should navigate to and test Users management", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 13066, "errors": [], "stdout": [{"text": "Users table has 6 headers and 14 rows\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:11.908Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-6b7414a6e27d0d9ab046", "file": "comprehensive-admin-dashboard.spec.ts", "line": 68, "column": 7}, {"title": "should navigate to and test Roles management", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 13035, "errors": [], "stdout": [{"text": "Roles table has 4 headers and 4 rows\n"}, {"text": "Found 10 role-related elements\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:11.907Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-da56bb8214f3323a0543", "file": "comprehensive-admin-dashboard.spec.ts", "line": 95, "column": 7}, {"title": "should navigate to and test Permissions management", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 13031, "errors": [], "stdout": [{"text": "Permissions table has 6 headers and 2 rows\n"}, {"text": "Found 3 permission-related elements\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:11.913Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-88ec15a23f6ff3204adc", "file": "comprehensive-admin-dashboard.spec.ts", "line": 117, "column": 7}, {"title": "should navigate to and test Groups management", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 13009, "errors": [], "stdout": [{"text": "Groups table has 4 headers and 4 rows\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:11.907Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-55174642dd7fb926e06d", "file": "comprehensive-admin-dashboard.spec.ts", "line": 139, "column": 7}, {"title": "should navigate to and test Brands management", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "failed", "duration": 12973, "error": {"message": "Error: locator.count: Unexpected token \"=\" while parsing css selector \"[data-testid*=\"brand\"], .brand, text=/brand/i\". Did you mean to CSS.escape it?", "stack": "Error: locator.count: Unexpected token \"=\" while parsing css selector \"[data-testid*=\"brand\"], .brand, text=/brand/i\". Did you mean to CSS.escape it?\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:172:44", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts", "column": 44, "line": 172}, "snippet": "\u001b[0m \u001b[90m 170 |\u001b[39m     \u001b[90m// Look for brand-specific features\u001b[39m\n \u001b[90m 171 |\u001b[39m     \u001b[36mconst\u001b[39m brandElements \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid*=\"brand\"], .brand, text=/brand/i'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 172 |\u001b[39m     \u001b[36mconst\u001b[39m brandCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m brandElements\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 173 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m`Found ${brandCount} brand-related elements`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 174 |\u001b[39m\n \u001b[90m 175 |\u001b[39m     \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m'admin-brands-page'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts", "column": 44, "line": 172}, "message": "Error: locator.count: Unexpected token \"=\" while parsing css selector \"[data-testid*=\"brand\"], .brand, text=/brand/i\". Did you mean to CSS.escape it?\n\n\u001b[0m \u001b[90m 170 |\u001b[39m     \u001b[90m// Look for brand-specific features\u001b[39m\n \u001b[90m 171 |\u001b[39m     \u001b[36mconst\u001b[39m brandElements \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid*=\"brand\"], .brand, text=/brand/i'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 172 |\u001b[39m     \u001b[36mconst\u001b[39m brandCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m brandElements\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 173 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m`Found ${brandCount} brand-related elements`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 174 |\u001b[39m\n \u001b[90m 175 |\u001b[39m     \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m'admin-brands-page'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:172:44\u001b[22m"}], "stdout": [{"text": "Brands table has 8 headers and 23 rows\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:11.910Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-admin-dashbo-c9ae0--and-test-Brands-management-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-admin-dashbo-c9ae0--and-test-Brands-management-chromium/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts", "column": 44, "line": 172}}], "status": "unexpected"}], "id": "553aa5e56666af0f27b5-5741a3e4a32e5a8a9c58", "file": "comprehensive-admin-dashboard.spec.ts", "line": 156, "column": 7}, {"title": "should navigate to and test Settings page", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 10721, "errors": [], "stdout": [{"text": "Found 0 settings elements\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:11.909Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-6ceb279fd5af9e228700", "file": "comprehensive-admin-dashboard.spec.ts", "line": 178, "column": 7}, {"title": "should navigate to and test Backups page", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 6976, "errors": [], "stdout": [{"text": "Found 3 backup-related elements\n"}, {"text": "Found 0 backup buttons\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:22.818Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-35186b574f191d8a32d1", "file": "comprehensive-admin-dashboard.spec.ts", "line": 194, "column": 7}, {"title": "should navigate to and test DB Structure page", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 1, "status": "passed", "duration": 7114, "errors": [], "stdout": [{"text": "Found 0 database structure elements\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:25.370Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-1bff8957448028ec1efb", "file": "comprehensive-admin-dashboard.spec.ts", "line": 215, "column": 7}, {"title": "should test admin API endpoints", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 6496, "errors": [], "stdout": [{"text": "/api/admin/users: 200\n"}, {"text": "/api/admin/roles: 200\n"}, {"text": "/api/admin/permissions: 200\n"}, {"text": "/api/admin/groups: 200\n"}, {"text": "/api/admin/brands: 200\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:25.099Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-e6258fb3366cf621289e", "file": "comprehensive-admin-dashboard.spec.ts", "line": 231, "column": 7}, {"title": "should handle admin form submissions", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 6, "status": "passed", "duration": 7445, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:25.387Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-478df801d18c98c9299f", "file": "comprehensive-admin-dashboard.spec.ts", "line": 254, "column": 7}, {"title": "should be responsive across different screen sizes", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 7168, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role=\"main\"]') resolved to 2 elements:\n    1) <main data-slot=\"sidebar-inset\" class=\"bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\">…</main> aka getByText('Toggle SidebarDashboardAdminAdmin DashboardManage users, roles, and system')\n    2) <main class=\"flex-1 overflow-auto\">…</main> aka getByRole('main').filter({ hasText: 'Toggle' }).getByRole('main')\n\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator('main, [role=\"main\"]')\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role=\"main\"]') resolved to 2 elements:\n    1) <main data-slot=\"sidebar-inset\" class=\"bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\">…</main> aka getByText('Toggle SidebarDashboardAdminAdmin DashboardManage users, roles, and system')\n    2) <main class=\"flex-1 overflow-auto\">…</main> aka getByRole('main').filter({ hasText: 'Toggle' }).getByRole('main')\n\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator('main, [role=\"main\"]')\u001b[22m\n\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:300:57", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts", "column": 57, "line": 300}, "snippet": "\u001b[0m \u001b[90m 298 |\u001b[39m       \n \u001b[90m 299 |\u001b[39m       \u001b[90m// Verify main elements are still visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 300 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'main, [role=\"main\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 301 |\u001b[39m       \n \u001b[90m 302 |\u001b[39m       \u001b[90m// Check admin navigation on different screens\u001b[39m\n \u001b[90m 303 |\u001b[39m       \u001b[36mconst\u001b[39m adminNav \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'nav'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts", "column": 57, "line": 300}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role=\"main\"]') resolved to 2 elements:\n    1) <main data-slot=\"sidebar-inset\" class=\"bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\">…</main> aka getByText('Toggle SidebarDashboardAdminAdmin DashboardManage users, roles, and system')\n    2) <main class=\"flex-1 overflow-auto\">…</main> aka getByRole('main').filter({ hasText: 'Toggle' }).getByRole('main')\n\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator('main, [role=\"main\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 298 |\u001b[39m       \n \u001b[90m 299 |\u001b[39m       \u001b[90m// Verify main elements are still visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 300 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'main, [role=\"main\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 301 |\u001b[39m       \n \u001b[90m 302 |\u001b[39m       \u001b[90m// Check admin navigation on different screens\u001b[39m\n \u001b[90m 303 |\u001b[39m       \u001b[36mconst\u001b[39m adminNav \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'nav'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:300:57\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:25.137Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-admin-dashbo-7048f-ross-different-screen-sizes-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-admin-dashbo-7048f-ross-different-screen-sizes-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-admin-dashbo-7048f-ross-different-screen-sizes-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts", "column": 57, "line": 300}}], "status": "unexpected"}], "id": "553aa5e56666af0f27b5-ad4adc0ebd440367e42f", "file": "comprehensive-admin-dashboard.spec.ts", "line": 285, "column": 7}, {"title": "should have proper accessibility features", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "failed", "duration": 6143, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreater<PERSON>han\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:320:41", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts", "column": 41, "line": 320}, "snippet": "\u001b[0m \u001b[90m 318 |\u001b[39m     \u001b[90m// Verify minimum accessibility requirements for admin interface\u001b[39m\n \u001b[90m 319 |\u001b[39m     expect(accessibilityInfo\u001b[33m.\u001b[39mheadings)\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 320 |\u001b[39m     expect(accessibilityInfo\u001b[33m.\u001b[39mlandmarks)\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 321 |\u001b[39m     \n \u001b[90m 322 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'Admin Dashboard accessibility info:'\u001b[39m\u001b[33m,\u001b[39m accessibilityInfo)\u001b[33m;\u001b[39m\n \u001b[90m 323 |\u001b[39m     \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m'admin-dashboard-accessibility'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts", "column": 41, "line": 320}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n\n\u001b[0m \u001b[90m 318 |\u001b[39m     \u001b[90m// Verify minimum accessibility requirements for admin interface\u001b[39m\n \u001b[90m 319 |\u001b[39m     expect(accessibilityInfo\u001b[33m.\u001b[39mheadings)\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 320 |\u001b[39m     expect(accessibilityInfo\u001b[33m.\u001b[39mlandmarks)\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 321 |\u001b[39m     \n \u001b[90m 322 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'Admin Dashboard accessibility info:'\u001b[39m\u001b[33m,\u001b[39m accessibilityInfo)\u001b[33m;\u001b[39m\n \u001b[90m 323 |\u001b[39m     \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m'admin-dashboard-accessibility'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:320:41\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:25.139Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-admin-dashbo-14f8d-oper-accessibility-features-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-admin-dashbo-14f8d-oper-accessibility-features-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-admin-dashbo-14f8d-oper-accessibility-features-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts", "column": 41, "line": 320}}], "status": "unexpected"}], "id": "553aa5e56666af0f27b5-eb25bc91404e7bb88691", "file": "comprehensive-admin-dashboard.spec.ts", "line": 312, "column": 7}, {"title": "should load within performance thresholds", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 6127, "errors": [], "stdout": [{"text": "Admin Dashboard loaded in 1331ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:25.161Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-53d39c0592d076d33218", "file": "comprehensive-admin-dashboard.spec.ts", "line": 326, "column": 7}, {"title": "should not have critical console errors", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 6092, "errors": [], "stdout": [{"text": "Critical console errors found: [\n  \u001b[32m'Error fetching saved views: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\\n'\u001b[39m +\n    \u001b[32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\\n'\u001b[39m +\n    \u001b[32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'\u001b[39m,\n  \u001b[32m'Error fetching brands: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\\n'\u001b[39m +\n    \u001b[32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9578:33)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9581:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)'\u001b[39m,\n  \u001b[32m'Error fetching saved views: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\\n'\u001b[39m +\n    \u001b[32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\\n'\u001b[39m +\n    \u001b[32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9578:33)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)'\u001b[39m,\n  \u001b[32m'Error fetching brands: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\\n'\u001b[39m +\n    \u001b[32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'\u001b[39m\n]\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:29.801Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-20f33e1c3e3f352722f1", "file": "comprehensive-admin-dashboard.spec.ts", "line": 341, "column": 7}]}]}, {"title": "comprehensive-api.spec.ts", "file": "comprehensive-api.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive API Testing", "file": "comprehensive-api.spec.ts", "line": 7, "column": 6, "specs": [{"title": "should test dashboard API endpoints", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 5729, "errors": [], "stdout": [{"text": "/api/dashboard/flexible-kpis: 200\n"}, {"text": "/api/dashboard/flexible-kpis returned data: [\n  \u001b[32m'Gross Revenue'\u001b[39m,         \u001b[32m'Net Revenue'\u001b[39m,\n  \u001b[32m'Gross Margin'\u001b[39m,          \u001b[32m'Adspend'\u001b[39m,\n  \u001b[32m'Contribution Margin'\u001b[39m,   \u001b[32m'% Gross Margin'\u001b[39m,\n  \u001b[32m'% Contribution Margin'\u001b[39m, \u001b[32m'% Adspend'\u001b[39m,\n  \u001b[32m'Landed Cost'\u001b[39m,           \u001b[32m'Fulfillment Cost'\u001b[39m,\n  \u001b[32m'Transaction Cost'\u001b[39m,      \u001b[32m'Discount'\u001b[39m,\n  \u001b[32m'Refund'\u001b[39m,                \u001b[32m'% Landed Cost'\u001b[39m,\n  \u001b[32m'% Fulfillment Cost'\u001b[39m,    \u001b[32m'% Transaction Cost'\u001b[39m,\n  \u001b[32m'% Discount'\u001b[39m,            \u001b[32m'% Refund'\u001b[39m,\n  \u001b[32m'ACOS'\u001b[39m,                  \u001b[32m'TACOS'\u001b[39m,\n  \u001b[32m'TCAC'\u001b[39m,                  \u001b[32m'Website Traffic'\u001b[39m,\n  \u001b[32m'Conversion Rate'\u001b[39m,       \u001b[32m'Organic Traffic'\u001b[39m,\n  \u001b[32m'Paid Traffic'\u001b[39m\n]\n"}, {"text": "/api/dashboard/kpi-data: 404\n"}, {"text": "/api/dashboard/chart-data: 404\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:31.297Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-d46edda83bf5bf3f7920", "file": "comprehensive-api.spec.ts", "line": 13, "column": 7}, {"title": "should test marketing API endpoints", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 4, "status": "passed", "duration": 5533, "errors": [], "stdout": [{"text": "/api/marketing/campaigns: 404\n"}, {"text": "/api/marketing/kpis: 404\n"}, {"text": "/api/marketing/spend-breakdown: 404\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:31.619Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-e6c629d90e606cf44106", "file": "comprehensive-api.spec.ts", "line": 35, "column": 7}, {"title": "should test admin API endpoints with proper authorization", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 7105, "errors": [], "stdout": [{"text": "/api/admin/users: 200\n"}, {"text": "/api/admin/users returned data: [\n  \u001b[32m'0'\u001b[39m,  \u001b[32m'1'\u001b[39m,  \u001b[32m'2'\u001b[39m,  \u001b[32m'3'\u001b[39m,\n  \u001b[32m'4'\u001b[39m,  \u001b[32m'5'\u001b[39m,  \u001b[32m'6'\u001b[39m,  \u001b[32m'7'\u001b[39m,\n  \u001b[32m'8'\u001b[39m,  \u001b[32m'9'\u001b[39m,  \u001b[32m'10'\u001b[39m, \u001b[32m'11'\u001b[39m,\n  \u001b[32m'12'\u001b[39m, \u001b[32m'13'\u001b[39m\n]\n"}, {"text": "/api/admin/roles: 200\n"}, {"text": "/api/admin/roles returned data: [ \u001b[32m'data'\u001b[39m, \u001b[32m'pagination'\u001b[39m ]\n"}, {"text": "/api/admin/permissions: 200\n"}, {"text": "/api/admin/permissions returned data: [ \u001b[32m'0'\u001b[39m, \u001b[32m'1'\u001b[39m ]\n"}, {"text": "/api/admin/groups: 200\n"}, {"text": "/api/admin/groups returned data: [ \u001b[32m'data'\u001b[39m, \u001b[32m'pagination'\u001b[39m ]\n"}, {"text": "/api/admin/brands: 200\n"}, {"text": "/api/admin/brands returned data: [ \u001b[32m'data'\u001b[39m, \u001b[32m'pagination'\u001b[39m ]\n"}, {"text": "/api/admin/settings: 401\n"}, {"text": "/api/admin/settings: Unauthorized (expected for non-admin users)\n"}, {"text": "/api/admin/backups: 401\n"}, {"text": "/api/admin/backups: Unauthorized (expected for non-admin users)\n"}, {"text": "/api/admin/db-structure: 401\n"}, {"text": "/api/admin/db-structure: Unauthorized (expected for non-admin users)\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:31.602Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-3b1cf828069c693bdc8e", "file": "comprehensive-api.spec.ts", "line": 57, "column": 7}, {"title": "should test budget API endpoint", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 11, "parallelIndex": 3, "status": "passed", "duration": 5667, "errors": [], "stdout": [{"text": "Budget API: 200\n"}, {"text": "Budget API returned data: [ \u001b[32m'budgets'\u001b[39m ]\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:32.588Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-c9f347ceb02be0529cf1", "file": "comprehensive-api.spec.ts", "line": 87, "column": 7}, {"title": "should test AI assistant API endpoint", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 1, "status": "passed", "duration": 19184, "errors": [], "stdout": [{"text": "AI Assistant GET: 405\n"}, {"text": "AI Assistant POST: 200\n"}, {"text": "AI Assistant response: {\n  message: \u001b[32m'# Total Revenue Analysis\\n'\u001b[39m +\n    \u001b[32m'\\n'\u001b[39m +\n    \u001b[32m'## Summary\\n'\u001b[39m +\n    \u001b[32m'The total gross revenue across all brands is **C$14,347,789**\\n'\u001b[39m +\n    \u001b[32m'\\n'\u001b[39m +\n    \u001b[32m'Would you like me to break down this revenue by specific time periods, brands, or channels?'\u001b[39m,\n  charts: [\n    {\n      type: \u001b[32m'line'\u001b[39m,\n      title: \u001b[32m'KPI Comparison'\u001b[39m,\n      data: \u001b[36m[Array]\u001b[39m,\n      xAxis: \u001b[32m'date'\u001b[39m,\n      yAxis: \u001b[36m[Array]\u001b[39m\n    }\n  ],\n  contextInfo: \u001b[32m'Error generating context information.'\u001b[39m\n}\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:32.568Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-960ba329222c9fe6ef5f", "file": "comprehensive-api.spec.ts", "line": 101, "column": 7}, {"title": "should test API error handling", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 6, "status": "passed", "duration": 5787, "errors": [], "stdout": [{"text": "/api/nonexistent: 404\n"}, {"text": "/api/dashboard/invalid: 404\n"}, {"text": "/api/admin/invalid: 404\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:32.902Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-69fae84ccd164bb43df7", "file": "comprehensive-api.spec.ts", "line": 125, "column": 7}, {"title": "should test API with query parameters", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 5156, "errors": [], "stdout": [{"text": "/api/dashboard/flexible-kpis?startDate=2024-01-01&endDate=2024-12-31: 200\n"}, {"text": "/api/dashboard/flexible-kpis?startDate=2024-01-01&endDate=2024-12-31 with params returned: [\n  \u001b[32m'Gross Revenue'\u001b[39m,         \u001b[32m'Net Revenue'\u001b[39m,\n  \u001b[32m'Gross Margin'\u001b[39m,          \u001b[32m'Adspend'\u001b[39m,\n  \u001b[32m'Contribution Margin'\u001b[39m,   \u001b[32m'% Gross Margin'\u001b[39m,\n  \u001b[32m'% Contribution Margin'\u001b[39m, \u001b[32m'% Adspend'\u001b[39m,\n  \u001b[32m'Landed Cost'\u001b[39m,           \u001b[32m'Fulfillment Cost'\u001b[39m,\n  \u001b[32m'Transaction Cost'\u001b[39m,      \u001b[32m'Discount'\u001b[39m,\n  \u001b[32m'Refund'\u001b[39m,                \u001b[32m'% Landed Cost'\u001b[39m,\n  \u001b[32m'% Fulfillment Cost'\u001b[39m,    \u001b[32m'% Transaction Cost'\u001b[39m,\n  \u001b[32m'% Discount'\u001b[39m,            \u001b[32m'% Refund'\u001b[39m,\n  \u001b[32m'ACOS'\u001b[39m,                  \u001b[32m'TACOS'\u001b[39m,\n  \u001b[32m'TCAC'\u001b[39m,                  \u001b[32m'Website Traffic'\u001b[39m,\n  \u001b[32m'Conversion Rate'\u001b[39m,       \u001b[32m'Organic Traffic'\u001b[39m,\n  \u001b[32m'Paid Traffic'\u001b[39m\n]\n"}, {"text": "/api/marketing/campaigns?brand=test&currency=USD: 404\n"}, {"text": "/api/budget?brand=test&brands=true: 200\n"}, {"text": "/api/budget?brand=test&brands=true with params returned: [ \u001b[32m'brands'\u001b[39m ]\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:35.901Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-b941ed9034005094099f", "file": "comprehensive-api.spec.ts", "line": 146, "column": 7}, {"title": "should test API response times", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 5094, "errors": [], "stdout": [{"text": "/api/dashboard/flexible-kpis: 200 in 254ms\n"}, {"text": "/api/marketing/campaigns: 404 in 56ms\n"}, {"text": "/api/budget: 200 in 67ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:37.029Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-6f18c7464c495e0725a1", "file": "comprehensive-api.spec.ts", "line": 168, "column": 7}, {"title": "should test API data validation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 4, "status": "passed", "duration": 5231, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:37.231Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-71bf185d8fda80937344", "file": "comprehensive-api.spec.ts", "line": 192, "column": 7}, {"title": "should test API authentication requirements", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 11, "parallelIndex": 3, "status": "passed", "duration": 5281, "errors": [], "stdout": [{"text": "/api/admin/users (unauthenticated): 200\n"}, {"text": "/api/admin/users (unauthenticated): Failed as expected - Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m200\u001b[39m\nReceived array: \u001b[31m[401, 302, 403]\u001b[39m\n"}, {"text": "/api/admin/roles (unauthenticated): 200\n"}, {"text": "/api/admin/roles (unauthenticated): Failed as expected - Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m200\u001b[39m\nReceived array: \u001b[31m[401, 302, 403]\u001b[39m\n"}, {"text": "/api/dashboard/flexible-kpis (unauthenticated): 200\n"}, {"text": "/api/dashboard/flexible-kpis (unauthenticated): Failed as expected - Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m200\u001b[39m\nReceived array: \u001b[31m[401, 302, 403]\u001b[39m\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:38.334Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-d5e579e72fbc4a710dae", "file": "comprehensive-api.spec.ts", "line": 218, "column": 7}, {"title": "should test API CORS headers", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 6, "status": "passed", "duration": 5048, "errors": [], "stdout": [{"text": "API Response Headers:\n"}, {"text": "Content-Type: application/json\n"}, {"text": "Cache-Control: no-store, max-age=0\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:38.693Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-e5e6632f7baa1e16dff4", "file": "comprehensive-api.spec.ts", "line": 243, "column": 7}, {"title": "should test API rate limiting (if implemented)", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 6010, "errors": [], "stdout": [{"text": "Request 1: 200\n"}, {"text": "Request 2: 200\n"}, {"text": "Request 3: 200\n"}, {"text": "Request 4: 200\n"}, {"text": "Request 5: 200\n"}, {"text": "Request 6: 200\n"}, {"text": "Request 7: 200\n"}, {"text": "Request 8: 200\n"}, {"text": "Request 9: 200\n"}, {"text": "Request 10: 200\n"}, {"text": "Successful requests: 10, Rate limited: 0\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:38.711Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-79e5ae70e269e15bab49", "file": "comprehensive-api.spec.ts", "line": 262, "column": 7}, {"title": "should test API monitoring endpoints", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "passed", "duration": 5125, "errors": [], "stdout": [{"text": "/api/monitoring/health: 200\n"}, {"text": "/api/monitoring/health health check: {\n  overall: \u001b[32m'healthy'\u001b[39m,\n  timestamp: \u001b[33m1748572065392\u001b[39m,\n  uptime: \u001b[33m36.888842958\u001b[39m,\n  version: \u001b[32m'0.1.0'\u001b[39m,\n  environment: \u001b[32m'development'\u001b[39m,\n  checks: [\n    {\n      service: \u001b[32m'SQLite Database'\u001b[39m,\n      status: \u001b[32m'healthy'\u001b[39m,\n      responseTime: \u001b[33m0\u001b[39m,\n      details: \u001b[32m'Database responding normally'\u001b[39m,\n      lastChecked: \u001b[33m1748572065371\u001b[39m\n    },\n    {\n      service: \u001b[32m'Redshift Database'\u001b[39m,\n      status: \u001b[32m'healthy'\u001b[39m,\n      responseTime: \u001b[33m21\u001b[39m,\n      details: \u001b[32m'Redshift responding normally'\u001b[39m,\n      lastChecked: \u001b[33m1748572065392\u001b[39m\n    },\n    {\n      service: \u001b[32m'NextAuth'\u001b[39m,\n      status: \u001b[32m'healthy'\u001b[39m,\n      details: \u001b[32m'NextAuth configured'\u001b[39m,\n      lastChecked: \u001b[33m1748572065392\u001b[39m\n    },\n    {\n      service: \u001b[32m'Google OAuth'\u001b[39m,\n      status: \u001b[32m'healthy'\u001b[39m,\n      details: \u001b[32m'Google OAuth configured'\u001b[39m,\n      lastChecked: \u001b[33m1748572065392\u001b[39m\n    }\n  ],\n  performance: {\n    memory: {\n      rss: \u001b[33m2979414016\u001b[39m,\n      heapTotal: \u001b[33m442580992\u001b[39m,\n      heapUsed: \u001b[33m400550176\u001b[39m,\n      external: \u001b[33m65451075\u001b[39m,\n      arrayBuffers: \u001b[33m62799962\u001b[39m\n    },\n    cpu: { user: \u001b[33m68653265\u001b[39m, system: \u001b[33m21761291\u001b[39m },\n    queries: {\n      totalQueries: \u001b[33m0\u001b[39m,\n      slowQueries: \u001b[33m0\u001b[39m,\n      avgDuration: \u001b[33m0\u001b[39m,\n      cacheHitRate: \u001b[33m0\u001b[39m,\n      topSlowQueries: []\n    },\n    apis: {\n      totalRequests: \u001b[33m0\u001b[39m,\n      errorRequests: \u001b[33m0\u001b[39m,\n      slowRequests: \u001b[33m0\u001b[39m,\n      avgDuration: \u001b[33m0\u001b[39m,\n      errorRate: \u001b[33m0\u001b[39m,\n      topSlowEndpoints: []\n    },\n    errors: { totalErrors: \u001b[33m0\u001b[39m, errorsByType: {}, recentErrors: [] }\n  },\n  database: {\n    sqlite: {\n      status: \u001b[32m'healthy'\u001b[39m,\n      responseTime: \u001b[33m0\u001b[39m,\n      details: \u001b[32m'Database responding normally'\u001b[39m\n    },\n    redshift: {\n      status: \u001b[32m'healthy'\u001b[39m,\n      responseTime: \u001b[33m21\u001b[39m,\n      poolStats: \u001b[36m[Object]\u001b[39m,\n      details: \u001b[32m'Redshift responding normally'\u001b[39m\n    }\n  }\n}\n"}, {"text": "/api/monitoring/status: 404\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:40.317Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-46251ebc505ea0d64369", "file": "comprehensive-api.spec.ts", "line": 293, "column": 7}]}]}, {"title": "comprehensive-brand-deep-dive.spec.ts", "file": "comprehensive-brand-deep-dive.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Brand Deep Dive Testing", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 14, "column": 6, "specs": [{"title": "should load brand deep dive page with all components", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 6914, "errors": [], "stdout": [{"text": "Page title: Create Next App\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:41.062Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-60ee1fcb54ee3ecf5abe", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 20, "column": 7}, {"title": "should display brand selector and allow brand selection", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 6063, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:42.126Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-452374d3464cf88bf199", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 39, "column": 7}, {"title": "should display KPI cards specific to selected brand", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 4, "status": "passed", "duration": 5955, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:42.467Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-9dba5927d678596274c4", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 66, "column": 7}, {"title": "should render brand performance charts", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 11, "parallelIndex": 3, "status": "passed", "duration": 8660, "errors": [], "stdout": [{"text": "Found 1 charts with selector: svg[class*=\"chart\"]\n"}, {"text": "Found 23 charts on brand deep dive page\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:43.632Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-d399a48d82b2d976c1e7", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 89, "column": 7}, {"title": "should display marketing campaigns for selected brand", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 6, "status": "failed", "duration": 6636, "error": {"message": "Error: locator.isVisible: Unexpected token \"=\" while parsing css selector \"[data-testid*=\"campaign\"], .campaign, text=/campaign/i\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m    - checking visibility of [data-testid*=\"campaign\"], .campaign, text=/campaign/i >> nth=0\u001b[22m\n", "stack": "Error: locator.isVisible: Unexpected token \"=\" while parsing css selector \"[data-testid*=\"campaign\"], .campaign, text=/campaign/i\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m    - checking visibility of [data-testid*=\"campaign\"], .campaign, text=/campaign/i >> nth=0\u001b[22m\n\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:123:40", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts", "column": 40, "line": 123}, "snippet": "\u001b[0m \u001b[90m 121 |\u001b[39m     \u001b[36mconst\u001b[39m campaignsSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid*=\"campaign\"], .campaign, text=/campaign/i'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 122 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 123 |\u001b[39m     \u001b[36mif\u001b[39m (\u001b[36mawait\u001b[39m campaignsSection\u001b[33m.\u001b[39mfirst()\u001b[33m.\u001b[39misVisible()) {\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 124 |\u001b[39m       \u001b[90m// Check for campaign data\u001b[39m\n \u001b[90m 125 |\u001b[39m       \u001b[36mconst\u001b[39m campaignItems \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid*=\"campaign-item\"], .campaign-item, tr'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 126 |\u001b[39m       \u001b[36mconst\u001b[39m campaignCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m campaignItems\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts", "column": 40, "line": 123}, "message": "Error: locator.isVisible: Unexpected token \"=\" while parsing css selector \"[data-testid*=\"campaign\"], .campaign, text=/campaign/i\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m    - checking visibility of [data-testid*=\"campaign\"], .campaign, text=/campaign/i >> nth=0\u001b[22m\n\n\n\u001b[0m \u001b[90m 121 |\u001b[39m     \u001b[36mconst\u001b[39m campaignsSection \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid*=\"campaign\"], .campaign, text=/campaign/i'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 122 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 123 |\u001b[39m     \u001b[36mif\u001b[39m (\u001b[36mawait\u001b[39m campaignsSection\u001b[33m.\u001b[39mfirst()\u001b[33m.\u001b[39misVisible()) {\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 124 |\u001b[39m       \u001b[90m// Check for campaign data\u001b[39m\n \u001b[90m 125 |\u001b[39m       \u001b[36mconst\u001b[39m campaignItems \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid*=\"campaign-item\"], .campaign-item, tr'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 126 |\u001b[39m       \u001b[36mconst\u001b[39m campaignCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m campaignItems\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:123:40\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:43.745Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-brand-deep-d-d54f0-ampaigns-for-selected-brand-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-brand-deep-d-d54f0-ampaigns-for-selected-brand-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-brand-deep-d-d54f0-ampaigns-for-selected-brand-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts", "column": 40, "line": 123}}], "status": "unexpected"}], "id": "2e173649ad4b74a0ea79-f42e06c014abcba64d06", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 115, "column": 7}, {"title": "should have functional brand-specific filters", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 8042, "errors": [], "stdout": [{"text": "Found 1 active filters\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:44.728Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-8166cd95e0738d86bf81", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 140, "column": 7}, {"title": "should display tabs and allow tab navigation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "passed", "duration": 5669, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:45.521Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-298a596789ceb9b7c03a", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 181, "column": 7}, {"title": "should handle brand comparison if available", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 5808, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:47.981Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-93fbfee592aa608119b0", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 208, "column": 7}, {"title": "should handle export functionality if available", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 5909, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:48.194Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-a55dc13a303e9a88f4dc", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 228, "column": 7}, {"title": "should be responsive across different screen sizes", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 4, "status": "failed", "duration": 6906, "error": {"message": "ReferenceError: getMainContent is not defined", "stack": "ReferenceError: getMainContent is not defined\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:261:27", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts", "column": 27, "line": 261}, "snippet": "\u001b[0m \u001b[90m 259 |\u001b[39m\n \u001b[90m 260 |\u001b[39m       \u001b[90m// Verify main elements are still visible and properly arranged\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 261 |\u001b[39m       \u001b[36mconst\u001b[39m mainContent \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m getMainContent(page)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 262 |\u001b[39m       \u001b[36mawait\u001b[39m expect(mainContent)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 263 |\u001b[39m\n \u001b[90m 264 |\u001b[39m       \u001b[90m// Check if mobile menu appears on small screens\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts", "column": 27, "line": 261}, "message": "ReferenceError: getMainContent is not defined\n\n\u001b[0m \u001b[90m 259 |\u001b[39m\n \u001b[90m 260 |\u001b[39m       \u001b[90m// Verify main elements are still visible and properly arranged\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 261 |\u001b[39m       \u001b[36mconst\u001b[39m mainContent \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m getMainContent(page)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 262 |\u001b[39m       \u001b[36mawait\u001b[39m expect(mainContent)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 263 |\u001b[39m\n \u001b[90m 264 |\u001b[39m       \u001b[90m// Check if mobile menu appears on small screens\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:261:27\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:48.427Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-brand-deep-d-ce6db-ross-different-screen-sizes-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-brand-deep-d-ce6db-ross-different-screen-sizes-chromium/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts", "column": 27, "line": 261}}], "status": "unexpected"}], "id": "2e173649ad4b74a0ea79-a3770fab3a14a421942b", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 246, "column": 7}, {"title": "should have proper accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 13, "parallelIndex": 6, "status": "passed", "duration": 5776, "errors": [], "stdout": [{"text": "Brand Deep Dive landmarks: 0\n"}, {"text": "Brand Deep Dive accessibility info: { ariaLabels: \u001b[33m1\u001b[39m, roles: \u001b[33m6\u001b[39m, headings: \u001b[33m3\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:50.670Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-e6fb6a8910206d7fe498", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 278, "column": 7}, {"title": "should load within performance thresholds", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "passed", "duration": 5761, "errors": [], "stdout": [{"text": "Brand Deep Dive loaded in 1049ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:51.194Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-4102223de0ec5edbd3dd", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 293, "column": 7}]}]}, {"title": "comprehensive-budget-ai.spec.ts", "file": "comprehensive-budget-ai.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Budget Page Testing", "file": "comprehensive-budget-ai.spec.ts", "line": 16, "column": 6, "specs": [{"title": "should load budget page with all components", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 1, "status": "passed", "duration": 6583, "errors": [], "stdout": [{"text": "Page title: Create Next App\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:51.758Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-acea5ba122acf431d3cd", "file": "comprehensive-budget-ai.spec.ts", "line": 22, "column": 7}, {"title": "should display budget data and charts", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 11, "parallelIndex": 3, "status": "passed", "duration": 8036, "errors": [], "stdout": [{"text": "Budget table has 17 headers and 12 rows\n"}, {"text": "Found 1 charts with selector: svg[class*=\"chart\"]\n"}, {"text": "Found 5 budget-related elements\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:52.299Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-dadb25d4613a7e1ed40b", "file": "comprehensive-budget-ai.spec.ts", "line": 41, "column": 7}, {"title": "should handle budget filters and brand selection", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 5752, "errors": [], "stdout": [{"text": "Found 0 active filters\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:52.777Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-6c267e5c278bce4cbdac", "file": "comprehensive-budget-ai.spec.ts", "line": 63, "column": 7}, {"title": "should test budget API endpoint", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 5339, "errors": [], "stdout": [{"text": "Budget API response: 200\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:53.795Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-d605c769b96857ddf2c1", "file": "comprehensive-budget-ai.spec.ts", "line": 90, "column": 7}, {"title": "should be responsive on different screen sizes", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 6485, "error": {"message": "ReferenceError: getMainContent is not defined", "stack": "ReferenceError: getMainContent is not defined\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:117:27", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts", "column": 27, "line": 117}, "snippet": "\u001b[0m \u001b[90m 115 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 116 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 117 |\u001b[39m       \u001b[36mconst\u001b[39m mainContent \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m getMainContent(page)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 118 |\u001b[39m       \u001b[36mawait\u001b[39m expect(mainContent)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 119 |\u001b[39m       \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m`budget-responsive-${viewport.name}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 120 |\u001b[39m     }\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts", "column": 27, "line": 117}, "message": "ReferenceError: getMainContent is not defined\n\n\u001b[0m \u001b[90m 115 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 116 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 117 |\u001b[39m       \u001b[36mconst\u001b[39m mainContent \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m getMainContent(page)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 118 |\u001b[39m       \u001b[36mawait\u001b[39m expect(mainContent)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 119 |\u001b[39m       \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m`budget-responsive-${viewport.name}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 120 |\u001b[39m     }\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:117:27\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:54.108Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-84dd1-e-on-different-screen-sizes-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-84dd1-e-on-different-screen-sizes-chromium/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts", "column": 27, "line": 117}}], "status": "unexpected"}], "id": "737a8975eec8bf10c274-470ff1f0d1cd3ac31e75", "file": "comprehensive-budget-ai.spec.ts", "line": 103, "column": 7}]}, {"title": "Comprehensive AI Assistant Testing", "file": "comprehensive-budget-ai.spec.ts", "line": 124, "column": 6, "specs": [{"title": "should load AI assistant page with all components", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 14, "parallelIndex": 4, "status": "passed", "duration": 7500, "errors": [], "stdout": [{"text": "Page title: Create Next App\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:55.681Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-f33e48accaf786fadff2", "file": "comprehensive-budget-ai.spec.ts", "line": 130, "column": 7}, {"title": "should display chat interface", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 13, "parallelIndex": 6, "status": "passed", "duration": 6782, "errors": [], "stdout": [{"text": "Found 1 chat interface elements\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:56.521Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-b03aadffa76cbe29a987", "file": "comprehensive-budget-ai.spec.ts", "line": 149, "column": 7}, {"title": "should handle message input and sending", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "passed", "duration": 6316, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:56.963Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-be06faa88ea896e117c7", "file": "comprehensive-budget-ai.spec.ts", "line": 174, "column": 7}, {"title": "should display conversation history", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 1, "status": "passed", "duration": 5701, "errors": [], "stdout": [{"text": "Found 0 messages in conversation history\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:58.346Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-0ab4b4350709df6d0e8f", "file": "comprehensive-budget-ai.spec.ts", "line": 206, "column": 7}, {"title": "should handle AI assistant features", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 5645, "errors": [], "stdout": [{"text": "Found 0 AI assistant features\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:58.534Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-48fe36971c94b67f4e5a", "file": "comprehensive-budget-ai.spec.ts", "line": 231, "column": 7}, {"title": "should test AI assistant API endpoint", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 5590, "errors": [], "stdout": [{"text": "AI Assistant API failed: Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m200\u001b[39m\nReceived: \u001b[31m405\u001b[39m\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:27:59.137Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-44d1f0ddb113fe3c9d6a", "file": "comprehensive-budget-ai.spec.ts", "line": 263, "column": 7}, {"title": "should handle suggested questions or templates", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 11, "parallelIndex": 3, "status": "passed", "duration": 5387, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:00.339Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-a309bcd3bc9bbe857353", "file": "comprehensive-budget-ai.spec.ts", "line": 276, "column": 7}, {"title": "should be responsive on different screen sizes", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 15, "parallelIndex": 2, "status": "failed", "duration": 6571, "error": {"message": "ReferenceError: getMainContent is not defined", "stack": "ReferenceError: getMainContent is not defined\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:319:27", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts", "column": 27, "line": 319}, "snippet": "\u001b[0m \u001b[90m 317 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 318 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 319 |\u001b[39m       \u001b[36mconst\u001b[39m mainContent \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m getMainContent(page)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 320 |\u001b[39m       \u001b[36mawait\u001b[39m expect(mainContent)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 321 |\u001b[39m\n \u001b[90m 322 |\u001b[39m       \u001b[90m// Check chat interface on different screens\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts", "column": 27, "line": 319}, "message": "ReferenceError: getMainContent is not defined\n\n\u001b[0m \u001b[90m 317 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 318 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 319 |\u001b[39m       \u001b[36mconst\u001b[39m mainContent \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m getMainContent(page)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 320 |\u001b[39m       \u001b[36mawait\u001b[39m expect(mainContent)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 321 |\u001b[39m\n \u001b[90m 322 |\u001b[39m       \u001b[90m// Check chat interface on different screens\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:319:27\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:00.980Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-1b3a3-e-on-different-screen-sizes-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-budget-ai-Co-1b3a3-e-on-different-screen-sizes-chromium/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts", "column": 27, "line": 319}}], "status": "unexpected"}], "id": "737a8975eec8bf10c274-6740ecd54b234653501d", "file": "comprehensive-budget-ai.spec.ts", "line": 305, "column": 7}, {"title": "should have proper accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 14, "parallelIndex": 4, "status": "passed", "duration": 6306, "errors": [], "stdout": [{"text": "AI Assistant landmarks: 0\n"}, {"text": "AI Assistant accessibility info: { ariaL<PERSON>ls: \u001b[33m1\u001b[39m, roles: \u001b[33m2\u001b[39m, headings: \u001b[33m2\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:03.276Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-9077241b2920b9ab9f29", "file": "comprehensive-budget-ai.spec.ts", "line": 332, "column": 7}, {"title": "should load within performance thresholds", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "passed", "duration": 6191, "errors": [], "stdout": [{"text": "AI Assistant loaded in 1327ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:03.285Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-664eca952a27eddf890c", "file": "comprehensive-budget-ai.spec.ts", "line": 347, "column": 7}]}]}, {"title": "comprehensive-dashboard.spec.ts", "file": "comprehensive-dashboard.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Dashboard Testing", "file": "comprehensive-dashboard.spec.ts", "line": 16, "column": 6, "specs": [{"title": "should load dashboard with all core components", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 13, "parallelIndex": 6, "status": "passed", "duration": 6672, "errors": [], "stdout": [{"text": "Page title: Create Next App\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:03.309Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-4f9a72da30ecc9aacaf4", "file": "comprehensive-dashboard.spec.ts", "line": 22, "column": 7}, {"title": "should display and interact with KPI cards", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 1, "status": "passed", "duration": 7622, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:04.054Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-f917a0bb813545912932", "file": "comprehensive-dashboard.spec.ts", "line": 43, "column": 7}, {"title": "should render charts correctly", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 9506, "errors": [], "stdout": [{"text": "Found 11 charts with selector: .recharts-wrapper\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:04.185Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-630828bc8bbb22b424af", "file": "comprehensive-dashboard.spec.ts", "line": 69, "column": 7}, {"title": "should have functional filters", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "failed", "duration": 6561, "error": {"message": "Error: locator.isVisible: Error: strict mode violation: locator('input[type=\"date\"]') resolved to 2 elements:\n    1) <input type=\"date\" id=\"start-date\" data-slot=\"input\" value=\"2025-03-01\" class=\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 …/> aka getByRole('textbox', { name: 'Start Date' })\n    2) <input type=\"date\" id=\"end-date\" data-slot=\"input\" value=\"2025-05-30\" class=\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md…/> aka getByRole('textbox', { name: 'End Date' })\n\nCall log:\n\u001b[2m    - checking visibility of locator('input[type=\"date\"]')\u001b[22m\n", "stack": "Error: locator.isVisible: Error: strict mode violation: locator('input[type=\"date\"]') resolved to 2 elements:\n    1) <input type=\"date\" id=\"start-date\" data-slot=\"input\" value=\"2025-03-01\" class=\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 …/> aka getByRole('textbox', { name: 'Start Date' })\n    2) <input type=\"date\" id=\"end-date\" data-slot=\"input\" value=\"2025-05-30\" class=\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md…/> aka getByRole('textbox', { name: 'End Date' })\n\nCall log:\n\u001b[2m    - checking visibility of locator('input[type=\"date\"]')\u001b[22m\n\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts:90:26", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts", "column": 26, "line": 90}, "snippet": "\u001b[0m \u001b[90m 88 |\u001b[39m     \u001b[90m// Test date filter if present\u001b[39m\n \u001b[90m 89 |\u001b[39m     \u001b[36mconst\u001b[39m dateFilter \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"date\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 90 |\u001b[39m     \u001b[36mif\u001b[39m (\u001b[36mawait\u001b[39m dateFilter\u001b[33m.\u001b[39misVisible()) {\n \u001b[90m    |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 91 |\u001b[39m       \u001b[36mawait\u001b[39m dateFilter\u001b[33m.\u001b[39mfill(\u001b[32m'2024-01-01'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 92 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 93 |\u001b[39m       \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m'dashboard-date-filter-applied'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts", "column": 26, "line": 90}, "message": "Error: locator.isVisible: Error: strict mode violation: locator('input[type=\"date\"]') resolved to 2 elements:\n    1) <input type=\"date\" id=\"start-date\" data-slot=\"input\" value=\"2025-03-01\" class=\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 …/> aka getByRole('textbox', { name: 'Start Date' })\n    2) <input type=\"date\" id=\"end-date\" data-slot=\"input\" value=\"2025-05-30\" class=\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md…/> aka getByRole('textbox', { name: 'End Date' })\n\nCall log:\n\u001b[2m    - checking visibility of locator('input[type=\"date\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 88 |\u001b[39m     \u001b[90m// Test date filter if present\u001b[39m\n \u001b[90m 89 |\u001b[39m     \u001b[36mconst\u001b[39m dateFilter \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"date\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 90 |\u001b[39m     \u001b[36mif\u001b[39m (\u001b[36mawait\u001b[39m dateFilter\u001b[33m.\u001b[39misVisible()) {\n \u001b[90m    |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 91 |\u001b[39m       \u001b[36mawait\u001b[39m dateFilter\u001b[33m.\u001b[39mfill(\u001b[32m'2024-01-01'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 92 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 93 |\u001b[39m       \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m'dashboard-date-filter-applied'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts:90:26\u001b[22m"}], "stdout": [{"text": "Found 1 active filters: [ \u001b[32m'input[type=\"date\"] (1/2)'\u001b[39m ]\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:04.734Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-dashboard-Co-4f9f8-uld-have-functional-filters-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-dashboard-Co-4f9f8-uld-have-functional-filters-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-dashboard-Co-4f9f8-uld-have-functional-filters-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts", "column": 26, "line": 90}}], "status": "unexpected"}], "id": "16e8e095986a497091c8-ac4ef4a0b79a2ac2ecfa", "file": "comprehensive-dashboard.spec.ts", "line": 79, "column": 7}, {"title": "should handle responsive design", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 11, "parallelIndex": 3, "status": "passed", "duration": 9574, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:05.732Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-fb423ac24a6e8dfe0e37", "file": "comprehensive-dashboard.spec.ts", "line": 113, "column": 7}, {"title": "should have proper accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 16, "parallelIndex": 2, "status": "passed", "duration": 6641, "errors": [], "stdout": [{"text": "Dashboard landmarks: 0\n"}, {"text": "Accessibility info: { aria<PERSON><PERSON>ls: \u001b[33m21\u001b[39m, roles: \u001b[33m26\u001b[39m, headings: \u001b[33m3\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:07.956Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-ca9cc54405668b80412b", "file": "comprehensive-dashboard.spec.ts", "line": 134, "column": 7}, {"title": "should handle navigation between dashboard sections", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "passed", "duration": 23152, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:09.485Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-e3724662986d23136e41", "file": "comprehensive-dashboard.spec.ts", "line": 149, "column": 7}, {"title": "should handle data refresh and updates", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 14, "parallelIndex": 4, "status": "passed", "duration": 8735, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:09.589Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-9d6d44a615b7160e3f91", "file": "comprehensive-dashboard.spec.ts", "line": 184, "column": 7}, {"title": "should not have critical console errors", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 13, "parallelIndex": 6, "status": "passed", "duration": 6723, "errors": [], "stdout": [{"text": "Critical console errors found: [\n  \u001b[32m'Error fetching sales channels: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3801:56)'\u001b[39m,\n  \u001b[32m'Error fetching saved views: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\\n'\u001b[39m +\n    \u001b[32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\\n'\u001b[39m +\n    \u001b[32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'\u001b[39m,\n  \u001b[32m'Error fetching brands: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\\n'\u001b[39m +\n    \u001b[32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'\u001b[39m\n]\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:09.988Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-4da02c01f94906859d61", "file": "comprehensive-dashboard.spec.ts", "line": 200, "column": 7}, {"title": "should load within performance thresholds", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 17, "parallelIndex": 7, "status": "passed", "duration": 6169, "errors": [], "stdout": [{"text": "Dashboard loaded in 1333ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:11.598Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-95be82971e6ac17c6b8a", "file": "comprehensive-dashboard.spec.ts", "line": 230, "column": 7}]}]}, {"title": "comprehensive-error-handling.spec.ts", "file": "comprehensive-error-handling.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Error Handling and Edge Cases", "file": "comprehensive-error-handling.spec.ts", "line": 9, "column": 6, "specs": [{"title": "should handle 404 errors gracefully", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 1, "status": "failed", "duration": 9702, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:39:50", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 50, "line": 39}, "snippet": "\u001b[0m \u001b[90m 37 |\u001b[39m\n \u001b[90m 38 |\u001b[39m       \u001b[90m// Either shows 404, redirects, or shows a valid page (some routes might be valid)\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 39 |\u001b[39m       expect(is404 \u001b[33m||\u001b[39m isRedirect \u001b[33m||\u001b[39m isValidPage)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 40 |\u001b[39m       \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m`error-404-${url.replace(/\\//g, '-')}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 41 |\u001b[39m     }\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 50, "line": 39}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 37 |\u001b[39m\n \u001b[90m 38 |\u001b[39m       \u001b[90m// Either shows 404, redirects, or shows a valid page (some routes might be valid)\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 39 |\u001b[39m       expect(is404 \u001b[33m||\u001b[39m isRedirect \u001b[33m||\u001b[39m isValidPage)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 40 |\u001b[39m       \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m`error-404-${url.replace(/\\//g, '-')}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 41 |\u001b[39m     }\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:39:50\u001b[22m"}], "stdout": [{"text": "/nonexistent-page -> http://localhost:6699/nonexistent-page\n"}, {"text": "/dashboard/invalid -> http://localhost:6699/dashboard/invalid\n"}, {"text": "/admin/nonexistent -> http://localhost:6699/admin/nonexistent\n"}, {"text": "/marketing-dashboard/invalid-campaign-id -> http://localhost:6699/marketing-dashboard/invalid-campaign-id\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:11.683Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 50, "line": 39}}], "status": "unexpected"}], "id": "fced13aa990448fd797b-2e176de4e4573722893a", "file": "comprehensive-error-handling.spec.ts", "line": 15, "column": 7}, {"title": "should handle network errors and offline scenarios", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 9832, "errors": [], "stdout": [{"text": "Expected network error: page.goto: net::ERR_INTERNET_DISCONNECTED at http://localhost:6699/marketing-dashboard\nCall log:\n\u001b[2m  - navigating to \"http://localhost:6699/marketing-dashboard\", waiting until \"load\"\u001b[22m\n\n"}, {"text": "Offline handling: Not detected\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:13.696Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "fced13aa990448fd797b-200626f1d67aaf8ff047", "file": "comprehensive-error-handling.spec.ts", "line": 44, "column": 7}, {"title": "should handle API errors gracefully", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 16, "parallelIndex": 2, "status": "failed", "duration": 6782, "error": {"message": "Error: locator.isVisible: Error: strict mode violation: locator('text=/error|failed|something went wrong/i') resolved to 2 elements:\n    1) <div data-slot=\"card-description\" class=\"text-muted-foreground text-sm\">Error loading data: HTTP 500: Internal Server Err…</div> aka getByText('Error loading data: HTTP 500')\n    2) <div class=\"text-center text-red-500\">Server error occurred. Please refresh the page or…</div> aka getByText('Server error occurred. Please')\n\nCall log:\n\u001b[2m    - checking visibility of locator('text=/error|failed|something went wrong/i')\u001b[22m\n", "stack": "Error: locator.isVisible: Error: strict mode violation: locator('text=/error|failed|something went wrong/i') resolved to 2 elements:\n    1) <div data-slot=\"card-description\" class=\"text-muted-foreground text-sm\">Error loading data: HTTP 500: Internal Server Err…</div> aka getByText('Error loading data: HTTP 500')\n    2) <div class=\"text-center text-red-500\">Server error occurred. Please refresh the page or…</div> aka getByText('Server error occurred. Please')\n\nCall log:\n\u001b[2m    - checking visibility of locator('text=/error|failed|something went wrong/i')\u001b[22m\n\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:92:49", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 49, "line": 92}, "snippet": "\u001b[0m \u001b[90m 90 |\u001b[39m     \u001b[90m// Should show error message or fallback content\u001b[39m\n \u001b[90m 91 |\u001b[39m     \u001b[36mconst\u001b[39m errorMessage \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=/error|failed|something went wrong/i'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 92 |\u001b[39m     \u001b[36mconst\u001b[39m hasErrorHandling \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m errorMessage\u001b[33m.\u001b[39misVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 93 |\u001b[39m\n \u001b[90m 94 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m`API error handling: ${hasErrorHandling ? 'Detected' : 'Not detected'}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 95 |\u001b[39m     \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m'api-error-500'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 49, "line": 92}, "message": "Error: locator.isVisible: Error: strict mode violation: locator('text=/error|failed|something went wrong/i') resolved to 2 elements:\n    1) <div data-slot=\"card-description\" class=\"text-muted-foreground text-sm\">Error loading data: HTTP 500: Internal Server Err…</div> aka getByText('Error loading data: HTTP 500')\n    2) <div class=\"text-center text-red-500\">Server error occurred. Please refresh the page or…</div> aka getByText('Server error occurred. Please')\n\nCall log:\n\u001b[2m    - checking visibility of locator('text=/error|failed|something went wrong/i')\u001b[22m\n\n\n\u001b[0m \u001b[90m 90 |\u001b[39m     \u001b[90m// Should show error message or fallback content\u001b[39m\n \u001b[90m 91 |\u001b[39m     \u001b[36mconst\u001b[39m errorMessage \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=/error|failed|something went wrong/i'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 92 |\u001b[39m     \u001b[36mconst\u001b[39m hasErrorHandling \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m errorMessage\u001b[33m.\u001b[39misVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 93 |\u001b[39m\n \u001b[90m 94 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m`API error handling: ${hasErrorHandling ? 'Detected' : 'Not detected'}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 95 |\u001b[39m     \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m'api-error-500'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:92:49\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:14.716Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-5bfc5-andle-API-errors-gracefully-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-5bfc5-andle-API-errors-gracefully-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-5bfc5-andle-API-errors-gracefully-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 49, "line": 92}}], "status": "unexpected"}], "id": "fced13aa990448fd797b-e29093ae1f4ba65fe1ad", "file": "comprehensive-error-handling.spec.ts", "line": 76, "column": 7}, {"title": "should handle authentication errors", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 11, "parallelIndex": 3, "status": "failed", "duration": 8434, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:122:43", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 43, "line": 122}, "snippet": "\u001b[0m \u001b[90m 120 |\u001b[39m\n \u001b[90m 121 |\u001b[39m       \u001b[90m// Either redirected to sign-in or stayed on dashboard (if session is still valid)\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 122 |\u001b[39m       expect(isOnSignIn \u001b[33m||\u001b[39m isOnDashboard)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 123 |\u001b[39m\n \u001b[90m 124 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m`${url} -> redirected to sign-in`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 125 |\u001b[39m     }\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 43, "line": 122}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 120 |\u001b[39m\n \u001b[90m 121 |\u001b[39m       \u001b[90m// Either redirected to sign-in or stayed on dashboard (if session is still valid)\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 122 |\u001b[39m       expect(isOnSignIn \u001b[33m||\u001b[39m isOnDashboard)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 123 |\u001b[39m\n \u001b[90m 124 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m`${url} -> redirected to sign-in`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 125 |\u001b[39m     }\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:122:43\u001b[22m"}], "stdout": [{"text": "/dashboard -> redirected to sign-in\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:15.313Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 43, "line": 122}}], "status": "unexpected"}], "id": "fced13aa990448fd797b-8c537d4584aa02be048a", "file": "comprehensive-error-handling.spec.ts", "line": 98, "column": 7}, {"title": "should handle form validation errors", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 13, "parallelIndex": 6, "status": "passed", "duration": 7390, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:16.720Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "fced13aa990448fd797b-d3f696244c2aa4d69252", "file": "comprehensive-error-handling.spec.ts", "line": 131, "column": 7}, {"title": "should handle large data sets without crashing", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 17, "parallelIndex": 7, "status": "failed", "duration": 11983, "error": {"message": "ReferenceError: getMainContent is not defined", "stack": "ReferenceError: getMainContent is not defined\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:180:27", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 27, "line": 180}, "snippet": "\u001b[0m \u001b[90m 178 |\u001b[39m\n \u001b[90m 179 |\u001b[39m       \u001b[90m// Page should still be responsive\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 180 |\u001b[39m       \u001b[36mconst\u001b[39m mainContent \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m getMainContent(page)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 181 |\u001b[39m       \u001b[36mconst\u001b[39m isResponsive \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m mainContent\u001b[33m.\u001b[39misVisible()\u001b[33m;\u001b[39m\n \u001b[90m 182 |\u001b[39m       expect(isResponsive)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 183 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 27, "line": 180}, "message": "ReferenceError: getMainContent is not defined\n\n\u001b[0m \u001b[90m 178 |\u001b[39m\n \u001b[90m 179 |\u001b[39m       \u001b[90m// Page should still be responsive\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 180 |\u001b[39m       \u001b[36mconst\u001b[39m mainContent \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m getMainContent(page)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 181 |\u001b[39m       \u001b[36mconst\u001b[39m isResponsive \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m mainContent\u001b[33m.\u001b[39misVisible()\u001b[33m;\u001b[39m\n \u001b[90m 182 |\u001b[39m       expect(isResponsive)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 183 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:180:27\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:17.869Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-72ce6--data-sets-without-crashing-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-72ce6--data-sets-without-crashing-chromium/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 27, "line": 180}}], "status": "unexpected"}], "id": "fced13aa990448fd797b-5674aa103fe56936cbec", "file": "comprehensive-error-handling.spec.ts", "line": 164, "column": 7}, {"title": "should handle browser back/forward navigation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 14, "parallelIndex": 4, "status": "passed", "duration": 14317, "errors": [], "stdout": [{"text": "Browser navigation: Working correctly\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:18.329Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "fced13aa990448fd797b-908245d7230eea4c023b", "file": "comprehensive-error-handling.spec.ts", "line": 189, "column": 7}, {"title": "should handle page refresh and state preservation", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 18, "parallelIndex": 1, "status": "failed", "duration": 9734, "error": {"message": "ReferenceError: getMainContent is not defined", "stack": "ReferenceError: getMainContent is not defined\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:235:25", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 25, "line": 235}, "snippet": "\u001b[0m \u001b[90m 233 |\u001b[39m\n \u001b[90m 234 |\u001b[39m     \u001b[90m// Page should load successfully after refresh\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 235 |\u001b[39m     \u001b[36mconst\u001b[39m mainContent \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m getMainContent(page)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 236 |\u001b[39m     \u001b[36mconst\u001b[39m isLoaded \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m mainContent\u001b[33m.\u001b[39misVisible()\u001b[33m;\u001b[39m\n \u001b[90m 237 |\u001b[39m     expect(isLoaded)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 238 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 25, "line": 235}, "message": "ReferenceError: getMainContent is not defined\n\n\u001b[0m \u001b[90m 233 |\u001b[39m\n \u001b[90m 234 |\u001b[39m     \u001b[90m// Page should load successfully after refresh\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 235 |\u001b[39m     \u001b[36mconst\u001b[39m mainContent \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m getMainContent(page)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 236 |\u001b[39m     \u001b[36mconst\u001b[39m isLoaded \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m mainContent\u001b[33m.\u001b[39misVisible()\u001b[33m;\u001b[39m\n \u001b[90m 237 |\u001b[39m     expect(isLoaded)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 238 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:235:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:21.729Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-a4ae1-resh-and-state-preservation-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-a4ae1-resh-and-state-preservation-chromium/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 25, "line": 235}}], "status": "unexpected"}], "id": "fced13aa990448fd797b-4aac42820df35f47bc43", "file": "comprehensive-error-handling.spec.ts", "line": 218, "column": 7}, {"title": "should handle concurrent user actions", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 19, "parallelIndex": 2, "status": "timedOut", "duration": 60098, "error": {"message": "\u001b[31mTest timeout of 60000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 60000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 60000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 16, "line": 261}, "message": "Error: page.waitForTimeout: Target page, context or browser has been closed\n\n\u001b[0m \u001b[90m 259 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[33mPromise\u001b[39m\u001b[33m.\u001b[39mall(promises)\u001b[33m;\u001b[39m\n \u001b[90m 260 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 261 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 262 |\u001b[39m     \u001b[36mawait\u001b[39m waitForLoadingToComplete(page)\u001b[33m;\u001b[39m\n \u001b[90m 263 |\u001b[39m\n \u001b[90m 264 |\u001b[39m     \u001b[90m// Page should still be functional\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:261:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:21.830Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/video.webm"}]}], "status": "unexpected"}], "id": "fced13aa990448fd797b-6ef62111590327d4e750", "file": "comprehensive-error-handling.spec.ts", "line": 243, "column": 7}, {"title": "should handle memory leaks during navigation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 32100, "errors": [], "stdout": [{"text": "Initial memory: 29.75MB\n"}, {"text": "Final memory: 29.75MB\n"}, {"text": "Memory increase: 0.00MB\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:23.536Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "fced13aa990448fd797b-461d3bc92413584e9dd0", "file": "comprehensive-error-handling.spec.ts", "line": 273, "column": 7}, {"title": "should handle edge cases in data filtering", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "passed", "duration": 12271, "errors": [], "stdout": [{"text": "Invalid date range handling: Detected\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:24.124Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "fced13aa990448fd797b-1c76e7613c59f307454c", "file": "comprehensive-error-handling.spec.ts", "line": 314, "column": 7}, {"title": "should handle session timeout gracefully", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 13, "parallelIndex": 6, "status": "passed", "duration": 9023, "errors": [], "stdout": [{"text": "Session timeout: Redirected to sign-in\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:24.119Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "fced13aa990448fd797b-5b27b05dfcf3bcea9458", "file": "comprehensive-error-handling.spec.ts", "line": 342, "column": 7}]}]}, {"title": "comprehensive-executive-summary.spec.ts", "file": "comprehensive-executive-summary.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Executive Summary Testing", "file": "comprehensive-executive-summary.spec.ts", "line": 14, "column": 6, "specs": [{"title": "should load executive summary with all components", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 7, "status": "passed", "duration": 6783, "errors": [], "stdout": [{"text": "Page title: Create Next App\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:30.258Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-edbc18e8c5268436b6ec", "file": "comprehensive-executive-summary.spec.ts", "line": 20, "column": 7}, {"title": "should display brand selector and period selector", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 22, "parallelIndex": 1, "status": "passed", "duration": 6474, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:31.974Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-a146f8e4919665dae76f", "file": "comprehensive-executive-summary.spec.ts", "line": 39, "column": 7}, {"title": "should display executive KPI cards with proper formatting", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "passed", "duration": 6980, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:32.647Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-be38ed06f94f062cd420", "file": "comprehensive-executive-summary.spec.ts", "line": 83, "column": 7}, {"title": "should switch between cards and slides view", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 14, "parallelIndex": 4, "status": "passed", "duration": 7998, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:32.658Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-939a7fb67443870ceb9b", "file": "comprehensive-executive-summary.spec.ts", "line": 113, "column": 7}, {"title": "should display trend charts and analysis", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 13, "parallelIndex": 6, "status": "failed", "duration": 8625, "error": {"message": "Error: locator.count: Unexpected token \"=\" while parsing css selector \"[data-testid*=\"trend\"], .trend, text=/trend|growth|change/i\". Did you mean to CSS.escape it?", "stack": "Error: locator.count: Unexpected token \"=\" while parsing css selector \"[data-testid*=\"trend\"], .trend, text=/trend|growth|change/i\". Did you mean to CSS.escape it?\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:166:44", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts", "column": 44, "line": 166}, "snippet": "\u001b[0m \u001b[90m 164 |\u001b[39m     \u001b[90m// Look for trend-specific elements\u001b[39m\n \u001b[90m 165 |\u001b[39m     \u001b[36mconst\u001b[39m trendElements \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid*=\"trend\"], .trend, text=/trend|growth|change/i'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 166 |\u001b[39m     \u001b[36mconst\u001b[39m trendCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m trendElements\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 167 |\u001b[39m\n \u001b[90m 168 |\u001b[39m     \u001b[90m// Look for trend indicators (up/down arrows, colors)\u001b[39m\n \u001b[90m 169 |\u001b[39m     \u001b[36mconst\u001b[39m trendIndicators \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.trend-up, .trend-down, .positive, .negative, [class*=\"increase\"], [class*=\"decrease\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts", "column": 44, "line": 166}, "message": "Error: locator.count: Unexpected token \"=\" while parsing css selector \"[data-testid*=\"trend\"], .trend, text=/trend|growth|change/i\". Did you mean to CSS.escape it?\n\n\u001b[0m \u001b[90m 164 |\u001b[39m     \u001b[90m// Look for trend-specific elements\u001b[39m\n \u001b[90m 165 |\u001b[39m     \u001b[36mconst\u001b[39m trendElements \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid*=\"trend\"], .trend, text=/trend|growth|change/i'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 166 |\u001b[39m     \u001b[36mconst\u001b[39m trendCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m trendElements\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 167 |\u001b[39m\n \u001b[90m 168 |\u001b[39m     \u001b[90m// Look for trend indicators (up/down arrows, colors)\u001b[39m\n \u001b[90m 169 |\u001b[39m     \u001b[36mconst\u001b[39m trendIndicators \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.trend-up, .trend-down, .positive, .negative, [class*=\"increase\"], [class*=\"decrease\"]'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:166:44\u001b[22m"}], "stdout": [{"text": "Found 3 charts with selector: .recharts-wrapper\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:33.151Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-executive-su-d4c94-y-trend-charts-and-analysis-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-executive-su-d4c94-y-trend-charts-and-analysis-chromium/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts", "column": 44, "line": 166}}], "status": "unexpected"}], "id": "0a5a97eda325cb9c37e6-b051bf0a519664a2f954", "file": "comprehensive-executive-summary.spec.ts", "line": 156, "column": 7}, {"title": "should handle PDF export functionality", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "passed", "duration": 8376, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:36.496Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-2777232c2ec0185122e1", "file": "comprehensive-executive-summary.spec.ts", "line": 176, "column": 7}, {"title": "should display period comparison data", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 7, "status": "passed", "duration": 6375, "errors": [], "stdout": [{"text": "Found 10 comparison elements and 0 comparison values\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:37.153Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-0ff33efba48fb91860f6", "file": "comprehensive-executive-summary.spec.ts", "line": 205, "column": 7}, {"title": "should handle currency switching", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 22, "parallelIndex": 1, "status": "passed", "duration": 5939, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:38.535Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-fc190dcac7328cfa725d", "file": "comprehensive-executive-summary.spec.ts", "line": 225, "column": 7}, {"title": "should display methodology and notes if available", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "passed", "duration": 5755, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:39.632Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-e9d48d12321137dd4e0b", "file": "comprehensive-executive-summary.spec.ts", "line": 258, "column": 7}, {"title": "should be responsive across different screen sizes", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 14, "parallelIndex": 4, "status": "failed", "duration": 6976, "error": {"message": "ReferenceError: getMainContent is not defined", "stack": "ReferenceError: getMainContent is not defined\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:297:27", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts", "column": 27, "line": 297}, "snippet": "\u001b[0m \u001b[90m 295 |\u001b[39m\n \u001b[90m 296 |\u001b[39m       \u001b[90m// Verify main elements are still visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 297 |\u001b[39m       \u001b[36mconst\u001b[39m mainContent \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m getMainContent(page)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 298 |\u001b[39m       \u001b[36mawait\u001b[39m expect(mainContent)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 299 |\u001b[39m\n \u001b[90m 300 |\u001b[39m       \u001b[90m// Check KPI cards layout on different screens\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts", "column": 27, "line": 297}, "message": "ReferenceError: getMainContent is not defined\n\n\u001b[0m \u001b[90m 295 |\u001b[39m\n \u001b[90m 296 |\u001b[39m       \u001b[90m// Verify main elements are still visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 297 |\u001b[39m       \u001b[36mconst\u001b[39m mainContent \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m getMainContent(page)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 298 |\u001b[39m       \u001b[36mawait\u001b[39m expect(mainContent)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 299 |\u001b[39m\n \u001b[90m 300 |\u001b[39m       \u001b[90m// Check KPI cards layout on different screens\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:297:27\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:40.661Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-executive-su-1b50d-ross-different-screen-sizes-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-executive-su-1b50d-ross-different-screen-sizes-chromium/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts", "column": 27, "line": 297}}], "status": "unexpected"}], "id": "0a5a97eda325cb9c37e6-a394186e34028aa6596d", "file": "comprehensive-executive-summary.spec.ts", "line": 282, "column": 7}, {"title": "should have proper accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 23, "parallelIndex": 6, "status": "passed", "duration": 6224, "errors": [], "stdout": [{"text": "Executive Summary landmarks: 0\n"}, {"text": "Executive Summary accessibility info: { ariaLabels: \u001b[33m29\u001b[39m, roles: \u001b[33m13\u001b[39m, headings: \u001b[33m11\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:42.125Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-94515537b1e16b52ee05", "file": "comprehensive-executive-summary.spec.ts", "line": 313, "column": 7}, {"title": "should load within performance thresholds", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 7, "status": "passed", "duration": 5825, "errors": [], "stdout": [{"text": "Executive Summary loaded in 1173ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:43.532Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-8c956e7ff334c766de40", "file": "comprehensive-executive-summary.spec.ts", "line": 328, "column": 7}, {"title": "should not have critical console errors", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 22, "parallelIndex": 1, "status": "passed", "duration": 6247, "errors": [], "stdout": [{"text": "Critical console errors found: [\n  \u001b[32m'Error fetching saved views: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\\n'\u001b[39m +\n    \u001b[32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\\n'\u001b[39m +\n    \u001b[32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'\u001b[39m,\n  \u001b[32m'Error fetching brands: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\\n'\u001b[39m +\n    \u001b[32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9578:33)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9581:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)'\u001b[39m,\n  \u001b[32m'Error fetching brands: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\\n'\u001b[39m +\n    \u001b[32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'\u001b[39m\n]\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:44.479Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-beae99e6201ea459605e", "file": "comprehensive-executive-summary.spec.ts", "line": 343, "column": 7}]}]}, {"title": "comprehensive-marketing-dashboard.spec.ts", "file": "comprehensive-marketing-dashboard.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Marketing Dashboard Testing", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 16, "column": 6, "specs": [{"title": "should load marketing dashboard with all components", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "passed", "duration": 6299, "errors": [], "stdout": [{"text": "Page title: Create Next App\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:44.879Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-fd258598690994fc76da", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 22, "column": 7}, {"title": "should display marketing KPI cards", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "passed", "duration": 6286, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:45.395Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-ee35e66dca54085de23c", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 41, "column": 7}, {"title": "should display campaign data table with proper functionality", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 24, "parallelIndex": 4, "status": "passed", "duration": 6114, "errors": [], "stdout": [{"text": "Campaign table has 12 headers and 50 rows\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:47.966Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-2b58c28b8273d739e102", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 64, "column": 7}, {"title": "should render spend breakdown charts", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 23, "parallelIndex": 6, "status": "failed", "duration": 8037, "error": {"message": "Error: locator.count: Unexpected token \"=\" while parsing css selector \"[data-testid*=\"spend\"], [class*=\"spend\"], text=/spend breakdown/i\". Did you mean to CSS.escape it?", "stack": "Error: locator.count: Unexpected token \"=\" while parsing css selector \"[data-testid*=\"spend\"], [class*=\"spend\"], text=/spend breakdown/i\". Did you mean to CSS.escape it?\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts:104:47", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts", "column": 47, "line": 104}, "snippet": "\u001b[0m \u001b[90m 102 |\u001b[39m     \u001b[90m// Look for spend breakdown specific charts\u001b[39m\n \u001b[90m 103 |\u001b[39m     \u001b[36mconst\u001b[39m spendCharts \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid*=\"spend\"], [class*=\"spend\"], text=/spend breakdown/i'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 104 |\u001b[39m     \u001b[36mconst\u001b[39m spendChartCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m spendCharts\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 105 |\u001b[39m\n \u001b[90m 106 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m`Found ${spendChartCount} spend-related chart elements`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 107 |\u001b[39m     \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m'marketing-dashboard-spend-charts'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts", "column": 47, "line": 104}, "message": "Error: locator.count: Unexpected token \"=\" while parsing css selector \"[data-testid*=\"spend\"], [class*=\"spend\"], text=/spend breakdown/i\". Did you mean to CSS.escape it?\n\n\u001b[0m \u001b[90m 102 |\u001b[39m     \u001b[90m// Look for spend breakdown specific charts\u001b[39m\n \u001b[90m 103 |\u001b[39m     \u001b[36mconst\u001b[39m spendCharts \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid*=\"spend\"], [class*=\"spend\"], text=/spend breakdown/i'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 104 |\u001b[39m     \u001b[36mconst\u001b[39m spendChartCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m spendCharts\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 105 |\u001b[39m\n \u001b[90m 106 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m`Found ${spendChartCount} spend-related chart elements`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 107 |\u001b[39m     \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m'marketing-dashboard-spend-charts'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts:104:47\u001b[22m"}], "stdout": [{"text": "Found 9 charts with selector: .recharts-wrapper\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:48.464Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-marketing-da-80b02-nder-spend-breakdown-charts-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-marketing-da-80b02-nder-spend-breakdown-charts-chromium/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts", "column": 47, "line": 104}}], "status": "unexpected"}], "id": "3d5e7d77d5c4168dec16-0209b8a324a45bafeda1", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 94, "column": 7}, {"title": "should have functional marketing filters", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 7, "status": "passed", "duration": 8291, "errors": [], "stdout": [{"text": "Found 1 active filters\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:49.366Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-bc6b5e788d34d6df7d43", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 110, "column": 7}, {"title": "should allow campaign search and filtering", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 22, "parallelIndex": 1, "status": "passed", "duration": 5976, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:50.734Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-495b0de60c02b4f03dbe", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 168, "column": 7}, {"title": "should navigate to campaign details", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "passed", "duration": 6160, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:51.184Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-c86317274e379426ca70", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 190, "column": 7}, {"title": "should display proper marketing metrics calculations", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "passed", "duration": 6275, "errors": [], "stdout": [{"text": "Found 2 marketing metrics out of 8 expected\n"}, {"text": "Found 57 percentage values and 168 currency values\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:51.686Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-afe33de2b8573e891233", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 225, "column": 7}, {"title": "should handle data export functionality", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 24, "parallelIndex": 4, "status": "passed", "duration": 5760, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:54.172Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-4eadf400b53eee94110e", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 271, "column": 7}, {"title": "should be responsive across different screen sizes", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "failed", "duration": 6881, "error": {"message": "ReferenceError: getMainContent is not defined", "stack": "ReferenceError: getMainContent is not defined\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts:306:27", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts", "column": 27, "line": 306}, "snippet": "\u001b[0m \u001b[90m 304 |\u001b[39m\n \u001b[90m 305 |\u001b[39m       \u001b[90m// Verify main elements are still visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 306 |\u001b[39m       \u001b[36mconst\u001b[39m mainContent \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m getMainContent(page)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 307 |\u001b[39m       \u001b[36mawait\u001b[39m expect(mainContent)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 308 |\u001b[39m\n \u001b[90m 309 |\u001b[39m       \u001b[90m// Check table responsiveness\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts", "column": 27, "line": 306}, "message": "ReferenceError: getMainContent is not defined\n\n\u001b[0m \u001b[90m 304 |\u001b[39m\n \u001b[90m 305 |\u001b[39m       \u001b[90m// Verify main elements are still visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 306 |\u001b[39m       \u001b[36mconst\u001b[39m mainContent \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m getMainContent(page)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 307 |\u001b[39m       \u001b[36mawait\u001b[39m expect(mainContent)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 308 |\u001b[39m\n \u001b[90m 309 |\u001b[39m       \u001b[90m// Check table responsiveness\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts:306:27\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:55.644Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-marketing-da-de393-ross-different-screen-sizes-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-marketing-da-de393-ross-different-screen-sizes-chromium/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-marketing-dashboard.spec.ts", "column": 27, "line": 306}}], "status": "unexpected"}], "id": "3d5e7d77d5c4168dec16-9d8723fc6ef8d74e67d7", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 291, "column": 7}, {"title": "should have proper accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 25, "parallelIndex": 6, "status": "passed", "duration": 6244, "errors": [], "stdout": [{"text": "Marketing Dashboard landmarks: 0\n"}, {"text": "Marketing Dashboard accessibility info: { ariaLabels: \u001b[33m1\u001b[39m, roles: \u001b[33m9\u001b[39m, headings: \u001b[33m4\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:56.876Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-137bd2b2a6d37164e03b", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 323, "column": 7}, {"title": "should load within performance thresholds", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 22, "parallelIndex": 1, "status": "passed", "duration": 5883, "errors": [], "stdout": [{"text": "Marketing Dashboard loaded in 1240ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:56.719Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-f749b7daf31bcaeea641", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 349, "column": 7}, {"title": "should not have critical console errors", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "passed", "duration": 6375, "errors": [], "stdout": [{"text": "Critical console errors found: [\n  \u001b[32m'Error fetching brands: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\\n'\u001b[39m +\n    \u001b[32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9578:33)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9618:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9581:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)\\n'\u001b[39m +\n    \u001b[32m'    at commitPassiveMountOnFiber (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9577:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraversePassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9569:106)'\u001b[39m,\n  \u001b[32m'Error fetching saved views: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\\n'\u001b[39m +\n    \u001b[32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\\n'\u001b[39m +\n    \u001b[32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'\u001b[39m,\n  \u001b[32m'Error fetching brands: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\\n'\u001b[39m +\n    \u001b[32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'\u001b[39m\n]\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:57.348Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-4e868d7207cd9d0d88a5", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 364, "column": 7}]}]}, {"title": "comprehensive-performance.spec.ts", "file": "comprehensive-performance.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Performance Testing", "file": "comprehensive-performance.spec.ts", "line": 10, "column": 6, "specs": [{"title": "should measure page load performance across all main pages", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 7, "status": "passed", "duration": 13193, "errors": [], "stdout": [{"text": "Dashboard: 2009ms (max: 8000ms)\n"}, {"text": "Brand Deep Dive: 1206ms (max: 12000ms)\n"}, {"text": "Marketing Dashboard: 1068ms (max: 15000ms)\n"}, {"text": "Executive Summary: 1032ms (max: 10000ms)\n"}, {"text": "Budget: 817ms (max: 8000ms)\n"}, {"text": "AI Assistant: 1342ms (max: 6000ms)\n"}, {"text": "Admin Dashboard: 789ms (max: 8000ms)\n"}, {"text": "\n=== Performance Summary ===\n"}, {"text": "✅ PASS Dashboard: 2009ms\n"}, {"text": "✅ PASS Brand Deep Dive: 1206ms\n"}, {"text": "✅ PASS Marketing Dashboard: 1068ms\n"}, {"text": "✅ PASS Executive Summary: 1032ms\n"}, {"text": "✅ PASS Budget: 817ms\n"}, {"text": "✅ PASS AI Assistant: 1342ms\n"}, {"text": "✅ PASS Admin Dashboard: 789ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:57.665Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-1d56667aee38502da4ef", "file": "comprehensive-performance.spec.ts", "line": 16, "column": 7}, {"title": "should measure Time to First Contentful Paint (FCP)", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "passed", "duration": 21385, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:57.969Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-93da73880c03e7afe5ef", "file": "comprehensive-performance.spec.ts", "line": 73, "column": 7}, {"title": "should measure Largest Contentful Paint (LCP)", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 24, "parallelIndex": 4, "status": "passed", "duration": 27132, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:28:59.940Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-5c101555131811069b32", "file": "comprehensive-performance.spec.ts", "line": 103, "column": 7}, {"title": "should measure JavaScript bundle sizes", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 26, "parallelIndex": 5, "status": "passed", "duration": 7605, "errors": [], "stdout": [{"text": "JS Bundle: %5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_fd44f5a4._.js - 1.01KB\n"}, {"text": "JS Bundle: node_modules_%40swc_helpers_cjs_00636ac3._.js - 1.25KB\n"}, {"text": "JS Bundle: node_modules_next_dist_2ecbf5fa._.js - 17.73KB\n"}, {"text": "JS Bundle: node_modules_next_dist_client_8f19e6fb._.js - 166.58KB\n"}, {"text": "JS Bundle: node_modules_next_dist_compiled_2ce9398a._.js - 192.75KB\n"}, {"text": "JS Bundle: _e69f0d32._.js - 0.81KB\n"}, {"text": "JS Bundle: _93808211._.js - 16.07KB\n"}, {"text": "JS Bundle: app_favicon_ico_mjs_659ce808._.js - 0.55KB\n"}, {"text": "JS Bundle: node_modules_next_dist_1a6ee436._.js - 18.44KB\n"}, {"text": "JS Bundle: app_layout_tsx_c0237562._.js - 0.58KB\n"}, {"text": "JS Bundle: _45166852._.js - 17.95KB\n"}, {"text": "JS Bundle: node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js - 17.12KB\n"}, {"text": "JS Bundle: node_modules_lodash_f240f67a._.js - 32.65KB\n"}, {"text": "JS Bundle: node_modules_%40floating-ui_9ec1fa39._.js - 21.34KB\n"}, {"text": "JS Bundle: _5e541529._.js - 70.10KB\n"}, {"text": "JS Bundle: app_dashboard_page_tsx_63d9a548._.js - 0.87KB\n"}, {"text": "JS Bundle: node_modules_%40radix-ui_fead58fd._.js - 55.73KB\n"}, {"text": "JS Bundle: node_modules_recharts_es6_98d167ba._.js - 117.77KB\n"}, {"text": "JS Bundle: node_modules_53dbc141._.js - 142.33KB\n"}, {"text": "JS Bundle: %5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js - 4.05KB\n"}, {"text": "JS Bundle: %5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_66796270._.js - 0.57KB\n"}, {"text": "Total JS Bundle Size: 896.22KB\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:02.907Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-6ec5a6c72a2d51937516", "file": "comprehensive-performance.spec.ts", "line": 132, "column": 7}, {"title": "should test memory usage during navigation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 22, "parallelIndex": 1, "status": "passed", "duration": 10039, "errors": [], "stdout": [{"text": "/dashboard Memory: 35.57MB used\n"}, {"text": "/brand-deep-dive Memory: 35.57MB used\n"}, {"text": "/marketing-dashboard Memory: 35.57MB used\n"}, {"text": "/executive-summary Memory: 35.57MB used\n"}, {"text": "\n=== Memory Usage Summary ===\n"}, {"text": "/dashboard: 35.57MB used, 110.63MB total\n"}, {"text": "/brand-deep-dive: 35.57MB used, 110.63MB total\n"}, {"text": "/marketing-dashboard: 35.57MB used, 110.63MB total\n"}, {"text": "/executive-summary: 35.57MB used, 110.63MB total\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:02.610Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-ca9f8c82d816ffa61610", "file": "comprehensive-performance.spec.ts", "line": 162, "column": 7}, {"title": "should test network performance and resource loading", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 25, "parallelIndex": 6, "status": "passed", "duration": 7385, "errors": [], "stdout": [{"text": "Network Performance:\n"}, {"text": "Domain Lookup: 0.00ms\n"}, {"text": "Connection: 0.00ms\n"}, {"text": "Request: 259.30ms\n"}, {"text": "Response: 19.60ms\n"}, {"text": "DOM Processing: 16.30ms\n"}, {"text": "Total Resources: 36\n"}, {"text": "Slow Resources (>1s): 0\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:03.225Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-2e40d0cfd50b752a3d19", "file": "comprehensive-performance.spec.ts", "line": 201, "column": 7}, {"title": "should test performance under different network conditions", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "passed", "duration": 8024, "errors": [], "stdout": [{"text": "Dashboard load time with slow network: 2546ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:03.729Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-2f662ff03c0eb5065d72", "file": "comprehensive-performance.spec.ts", "line": 238, "column": 7}, {"title": "should test performance with large datasets", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 25, "parallelIndex": 6, "status": "passed", "duration": 13183, "errors": [], "stdout": [{"text": "/marketing-dashboard with data rendering: 4384ms\n"}, {"text": "/brand-deep-dive with data rendering: 4084ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:10.619Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-d0e0777ac1cb5a35fb09", "file": "comprehensive-performance.spec.ts", "line": 257, "column": 7}, {"title": "should test scroll performance on long pages", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 26, "parallelIndex": 5, "status": "passed", "duration": 7208, "errors": [], "stdout": [{"text": "Scroll performance: 1005ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:10.649Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-ec696b9e3ff5403b6776", "file": "comprehensive-performance.spec.ts", "line": 282, "column": 7}, {"title": "should test chart rendering performance", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 7, "status": "passed", "duration": 6836, "errors": [], "stdout": [{"text": "Chart rendering time: 1ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:10.864Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-382e241d17cafef30520", "file": "comprehensive-performance.spec.ts", "line": 309, "column": 7}, {"title": "should test accessibility performance", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "passed", "duration": 8029, "errors": [], "stdout": [{"text": "/dashboard accessibility scan: 1286ms\n"}, {"text": "Accessibility elements found: { ariaLabels: \u001b[33m21\u001b[39m, roles: \u001b[33m26\u001b[39m, headings: \u001b[33m3\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}, {"text": "/dashboard landmarks: 0\n"}, {"text": "/marketing-dashboard accessibility scan: 991ms\n"}, {"text": "Accessibility elements found: { ariaLabels: \u001b[33m1\u001b[39m, roles: \u001b[33m9\u001b[39m, headings: \u001b[33m4\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}, {"text": "/marketing-dashboard landmarks: 0\n"}, {"text": "/admin accessibility scan: 747ms\n"}, {"text": "Accessibility elements found: { ariaLabels: \u001b[33m1\u001b[39m, roles: \u001b[33m2\u001b[39m, headings: \u001b[33m4\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}, {"text": "/admin landmarks: 0\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:11.759Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-f179641bd966b296b09f", "file": "comprehensive-performance.spec.ts", "line": 341, "column": 7}]}]}, {"title": "comprehensive-test-runner.spec.ts", "file": "comprehensive-test-runner.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Application Test Suite", "file": "comprehensive-test-runner.spec.ts", "line": 12, "column": 6, "specs": [{"title": "should run complete application smoke test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 22, "parallelIndex": 1, "status": "passed", "duration": 14949, "errors": [], "stdout": [{"text": "🚀 Starting comprehensive application smoke test...\n"}, {"text": "📄 Testing Dashboard...\n"}, {"text": "✅ Dashboard: 1166ms\n"}, {"text": "📄 Testing Brand Deep Dive...\n"}, {"text": "✅ Brand Deep Dive: 965ms\n"}, {"text": "📄 Testing Marketing Dashboard...\n"}, {"text": "✅ Marketing Dashboard: 988ms\n"}, {"text": "📄 Testing Executive Summary...\n"}, {"text": "✅ Executive Summary: 1003ms\n"}, {"text": "📄 Testing Budget...\n"}, {"text": "✅ Budget: 768ms\n"}, {"text": "📄 Testing AI Assistant...\n"}, {"text": "✅ AI Assistant: 1014ms\n"}, {"text": "🔐 Testing admin pages...\n"}, {"text": "✅ Admin Dashboard: Accessible\n"}, {"text": "✅ Admin Users: Accessible\n"}, {"text": "✅ Admin Roles: Accessible\n"}, {"text": "✅ Admin Permissions: Accessible\n"}, {"text": "\n📊 SMOKE TEST SUMMARY:\n"}, {"text": "========================\n"}, {"text": "✅ Dashboard: 1166ms\n"}, {"text": "✅ Brand Deep Dive: 965ms\n"}, {"text": "✅ Marketing Dashboard: 988ms\n"}, {"text": "✅ Executive Summary: 1003ms\n"}, {"text": "✅ Budget: 768ms\n"}, {"text": "✅ AI Assistant: 1014ms\n"}, {"text": "\n🎯 Results: 6/6 tests passed\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:12.659Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "68b8c085ca31fe9a4ab1-b584c045f062ccf853a7", "file": "comprehensive-test-runner.spec.ts", "line": 18, "column": 7}, {"title": "should verify authentication system works correctly", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 7, "status": "failed", "duration": 19165, "error": {"message": "TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\u001b[22m\n", "stack": "TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\u001b[22m\n\n    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)\n    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 26, "line": 46}, "snippet": "\u001b[90m   at \u001b[39mutils/test-helpers.ts:46\n\n\u001b[0m \u001b[90m 44 |\u001b[39m\n \u001b[90m 45 |\u001b[39m     \u001b[90m// Wait for the button to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 46 |\u001b[39m     \u001b[36mawait\u001b[39m credentialsTab\u001b[33m.\u001b[39mwaitFor({ state\u001b[33m:\u001b[39m \u001b[32m'visible'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 47 |\u001b[39m     \u001b[36mawait\u001b[39m credentialsTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1500\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Wait for tab switch animation\u001b[39m\n \u001b[90m 49 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 26, "line": 46}, "message": "TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:46\n\n\u001b[0m \u001b[90m 44 |\u001b[39m\n \u001b[90m 45 |\u001b[39m     \u001b[90m// Wait for the button to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 46 |\u001b[39m     \u001b[36mawait\u001b[39m credentialsTab\u001b[33m.\u001b[39mwaitFor({ state\u001b[33m:\u001b[39m \u001b[32m'visible'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 47 |\u001b[39m     \u001b[36mawait\u001b[39m credentialsTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1500\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Wait for tab switch animation\u001b[39m\n \u001b[90m 49 |\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7\u001b[22m"}], "stdout": [{"text": "🔐 Testing authentication system...\n"}, {"text": "Sign in failed: locator.waitFor: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\u001b[22m\n\n    at signInWithCredentials \u001b[90m(/Users/<USER>/Projects/NOLK/nolk-v4/\u001b[39mtests/e2e/utils/test-helpers.ts:46:26\u001b[90m)\u001b[39m\n    at signInAsAdmin \u001b[90m(/Users/<USER>/Projects/NOLK/nolk-v4/\u001b[39mtests/e2e/utils/test-helpers.ts:75:3\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/Projects/NOLK/nolk-v4/\u001b[39mtests/e2e/comprehensive-test-runner.spec.ts:127:7 {\n  name: \u001b[32m'TimeoutError'\u001b[39m,\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    stepId: \u001b[32m'pw:api@25'\u001b[39m,\n    location: {\n      file: \u001b[32m'/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts'\u001b[39m,\n      line: \u001b[33m46\u001b[39m,\n      column: \u001b[33m26\u001b[39m,\n      function: \u001b[32m'signInWithCredentials'\u001b[39m\n    },\n    category: \u001b[32m'pw:api'\u001b[39m,\n    title: \u001b[32m'locator.waitFor(button >> internal:has-text=\"Username/Password\"i)'\u001b[39m,\n    apiName: \u001b[32m'locator.waitFor'\u001b[39m,\n    params: {\n      selector: \u001b[32m'button >> internal:has-text=\"Username/Password\"i'\u001b[39m,\n      strict: \u001b[33mtrue\u001b[39m,\n      omitReturnValue: \u001b[33mtrue\u001b[39m,\n      state: \u001b[32m'visible'\u001b[39m,\n      timeout: \u001b[33m10000\u001b[39m\n    },\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'pw:api@25'\u001b[39m\n    },\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1748572176497\u001b[39m,\n    error: {\n      message: \u001b[32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\\x1B[22m\\n\"\u001b[39m,\n      stack: \u001b[32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\\x1B[22m\\n\"\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)\\n'\u001b[39m +\n        \u001b[32m'    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)\\n'\u001b[39m +\n        \u001b[32m'    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}, {"text": "❌ Authentication test failed: locator.waitFor: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\u001b[22m\n\n    at signInWithCredentials \u001b[90m(/Users/<USER>/Projects/NOLK/nolk-v4/\u001b[39mtests/e2e/utils/test-helpers.ts:46:26\u001b[90m)\u001b[39m\n    at signInAsAdmin \u001b[90m(/Users/<USER>/Projects/NOLK/nolk-v4/\u001b[39mtests/e2e/utils/test-helpers.ts:75:3\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/Projects/NOLK/nolk-v4/\u001b[39mtests/e2e/comprehensive-test-runner.spec.ts:127:7 {\n  name: \u001b[32m'TimeoutError'\u001b[39m,\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    stepId: \u001b[32m'pw:api@25'\u001b[39m,\n    location: {\n      file: \u001b[32m'/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts'\u001b[39m,\n      line: \u001b[33m46\u001b[39m,\n      column: \u001b[33m26\u001b[39m,\n      function: \u001b[32m'signInWithCredentials'\u001b[39m\n    },\n    category: \u001b[32m'pw:api'\u001b[39m,\n    title: \u001b[32m'locator.waitFor(button >> internal:has-text=\"Username/Password\"i)'\u001b[39m,\n    apiName: \u001b[32m'locator.waitFor'\u001b[39m,\n    params: {\n      selector: \u001b[32m'button >> internal:has-text=\"Username/Password\"i'\u001b[39m,\n      strict: \u001b[33mtrue\u001b[39m,\n      omitReturnValue: \u001b[33mtrue\u001b[39m,\n      state: \u001b[32m'visible'\u001b[39m,\n      timeout: \u001b[33m10000\u001b[39m\n    },\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'pw:api@25'\u001b[39m\n    },\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1748572176497\u001b[39m,\n    error: {\n      message: \u001b[32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\\x1B[22m\\n\"\u001b[39m,\n      stack: \u001b[32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\\x1B[22m\\n\"\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)\\n'\u001b[39m +\n        \u001b[32m'    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)\\n'\u001b[39m +\n        \u001b[32m'    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:17.709Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 26, "line": 46}}], "status": "unexpected"}], "id": "68b8c085ca31fe9a4ab1-a084ac754b4d6845a235", "file": "comprehensive-test-runner.spec.ts", "line": 122, "column": 7}, {"title": "should verify all navigation links work", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 26, "parallelIndex": 5, "status": "passed", "duration": 10686, "errors": [], "stdout": [{"text": "🧭 Testing navigation system...\n"}, {"text": "✅ Navigation to Dashboard: Working\n"}, {"text": "✅ Navigation to Marketing Dashboard: Working\n"}, {"text": "✅ Navigation to Executive Summary: Working\n"}, {"text": "✅ Navigation to Budget: Working\n"}, {"text": "✅ Navigation to AI Assistant: Working\n"}, {"text": "🎯 Navigation: 5/6 links working\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:17.868Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "68b8c085ca31fe9a4ab1-891466e9a0d3c7f664a2", "file": "comprehensive-test-runner.spec.ts", "line": 143, "column": 7}, {"title": "should verify data loading and display", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "passed", "duration": 8295, "errors": [], "stdout": [{"text": "📊 Testing data loading and display...\n"}, {"text": "📈 Dashboard KPIs: 0 data elements found\n"}, {"text": "⚠️ Dashboard KPIs: No data elements found\n"}, {"text": "📈 Marketing Campaigns: 1 data elements found\n"}, {"text": "✅ Marketing Campaigns: Data loaded successfully\n"}, {"text": "📈 Brand Data: 0 data elements found\n"}, {"text": "⚠️ Brand Data: No data elements found\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:19.360Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "68b8c085ca31fe9a4ab1-585574cae176d7c31753", "file": "comprehensive-test-runner.spec.ts", "line": 189, "column": 7}, {"title": "should verify responsive design works", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "passed", "duration": 9735, "errors": [], "stdout": [{"text": "📱 Testing responsive design...\n"}, {"text": "✅ Desktop (1920x1080): Layout working\n"}, {"text": "✅ Tablet (1024x768): Layout working\n"}, {"text": "✅ Mobile (375x667): Layout working\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:19.795Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "68b8c085ca31fe9a4ab1-a204b6df0fba0ed7cacc", "file": "comprehensive-test-runner.spec.ts", "line": 224, "column": 7}, {"title": "should verify accessibility standards", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 27, "parallelIndex": 2, "status": "passed", "duration": 8161, "errors": [], "stdout": [{"text": "♿ Testing accessibility standards...\n"}, {"text": "🔍 /dashboard accessibility:\n"}, {"text": "  - Headings: 3\n"}, {"text": "  - ARIA labels: 21\n"}, {"text": "  - Landmarks: 0\n"}, {"text": "⚠️ /dashboard: No landmarks found, but continuing test\n"}, {"text": "✅ /dashboard: Accessibility standards met\n"}, {"text": "🔍 /marketing-dashboard accessibility:\n"}, {"text": "  - Headings: 4\n"}, {"text": "  - ARIA labels: 1\n"}, {"text": "  - Landmarks: 0\n"}, {"text": "⚠️ /marketing-dashboard: No landmarks found, but continuing test\n"}, {"text": "✅ /marketing-dashboard: Accessibility standards met\n"}, {"text": "🔍 /admin accessibility:\n"}, {"text": "  - Headings: 4\n"}, {"text": "  - ARIA labels: 1\n"}, {"text": "  - Landmarks: 0\n"}, {"text": "⚠️ /admin: No landmarks found, but continuing test\n"}, {"text": "✅ /admin: Accessibility standards met\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:22.410Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "68b8c085ca31fe9a4ab1-786bf0f134286060393f", "file": "comprehensive-test-runner.spec.ts", "line": 254, "column": 7}, {"title": "should verify performance benchmarks", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 25, "parallelIndex": 6, "status": "passed", "duration": 7697, "errors": [], "stdout": [{"text": "⚡ Testing performance benchmarks...\n"}, {"text": "✅ Dashboard: 1134ms (under 8000ms limit)\n"}, {"text": "✅ Marketing Dashboard: 1005ms (under 15000ms limit)\n"}, {"text": "✅ Admin Dashboard: 704ms (under 8000ms limit)\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:23.812Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "68b8c085ca31fe9a4ab1-982dc5b689434e831929", "file": "comprehensive-test-runner.spec.ts", "line": 283, "column": 7}, {"title": "should run final comprehensive validation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 24, "parallelIndex": 4, "status": "passed", "duration": 10317, "errors": [], "stdout": [{"text": "🎯 Running final comprehensive validation...\n"}, {"text": "✅ Journey step: /brand-deep-dive completed successfully\n"}, {"text": "✅ Journey step: /marketing-dashboard completed successfully\n"}, {"text": "✅ Journey step: /executive-summary completed successfully\n"}, {"text": "✅ Journey step: /admin completed successfully\n"}, {"text": "🎉 Comprehensive test suite completed successfully!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T02:29:27.079Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "68b8c085ca31fe9a4ab1-b32ada70a3c078f61a08", "file": "comprehensive-test-runner.spec.ts", "line": 314, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-05-30T02:27:01.979Z", "duration": 155523.232, "expected": 102, "skipped": 0, "unexpected": 21, "flaky": 0}}