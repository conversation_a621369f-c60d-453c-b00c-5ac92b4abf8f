{"config": {"configFile": "/Users/<USER>/Projects/NOLK/nolk-v4/playwright.config.ts", "rootDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 5}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "tests/reports/e2e-html"}], ["json", {"outputFile": "tests/reports/e2e-results.json"}], ["junit", {"outputFile": "tests/reports/e2e-junit.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 8, "webServer": {"command": "npm run dev", "url": "http://localhost:6699", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "auth.spec.ts", "file": "auth.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Credentials Authentication", "file": "auth.spec.ts", "line": 171, "column": 6, "specs": [{"title": "should successfully sign in with farbour/admin credentials", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 3614, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('h1, h2, [data-testid=\"dashboard-title\"]') resolved to 2 elements:\n    1) <h1 class=\"text-2xl sm:text-3xl font-bold tracking-tight\">Dashboard</h1> aka getByRole('heading', { name: 'Dashboard' })\n    2) <h2 class=\"text-sm sm:text-lg text-muted-foreground mt-1\">Overview of your business performance</h2> aka getByRole('heading', { name: 'Overview of your business' })\n\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1, h2, [data-testid=\"dashboard-title\"]')\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('h1, h2, [data-testid=\"dashboard-title\"]') resolved to 2 elements:\n    1) <h1 class=\"text-2xl sm:text-3xl font-bold tracking-tight\">Dashboard</h1> aka getByRole('heading', { name: 'Dashboard' })\n    2) <h2 class=\"text-sm sm:text-lg text-muted-foreground mt-1\">Overview of your business performance</h2> aka getByRole('heading', { name: 'Overview of your business' })\n\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1, h2, [data-testid=\"dashboard-title\"]')\u001b[22m\n\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:190:36", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts", "column": 36, "line": 190}, "snippet": "\u001b[0m \u001b[90m 188 |\u001b[39m     \u001b[90m// Verify dashboard elements are present\u001b[39m\n \u001b[90m 189 |\u001b[39m     \u001b[36mconst\u001b[39m dashboardHeading \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, [data-testid=\"dashboard-title\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 190 |\u001b[39m     \u001b[36mawait\u001b[39m expect(dashboardHeading)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 191 |\u001b[39m\n \u001b[90m 192 |\u001b[39m     \u001b[90m// Take screenshot of successful login\u001b[39m\n \u001b[90m 193 |\u001b[39m     \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m'successful-admin-login'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts", "column": 36, "line": 190}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('h1, h2, [data-testid=\"dashboard-title\"]') resolved to 2 elements:\n    1) <h1 class=\"text-2xl sm:text-3xl font-bold tracking-tight\">Dashboard</h1> aka getByRole('heading', { name: 'Dashboard' })\n    2) <h2 class=\"text-sm sm:text-lg text-muted-foreground mt-1\">Overview of your business performance</h2> aka getByRole('heading', { name: 'Overview of your business' })\n\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1, h2, [data-testid=\"dashboard-title\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 188 |\u001b[39m     \u001b[90m// Verify dashboard elements are present\u001b[39m\n \u001b[90m 189 |\u001b[39m     \u001b[36mconst\u001b[39m dashboardHeading \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, [data-testid=\"dashboard-title\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 190 |\u001b[39m     \u001b[36mawait\u001b[39m expect(dashboardHeading)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 191 |\u001b[39m\n \u001b[90m 192 |\u001b[39m     \u001b[90m// Take screenshot of successful login\u001b[39m\n \u001b[90m 193 |\u001b[39m     \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m'successful-admin-login'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:190:36\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T01:04:38.138Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/auth-Credentials-Authentic-b9324-h-farbour-admin-credentials-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/auth-Credentials-Authentic-b9324-h-farbour-admin-credentials-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/auth-Credentials-Authentic-b9324-h-farbour-admin-credentials-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts", "column": 36, "line": 190}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-1c29bfa2048d143131c6", "file": "auth.spec.ts", "line": 178, "column": 7}, {"title": "should reject invalid credentials", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "timedOut", "duration": 30421, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts", "column": 16, "line": 209}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 207 |\u001b[39m\n \u001b[90m 208 |\u001b[39m     \u001b[90m// Fill in invalid credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 209 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'invalid'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 210 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'wrongpassword'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 211 |\u001b[39m\n \u001b[90m 212 |\u001b[39m     \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:209:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T01:04:38.141Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/auth-Credentials-Authentic-ab45c--reject-invalid-credentials-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/auth-Credentials-Authentic-ab45c--reject-invalid-credentials-chromium/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/auth-Credentials-Authentic-ab45c--reject-invalid-credentials-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-0ca8c16c75b15a344ea0", "file": "auth.spec.ts", "line": 196, "column": 7}, {"title": "should verify admin access after login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 5703, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T01:04:38.138Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d748ac400d08b85935ef-ed327a2e010f3525f988", "file": "auth.spec.ts", "line": 228, "column": 7}, {"title": "should maintain session across page navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 7144, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T01:04:38.138Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d748ac400d08b85935ef-ca85c00296c9cb6252e2", "file": "auth.spec.ts", "line": 243, "column": 7}, {"title": "should successfully sign out", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "timedOut", "duration": 30389, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 90}, "message": "Error: page.waitForURL: Test timeout of 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"/auth/signin\" until \"load\"\n============================================================\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:90\n\n\u001b[0m \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// Wait for redirect to sign-in page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 90 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'/auth/signin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 91 |\u001b[39m }\n \u001b[90m 92 |\u001b[39m\n \u001b[90m 93 |\u001b[39m \u001b[90m/**\u001b[39m\u001b[0m\n\u001b[2m    at signOut (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:90:14)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:269:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T01:04:38.135Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/auth-Credentials-Authentication-should-successfully-sign-out-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/auth-Credentials-Authentication-should-successfully-sign-out-chromium/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/auth-Credentials-Authentication-should-successfully-sign-out-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-10ba817caefcb4ad0080", "file": "auth.spec.ts", "line": 264, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-05-30T01:04:36.678Z", "duration": 32040.898999999998, "expected": 2, "skipped": 0, "unexpected": 3, "flaky": 0}}