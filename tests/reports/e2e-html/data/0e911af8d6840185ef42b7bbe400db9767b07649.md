# Test info

- Name: Comprehensive Application Test Suite >> should verify authentication system works correctly
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:119:7

# Error details

```
Error: locator.click: Test timeout of 60000ms exceeded.
Call log:
  - waiting for locator('button:has-text("Username/Password")')

    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:42:24)
    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:64:3)
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:142:5
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Dashboard":
      - /url: /dashboard
  - main:
    - heading "Dashboard" [level=1]
    - heading "Overview of your business performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Countries"
    - button "Sales Channels"
    - text: Master KPI Comparison Compare 'Gross Revenue' across selected brands using a line chart.
    - combobox "Select KPI": Gross Revenue
    - img: 2/28/2025 3/31/2025 4/30/2025 0 550K 1M 2M 2M
    - text: Brands Click to toggle. Sorted by latest KPI.
    - list:
      - button "Toggle visibility for Revant" [pressed]: Revant
      - button "Toggle visibility for Qalo" [pressed]: Qalo
      - button "Toggle visibility for Ergonofis" [pressed]: Ergonofis
      - button "Toggle visibility for Opposite Wall" [pressed]: Opposite Wall
      - button "Toggle visibility for Homesick" [pressed]: Homesick
      - button "Toggle visibility for Gravity" [pressed]: Gravity
      - button "Toggle visibility for Freakmount" [pressed]: Freakmount
      - button "Toggle visibility for MiHIGH" [pressed]: MiHIGH
      - button "Toggle visibility for Rose Boreal" [pressed]: Rose Boreal
      - button "Toggle visibility for Rachel" [pressed]: Rachel
      - button "Toggle visibility for Kana" [pressed]: Kana
      - button "Toggle visibility for Arctic Tumblers" [pressed]: Arctic Tumblers
      - button "Toggle visibility for Loctote" [pressed]: Loctote
      - button "Toggle visibility for Proper Pour" [pressed]: Proper Pour
      - button "Toggle visibility for Love Your Melon" [pressed]: Love Your Melon
      - button "Toggle visibility for Wolf & Grizzly" [pressed]: Wolf & Grizzly
      - button "Toggle visibility for Go Green" [pressed]: Go Green
      - button "Toggle visibility for Corretto" [pressed]: Corretto
      - button "Toggle visibility for Alex Bottle" [pressed]: Alex Bottle
    - heading "Key Performance Indicators" [level=3]
    - text: Revenue
    - button "KPI Definition"
    - text: $17,245,998.18
    - img: 2/28/2025 4/30/2025 0 2,000,000 4,000,000 6,000,000 8,000,000
    - text: Discount
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,916,224.18
    - img: 2/28/2025 4/30/2025 0 200,000 400,000 600,000 800,000
    - text: Refunds
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $614,536.13
    - img: 2/28/2025 4/30/2025 0 70,000 140,000 210,000 280,000
    - text: Net Revenue
    - button "KPI Definition"
    - text: $14,715,237.88
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
    - text: Landed Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,709,657.76
    - img: 2/28/2025 4/30/2025 0 300,000 600,000 900,000 1,200,000
    - text: Fulfillment Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,167,088.33
    - img: 2/28/2025 4/30/2025 0 250,000 500,000 750,000 1,000,000
    - text: Transaction Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,034,384.19
    - img: 2/28/2025 4/30/2025 0 95,000 190,000 285,000 380,000
    - text: Gross Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $9,520,150.47
    - img: 2/28/2025 4/30/2025 0 900,000 1,800,000 2,700,000 3,600,000
    - text: Adspend
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $3,424,904.20
    - img: 2/28/2025 4/30/2025 0 350,000 700,000 1,050,000 1,400,000
    - text: Contribution Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $12,945,054.68
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   1 | import { Page, expect } from '@playwright/test';
   2 |
   3 | /**
   4 |  * Helper functions for E2E tests
   5 |  */
   6 |
   7 | /**
   8 |  * Wait for the page to be fully loaded
   9 |  */
   10 | export async function waitForPageLoad(page: Page) {
   11 |   await page.waitForLoadState('networkidle');
   12 |   await page.waitForLoadState('domcontentloaded');
   13 | }
   14 |
   15 | /**
   16 |  * Sign in with Google (mock for testing)
   17 |  */
   18 | export async function signInWithGoogle(page: Page) {
   19 |   // Navigate to sign-in page
   20 |   await page.goto('/auth/signin');
   21 |   await waitForPageLoad(page);
   22 |
   23 |   // Click the Google sign-in button
   24 |   await page.click('button:has-text("Continue with Google")');
   25 |
   26 |   // In a real test environment, you would handle OAuth flow
   27 |   // For now, we'll assume the sign-in is successful and we're redirected
   28 |   await page.waitForURL('/dashboard');
   29 |   await waitForPageLoad(page);
   30 | }
   31 |
   32 | /**
   33 |  * Sign in with username and password credentials
   34 |  */
   35 | export async function signInWithCredentials(page: Page, username: string, password: string) {
   36 |   // Navigate to sign-in page
   37 |   await page.goto('/auth/signin');
   38 |   await waitForPageLoad(page);
   39 |
   40 |   // Switch to credentials login
   41 |   const credentialsTab = page.locator('button:has-text("Username/Password")');
>  42 |   await credentialsTab.click();
      |                        ^ Error: locator.click: Test timeout of 60000ms exceeded.
   43 |   await page.waitForTimeout(1000); // Wait for tab switch animation
   44 |
   45 |   // Wait for the form to be visible
   46 |   await page.waitForSelector('#username', { state: 'visible', timeout: 5000 });
   47 |
   48 |   // Fill in credentials using id selectors
   49 |   await page.fill('#username', username);
   50 |   await page.fill('#password', password);
   51 |
   52 |   // Click sign in button
   53 |   await page.click('button[type="submit"]:has-text("Sign In")');
   54 |
   55 |   // Wait for redirect to dashboard
   56 |   await page.waitForURL('/dashboard', { timeout: 15000 });
   57 |   await waitForPageLoad(page);
   58 | }
   59 |
   60 | /**
   61 |  * Sign in with the test admin user (farbour/admin)
   62 |  */
   63 | export async function signInAsAdmin(page: Page) {
   64 |   await signInWithCredentials(page, 'farbour', 'admin');
   65 | }
   66 |
   67 | /**
   68 |  * Sign out from the application
   69 |  */
   70 | export async function signOut(page: Page) {
   71 |   // Look for user menu or sign out button
   72 |   const userMenu = page.locator('[data-testid="user-menu"]').or(
   73 |     page.locator('button:has-text("Sign out")')
   74 |   );
   75 |
   76 |   if (await userMenu.isVisible()) {
   77 |     await userMenu.click();
   78 |
   79 |     // Look for sign out option
   80 |     const signOutButton = page.locator('button:has-text("Sign out")').or(
   81 |       page.locator('[data-testid="sign-out"]')
   82 |     );
   83 |
   84 |     if (await signOutButton.isVisible()) {
   85 |       await signOutButton.click();
   86 |     }
   87 |   }
   88 |
   89 |   // Wait for redirect to sign-in page
   90 |   await page.waitForURL('/auth/signin');
   91 | }
   92 |
   93 | /**
   94 |  * Navigate to a specific page and wait for it to load
   95 |  */
   96 | export async function navigateToPage(page: Page, path: string) {
   97 |   await page.goto(path);
   98 |   await waitForPageLoad(page);
   99 | }
  100 |
  101 | /**
  102 |  * Check if user is authenticated
  103 |  */
  104 | export async function isAuthenticated(page: Page): Promise<boolean> {
  105 |   try {
  106 |     // Check if we're on the dashboard or any authenticated page
  107 |     const currentUrl = page.url();
  108 |     return !currentUrl.includes('/auth/signin');
  109 |   } catch {
  110 |     return false;
  111 |   }
  112 | }
  113 |
  114 | /**
  115 |  * Wait for a specific element to be visible
  116 |  */
  117 | export async function waitForElement(page: Page, selector: string, timeout = 10000) {
  118 |   await page.waitForSelector(selector, { state: 'visible', timeout });
  119 | }
  120 |
  121 | /**
  122 |  * Take a screenshot with a descriptive name
  123 |  */
  124 | export async function takeScreenshot(page: Page, name: string) {
  125 |   await page.screenshot({
  126 |     path: `tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`,
  127 |     fullPage: true
  128 |   });
  129 | }
  130 |
  131 | /**
  132 |  * Check if the sidebar navigation is working
  133 |  */
  134 | export async function testSidebarNavigation(page: Page) {
  135 |   // Test main navigation items
  136 |   const navItems = [
  137 |     { text: 'Dashboard', url: '/dashboard' },
  138 |     { text: 'Brand Deep Dive', url: '/brand-deep-dive' },
  139 |     { text: 'Marketing Dashboard', url: '/marketing-dashboard' },
  140 |     { text: 'Executive Summary', url: '/executive-summary' },
  141 |     { text: 'Budget', url: '/budget' },
  142 |     { text: 'AI Assistant', url: '/ai-assistant' }
```