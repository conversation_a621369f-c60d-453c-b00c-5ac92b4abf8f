# Test info

- Name: Comprehensive Admin Dashboard Testing >> should be responsive across different screen sizes
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:285:7

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role="main"]') resolved to 2 elements:
    1) <main data-slot="sidebar-inset" class="bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2">…</main> aka getByText('Toggle SidebarDashboardAdminAdmin DashboardManage users, roles, and system')
    2) <main class="flex-1 overflow-auto">…</main> aka getByRole('main').filter({ hasText: 'Toggle' }).getByRole('main')

Call log:
  - expect.toBeVisible with timeout 15000ms
  - waiting for locator('main, [role="main"]')

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:300:57
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "Admin":
      - /url: /admin
      - img
      - text: Admin
- list:
  - listitem:
    - link "Back to Dashboard":
      - /url: /dashboard
      - img
      - text: Back to Dashboard
  - listitem:
    - link "Dashboard":
      - /url: /admin
      - img
      - text: Dashboard
  - listitem:
    - link "Users":
      - /url: /admin/users
      - img
      - text: Users
  - listitem:
    - link "Roles":
      - /url: /admin/roles
      - img
      - text: Roles
  - listitem:
    - link "Permissions":
      - /url: /admin/permissions
      - img
      - text: Permissions
  - listitem:
    - link "Groups":
      - /url: /admin/groups
      - img
      - text: Groups
  - listitem:
    - link "Brands":
      - /url: /admin/brands
      - img
      - text: Brands
  - listitem:
    - link "DB Structure":
      - /url: /admin/db-structure
      - img
      - text: DB Structure
  - listitem:
    - link "Backups":
      - /url: /admin/backups
      - img
      - text: Backups
- list:
  - listitem:
    - link "Main Dashboard":
      - /url: /dashboard
      - img
      - text: Main Dashboard
  - listitem:
    - link "Settings":
      - /url: /admin/settings
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Admin":
      - /url: /admin
  - main:
    - heading "Admin Dashboard" [level=1]
    - paragraph: Manage users, roles, and system settings
    - text: System Active
    - heading "Key Metrics" [level=2]
    - text: Live Data Total Users 14
    - paragraph: Active system users
    - text: Total Brands 23
    - paragraph: Managed brands
    - text: Total Roles 4
    - paragraph: Permission roles
    - text: Total Groups 4
    - paragraph: User groups
    - text: Total Permissions 2
    - paragraph: System permissions
    - heading "Recent Activity" [level=2]
    - text: Latest User Additions
    - paragraph: François Arbour
    - paragraph: <EMAIL>
    - paragraph: 5/13/2025
    - paragraph: Marin Savignac
    - paragraph: <EMAIL>
    - paragraph: 5/13/2025
    - paragraph: Win brand user
    - paragraph: <EMAIL>
    - paragraph: 5/13/2025
    - paragraph: Francois Arbour
    - paragraph: <EMAIL>
    - paragraph: 12/31/1969
    - paragraph: arenaud
    - paragraph: <EMAIL>
    - paragraph: 12/31/1969
    - text: Recent Login Activities
    - paragraph: No recent login activities
    - paragraph: Login tracking will appear here once implemented
    - heading "Management Sections" [level=2]
    - link "User Management Manage users, roles, and access permissions 14 users":
      - /url: /admin/users
      - text: User Management
      - paragraph: Manage users, roles, and access permissions
      - text: 14 users
    - link "Role Management Configure user roles and permission sets 4 roles":
      - /url: /admin/roles
      - text: Role Management
      - paragraph: Configure user roles and permission sets
      - text: 4 roles
    - link "Permission Management Define and manage application permissions 2 permissions":
      - /url: /admin/permissions
      - text: Permission Management
      - paragraph: Define and manage application permissions
      - text: 2 permissions
    - link "Group Management Organize users into logical groups 4 groups":
      - /url: /admin/groups
      - text: Group Management
      - paragraph: Organize users into logical groups
      - text: 4 groups
    - link "Brand Management Manage brand information and settings 23 brands":
      - /url: /admin/brands
      - text: Brand Management
      - paragraph: Manage brand information and settings
      - text: 23 brands
    - link "Database Structure View and manage database schema Schema info":
      - /url: /admin/db-structure
      - text: Database Structure
      - paragraph: View and manage database schema
      - text: Schema info
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
  200 |     expect(page.url()).toContain('/admin/backups');
  201 |     
  202 |     // Look for backup-related features
  203 |     const backupElements = page.locator('text=/backup|restore|download|create/i');
  204 |     const backupCount = await backupElements.count();
  205 |     console.log(`Found ${backupCount} backup-related elements`);
  206 |
  207 |     // Look for backup buttons
  208 |     const backupButtons = page.locator('button:has-text("Backup"), button:has-text("Create"), [data-testid*="backup"]');
  209 |     const buttonCount = await backupButtons.count();
  210 |     console.log(`Found ${buttonCount} backup buttons`);
  211 |
  212 |     await takeScreenshot(page, 'admin-backups-page');
  213 |   });
  214 |
  215 |   test('should navigate to and test DB Structure page', async ({ page }) => {
  216 |     await page.goto('/admin/db-structure');
  217 |     await waitForPageLoad(page);
  218 |     await waitForLoadingToComplete(page);
  219 |
  220 |     // Verify DB structure page loaded
  221 |     expect(page.url()).toContain('/admin/db-structure');
  222 |     
  223 |     // Look for database structure elements
  224 |     const dbElements = page.locator('text=/table|column|schema|database/i');
  225 |     const dbCount = await dbElements.count();
  226 |     console.log(`Found ${dbCount} database structure elements`);
  227 |
  228 |     await takeScreenshot(page, 'admin-db-structure-page');
  229 |   });
  230 |
  231 |   test('should test admin API endpoints', async ({ page }) => {
  232 |     await page.goto('/admin');
  233 |     await waitForPageLoad(page);
  234 |
  235 |     // Test various admin API endpoints
  236 |     const adminEndpoints = [
  237 |       '/api/admin/users',
  238 |       '/api/admin/roles',
  239 |       '/api/admin/permissions',
  240 |       '/api/admin/groups',
  241 |       '/api/admin/brands'
  242 |     ];
  243 |
  244 |     for (const endpoint of adminEndpoints) {
  245 |       try {
  246 |         const response = await testApiEndpoint(page, endpoint);
  247 |         console.log(`${endpoint}: ${response.status()}`);
  248 |       } catch (error) {
  249 |         console.log(`${endpoint}: Failed - ${error}`);
  250 |       }
  251 |     }
  252 |   });
  253 |
  254 |   test('should handle admin form submissions', async ({ page }) => {
  255 |     await page.goto('/admin/users');
  256 |     await waitForPageLoad(page);
  257 |
  258 |     // Look for add/create user form
  259 |     const addButton = page.locator('button:has-text("Add"), button:has-text("Create")');
  260 |     
  261 |     if (await addButton.isVisible()) {
  262 |       await addButton.click();
  263 |       await page.waitForTimeout(1000);
  264 |       
  265 |       // Look for form modal or page
  266 |       const form = page.locator('form, [data-testid*="form"]');
  267 |       
  268 |       if (await form.isVisible()) {
  269 |         // Test form fields (don't actually submit)
  270 |         const inputs = form.locator('input, select, textarea');
  271 |         const inputCount = await inputs.count();
  272 |         console.log(`Found form with ${inputCount} input fields`);
  273 |         
  274 |         await takeScreenshot(page, 'admin-user-form');
  275 |         
  276 |         // Close form/modal
  277 |         const cancelButton = page.locator('button:has-text("Cancel"), button:has-text("Close")');
  278 |         if (await cancelButton.isVisible()) {
  279 |           await cancelButton.click();
  280 |         }
  281 |       }
  282 |     }
  283 |   });
  284 |
  285 |   test('should be responsive across different screen sizes', async ({ page }) => {
  286 |     await page.goto('/admin');
  287 |     await waitForPageLoad(page);
  288 |
  289 |     const viewports = [
  290 |       { width: 1920, height: 1080, name: 'desktop' },
  291 |       { width: 1024, height: 768, name: 'tablet' },
  292 |       { width: 375, height: 667, name: 'mobile' }
  293 |     ];
  294 |
  295 |     for (const viewport of viewports) {
  296 |       await page.setViewportSize(viewport);
  297 |       await page.waitForTimeout(1000);
  298 |       
  299 |       // Verify main elements are still visible
> 300 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
      |                                                         ^ Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role="main"]') resolved to 2 elements:
  301 |       
  302 |       // Check admin navigation on different screens
  303 |       const adminNav = page.locator('nav');
  304 |       if (await adminNav.isVisible()) {
  305 |         console.log(`Admin navigation visible on ${viewport.name}`);
  306 |       }
  307 |       
  308 |       await takeScreenshot(page, `admin-dashboard-responsive-${viewport.name}`);
  309 |     }
  310 |   });
  311 |
  312 |   test('should have proper accessibility features', async ({ page }) => {
  313 |     await page.goto('/admin');
  314 |     await waitForPageLoad(page);
  315 |
  316 |     const accessibilityInfo = await testAccessibility(page);
  317 |     
  318 |     // Verify minimum accessibility requirements for admin interface
  319 |     expect(accessibilityInfo.headings).toBeGreaterThan(0);
  320 |     expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
  321 |     
  322 |     console.log('Admin Dashboard accessibility info:', accessibilityInfo);
  323 |     await takeScreenshot(page, 'admin-dashboard-accessibility');
  324 |   });
  325 |
  326 |   test('should load within performance thresholds', async ({ page }) => {
  327 |     const startTime = Date.now();
  328 |     
  329 |     await page.goto('/admin');
  330 |     await waitForPageLoad(page);
  331 |     await waitForLoadingToComplete(page);
  332 |     
  333 |     const loadTime = Date.now() - startTime;
  334 |     
  335 |     // Admin Dashboard should load within 10 seconds
  336 |     expect(loadTime).toBeLessThan(10000);
  337 |     
  338 |     console.log(`Admin Dashboard loaded in ${loadTime}ms`);
  339 |   });
  340 |
  341 |   test('should not have critical console errors', async ({ page }) => {
  342 |     const consoleErrors: string[] = [];
  343 |     
  344 |     page.on('console', (msg) => {
  345 |       if (msg.type() === 'error') {
  346 |         consoleErrors.push(msg.text());
  347 |       }
  348 |     });
  349 |
  350 |     await page.goto('/admin');
  351 |     await waitForPageLoad(page);
  352 |     await waitForLoadingToComplete(page);
  353 |
  354 |     // Filter out acceptable errors
  355 |     const criticalErrors = consoleErrors.filter(error =>
  356 |       !error.includes('favicon') &&
  357 |       !error.includes('404') &&
  358 |       !error.includes('net::ERR_FAILED') &&
  359 |       !error.includes('ChunkLoadError') &&
  360 |       !error.includes('ResizeObserver')
  361 |     );
  362 |
  363 |     if (criticalErrors.length > 0) {
  364 |       console.log('Critical console errors found:', criticalErrors);
  365 |     }
  366 |
  367 |     // For now, log errors but don't fail the test
  368 |     // expect(criticalErrors).toHaveLength(0);
  369 |   });
  370 | });
  371 |
```