# Test info

- Name: Comprehensive Error Handling and Edge Cases >> should handle page refresh and state preservation
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:207:7

# Error details

```
Error: locator.isVisible: Error: strict mode violation: locator('main') resolved to 2 elements:
    1) <main data-slot="sidebar-inset" class="bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2">…</main> aka getByText('Toggle SidebarDashboardDashboardDashboardOverview of your business')
    2) <main class="flex-1 overflow-auto">…</main> aka getByRole('main').filter({ hasText: 'Toggle SidebarDashboardDashboardDashboardOverview of your business' }).getByRole('main')

Call log:
    - checking visibility of locator('main')

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:224:49
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Dashboard":
      - /url: /dashboard
  - main:
    - heading "Dashboard" [level=1]
    - heading "Overview of your business performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Countries"
    - button "Sales Channels"
    - text: Master KPI Comparison Compare 'Gross Revenue' across selected brands using a line chart.
    - combobox "Select KPI": Gross Revenue
    - img: 2/28/2025 3/31/2025 4/30/2025 0 550K 1M 2M 2M
    - text: Brands Click to toggle. Sorted by latest KPI.
    - list:
      - button "Toggle visibility for Revant" [pressed]: Revant
      - button "Toggle visibility for Qalo" [pressed]: Qalo
      - button "Toggle visibility for Ergonofis" [pressed]: Ergonofis
      - button "Toggle visibility for Opposite Wall" [pressed]: Opposite Wall
      - button "Toggle visibility for Homesick" [pressed]: Homesick
      - button "Toggle visibility for Gravity" [pressed]: Gravity
      - button "Toggle visibility for Freakmount" [pressed]: Freakmount
      - button "Toggle visibility for MiHIGH" [pressed]: MiHIGH
      - button "Toggle visibility for Rose Boreal" [pressed]: Rose Boreal
      - button "Toggle visibility for Rachel" [pressed]: Rachel
      - button "Toggle visibility for Kana" [pressed]: Kana
      - button "Toggle visibility for Arctic Tumblers" [pressed]: Arctic Tumblers
      - button "Toggle visibility for Loctote" [pressed]: Loctote
      - button "Toggle visibility for Proper Pour" [pressed]: Proper Pour
      - button "Toggle visibility for Love Your Melon" [pressed]: Love Your Melon
      - button "Toggle visibility for Wolf & Grizzly" [pressed]: Wolf & Grizzly
      - button "Toggle visibility for Go Green" [pressed]: Go Green
      - button "Toggle visibility for Corretto" [pressed]: Corretto
      - button "Toggle visibility for Alex Bottle" [pressed]: Alex Bottle
    - heading "Key Performance Indicators" [level=3]
    - text: Revenue
    - button "KPI Definition"
    - text: $17,245,998.18
    - img: 2/28/2025 4/30/2025 0 2,000,000 4,000,000 6,000,000 8,000,000
    - text: Discount
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,916,224.18
    - img: 2/28/2025 4/30/2025 0 200,000 400,000 600,000 800,000
    - text: Refunds
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $614,536.13
    - img: 2/28/2025 4/30/2025 0 70,000 140,000 210,000 280,000
    - text: Net Revenue
    - button "KPI Definition"
    - text: $14,715,237.88
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
    - text: Landed Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,709,657.76
    - img: 2/28/2025 4/30/2025 0 300,000 600,000 900,000 1,200,000
    - text: Fulfillment Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,167,088.33
    - img: 2/28/2025 4/30/2025 0 250,000 500,000 750,000 1,000,000
    - text: Transaction Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,034,384.19
    - img: 2/28/2025 4/30/2025 0 95,000 190,000 285,000 380,000
    - text: Gross Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $9,520,150.47
    - img: 2/28/2025 4/30/2025 0 900,000 1,800,000 2,700,000 3,600,000
    - text: Adspend
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $3,424,904.20
    - img: 2/28/2025 4/30/2025 0 350,000 700,000 1,050,000 1,400,000
    - text: Contribution Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $12,945,054.68
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
  124 |     
  125 |     // Look for add user button
  126 |     const addButton = page.locator('button:has-text("Add"), button:has-text("Create")');
  127 |     
  128 |     if (await addButton.isVisible()) {
  129 |       await addButton.click();
  130 |       await page.waitForTimeout(1000);
  131 |       
  132 |       // Look for form
  133 |       const form = page.locator('form');
  134 |       
  135 |       if (await form.isVisible()) {
  136 |         // Try to submit empty form
  137 |         const submitButton = form.locator('button[type="submit"], button:has-text("Save"), button:has-text("Create")');
  138 |         
  139 |         if (await submitButton.isVisible()) {
  140 |           await submitButton.click();
  141 |           await page.waitForTimeout(1000);
  142 |           
  143 |           // Should show validation errors
  144 |           const validationErrors = page.locator('.error, .invalid, text=/required|invalid/i');
  145 |           const hasValidation = await validationErrors.count() > 0;
  146 |           
  147 |           console.log(`Form validation: ${hasValidation ? 'Working' : 'Not detected'}`);
  148 |           await takeScreenshot(page, 'form-validation-errors');
  149 |         }
  150 |       }
  151 |     }
  152 |   });
  153 |
  154 |   test('should handle large data sets without crashing', async ({ page }) => {
  155 |     // Test with potentially large datasets
  156 |     await page.goto('/marketing-dashboard');
  157 |     await waitForPageLoad(page);
  158 |     
  159 |     // Set a very large date range to potentially load lots of data
  160 |     const startDateInput = page.locator('input[type="date"]').first();
  161 |     const endDateInput = page.locator('input[type="date"]').last();
  162 |     
  163 |     if (await startDateInput.isVisible() && await endDateInput.isVisible()) {
  164 |       await startDateInput.fill('2020-01-01');
  165 |       await endDateInput.fill('2024-12-31');
  166 |       await page.waitForTimeout(5000);
  167 |       await waitForLoadingToComplete(page);
  168 |       
  169 |       // Page should still be responsive
  170 |       const isResponsive = await page.locator('main').isVisible();
  171 |       expect(isResponsive).toBe(true);
  172 |       
  173 |       console.log('Large dataset handling: Page remained responsive');
  174 |       await takeScreenshot(page, 'large-dataset-handling');
  175 |     }
  176 |   });
  177 |
  178 |   test('should handle browser back/forward navigation', async ({ page }) => {
  179 |     // Navigate through several pages
  180 |     await page.goto('/dashboard');
  181 |     await waitForPageLoad(page);
  182 |     
  183 |     await page.goto('/marketing-dashboard');
  184 |     await waitForPageLoad(page);
  185 |     
  186 |     await page.goto('/brand-deep-dive');
  187 |     await waitForPageLoad(page);
  188 |     
  189 |     // Test back navigation
  190 |     await page.goBack();
  191 |     await waitForPageLoad(page);
  192 |     expect(page.url()).toContain('/marketing-dashboard');
  193 |     
  194 |     await page.goBack();
  195 |     await waitForPageLoad(page);
  196 |     expect(page.url()).toContain('/dashboard');
  197 |     
  198 |     // Test forward navigation
  199 |     await page.goForward();
  200 |     await waitForPageLoad(page);
  201 |     expect(page.url()).toContain('/marketing-dashboard');
  202 |     
  203 |     console.log('Browser navigation: Working correctly');
  204 |     await takeScreenshot(page, 'browser-navigation');
  205 |   });
  206 |
  207 |   test('should handle page refresh and state preservation', async ({ page }) => {
  208 |     await page.goto('/dashboard');
  209 |     await waitForPageLoad(page);
  210 |     
  211 |     // Apply some filters if available
  212 |     const brandFilter = page.locator('select[name*="brand"]');
  213 |     if (await brandFilter.isVisible()) {
  214 |       await brandFilter.selectOption({ index: 1 });
  215 |       await page.waitForTimeout(2000);
  216 |     }
  217 |     
  218 |     // Refresh the page
  219 |     await page.reload();
  220 |     await waitForPageLoad(page);
  221 |     await waitForLoadingToComplete(page);
  222 |     
  223 |     // Page should load successfully after refresh
> 224 |     const isLoaded = await page.locator('main').isVisible();
      |                                                 ^ Error: locator.isVisible: Error: strict mode violation: locator('main') resolved to 2 elements:
  225 |     expect(isLoaded).toBe(true);
  226 |     
  227 |     console.log('Page refresh: Handled successfully');
  228 |     await takeScreenshot(page, 'page-refresh');
  229 |   });
  230 |
  231 |   test('should handle concurrent user actions', async ({ page }) => {
  232 |     await page.goto('/marketing-dashboard');
  233 |     await waitForPageLoad(page);
  234 |     await waitForLoadingToComplete(page);
  235 |     
  236 |     // Simulate rapid user interactions
  237 |     const actions = [
  238 |       () => page.locator('select[name*="currency"]').selectOption({ index: 1 }),
  239 |       () => page.locator('input[type="date"]').first().fill('2024-01-01'),
  240 |       () => page.locator('button').first().click(),
  241 |       () => page.keyboard.press('Tab'),
  242 |       () => page.mouse.move(100, 100)
  243 |     ];
  244 |     
  245 |     // Execute actions rapidly
  246 |     const promises = actions.map(action => action().catch(() => {}));
  247 |     await Promise.all(promises);
  248 |     
  249 |     await page.waitForTimeout(2000);
  250 |     await waitForLoadingToComplete(page);
  251 |     
  252 |     // Page should still be functional
  253 |     const isStillFunctional = await page.locator('main').isVisible();
  254 |     expect(isStillFunctional).toBe(true);
  255 |     
  256 |     console.log('Concurrent actions: Handled successfully');
  257 |     await takeScreenshot(page, 'concurrent-actions');
  258 |   });
  259 |
  260 |   test('should handle memory leaks during navigation', async ({ page }) => {
  261 |     const pages = ['/dashboard', '/marketing-dashboard', '/brand-deep-dive', '/executive-summary'];
  262 |     let initialMemory = 0;
  263 |     
  264 |     // Get initial memory usage
  265 |     const memory = await page.evaluate(() => {
  266 |       if ('memory' in performance) {
  267 |         return (performance as any).memory.usedJSHeapSize;
  268 |       }
  269 |       return 0;
  270 |     });
  271 |     
  272 |     initialMemory = memory;
  273 |     console.log(`Initial memory: ${(initialMemory / 1024 / 1024).toFixed(2)}MB`);
  274 |     
  275 |     // Navigate through pages multiple times
  276 |     for (let i = 0; i < 3; i++) {
  277 |       for (const url of pages) {
  278 |         await page.goto(url);
  279 |         await waitForPageLoad(page);
  280 |         await waitForLoadingToComplete(page);
  281 |         await page.waitForTimeout(1000);
  282 |       }
  283 |     }
  284 |     
  285 |     // Check final memory usage
  286 |     const finalMemory = await page.evaluate(() => {
  287 |       if ('memory' in performance) {
  288 |         return (performance as any).memory.usedJSHeapSize;
  289 |       }
  290 |       return 0;
  291 |     });
  292 |     
  293 |     const memoryIncrease = finalMemory - initialMemory;
  294 |     console.log(`Final memory: ${(finalMemory / 1024 / 1024).toFixed(2)}MB`);
  295 |     console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
  296 |     
  297 |     // Memory increase should be reasonable (less than 50MB)
  298 |     expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
  299 |   });
  300 |
  301 |   test('should handle edge cases in data filtering', async ({ page }) => {
  302 |     await page.goto('/dashboard');
  303 |     await waitForPageLoad(page);
  304 |     
  305 |     // Test edge cases in date filtering
  306 |     const dateInputs = page.locator('input[type="date"]');
  307 |     const dateCount = await dateInputs.count();
  308 |     
  309 |     if (dateCount >= 2) {
  310 |       // Test invalid date range (end before start)
  311 |       await dateInputs.first().fill('2024-12-31');
  312 |       await dateInputs.last().fill('2024-01-01');
  313 |       await page.waitForTimeout(2000);
  314 |       
  315 |       // Should handle invalid date range gracefully
  316 |       const errorMessage = page.locator('text=/invalid|error/i');
  317 |       const hasErrorHandling = await errorMessage.isVisible();
  318 |       
  319 |       console.log(`Invalid date range handling: ${hasErrorHandling ? 'Detected' : 'Handled silently'}`);
  320 |       await takeScreenshot(page, 'invalid-date-range');
  321 |       
  322 |       // Reset to valid range
  323 |       await dateInputs.first().fill('2024-01-01');
  324 |       await dateInputs.last().fill('2024-12-31');
```