# Test info

- Name: Comprehensive Error Handling and Edge Cases >> should handle authentication errors
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:92:7

# Error details

```
Error: expect(received).toContain(expected) // indexOf

Expected substring: "/auth/signin"
Received string:    "http://localhost:6699/dashboard"
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:112:26
```

# Page snapshot

```yaml
- alert
- button "Open Next.js Dev Tools":
  - img
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Dashboard":
      - /url: /dashboard
  - main:
    - heading "Dashboard" [level=1]
    - heading "Overview of your business performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Countries"
    - button "Sales Channels"
    - text: Master KPI Comparison Compare 'Gross Revenue' across selected brands using a line chart.
    - combobox "Select KPI": Gross Revenue
    - img: 2/28/2025 3/31/2025 4/30/2025 0 550K 1M 2M 2M
    - text: Brands Click to toggle. Sorted by latest KPI.
    - list:
      - button "Toggle visibility for Revant" [pressed]: Revant
      - button "Toggle visibility for Qalo" [pressed]: Qalo
      - button "Toggle visibility for Ergonofis" [pressed]: Ergonofis
      - button "Toggle visibility for Opposite Wall" [pressed]: Opposite Wall
      - button "Toggle visibility for Homesick" [pressed]: Homesick
      - button "Toggle visibility for Gravity" [pressed]: Gravity
      - button "Toggle visibility for Freakmount" [pressed]: Freakmount
      - button "Toggle visibility for MiHIGH" [pressed]: MiHIGH
      - button "Toggle visibility for Rose Boreal" [pressed]: Rose Boreal
      - button "Toggle visibility for Rachel" [pressed]: Rachel
      - button "Toggle visibility for Kana" [pressed]: Kana
      - button "Toggle visibility for Arctic Tumblers" [pressed]: Arctic Tumblers
      - button "Toggle visibility for Loctote" [pressed]: Loctote
      - button "Toggle visibility for Proper Pour" [pressed]: Proper Pour
      - button "Toggle visibility for Love Your Melon" [pressed]: Love Your Melon
      - button "Toggle visibility for Wolf & Grizzly" [pressed]: Wolf & Grizzly
      - button "Toggle visibility for Go Green" [pressed]: Go Green
      - button "Toggle visibility for Corretto" [pressed]: Corretto
      - button "Toggle visibility for Alex Bottle" [pressed]: Alex Bottle
    - heading "Key Performance Indicators" [level=3]
    - text: Revenue
    - button "KPI Definition"
    - text: $17,245,998.18
    - img: 2/28/2025 4/30/2025 0 2,000,000 4,000,000 6,000,000 8,000,000
    - text: Discount
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,916,224.18
    - img: 2/28/2025 4/30/2025 0 200,000 400,000 600,000 800,000
    - text: Refunds
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $614,536.13
    - img: 2/28/2025 4/30/2025 0 70,000 140,000 210,000 280,000
    - text: Net Revenue
    - button "KPI Definition"
    - text: $14,715,237.88
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
    - text: Landed Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,709,657.76
    - img: 2/28/2025 4/30/2025 0 300,000 600,000 900,000 1,200,000
    - text: Fulfillment Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,167,088.33
    - img: 2/28/2025 4/30/2025 0 250,000 500,000 750,000 1,000,000
    - text: Transaction Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,034,384.19
    - img: 2/28/2025 4/30/2025 0 95,000 190,000 285,000 380,000
    - text: Gross Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $9,520,150.47
    - img: 2/28/2025 4/30/2025 0 900,000 1,800,000 2,700,000 3,600,000
    - text: Adspend
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $3,424,904.20
    - img: 2/28/2025 4/30/2025 0 350,000 700,000 1,050,000 1,400,000
    - text: Contribution Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $12,945,054.68
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
```

# Test source

```ts
   12 |     await signInAsAdmin(page);
   13 |   });
   14 |
   15 |   test('should handle 404 errors gracefully', async ({ page }) => {
   16 |     const invalidUrls = [
   17 |       '/nonexistent-page',
   18 |       '/dashboard/invalid',
   19 |       '/admin/nonexistent',
   20 |       '/marketing-dashboard/invalid-campaign-id',
   21 |       '/brand-deep-dive/invalid'
   22 |     ];
   23 |
   24 |     for (const url of invalidUrls) {
   25 |       await page.goto(url);
   26 |       await waitForPageLoad(page);
   27 |       
   28 |       // Should either show 404 page or redirect to valid page
   29 |       const currentUrl = page.url();
   30 |       console.log(`${url} -> ${currentUrl}`);
   31 |       
   32 |       // Check for 404 page elements or redirect
   33 |       const is404 = currentUrl.includes('404') || 
   34 |                    await page.locator('text=/404|not found|page not found/i').isVisible();
   35 |       const isRedirect = !currentUrl.includes(url.split('/')[1]);
   36 |       
   37 |       expect(is404 || isRedirect).toBe(true);
   38 |       await takeScreenshot(page, `error-404-${url.replace(/\//g, '-')}`);
   39 |     }
   40 |   });
   41 |
   42 |   test('should handle network errors and offline scenarios', async ({ page }) => {
   43 |     await page.goto('/dashboard');
   44 |     await waitForPageLoad(page);
   45 |     
   46 |     // Simulate network failure
   47 |     await page.context().setOffline(true);
   48 |     
   49 |     // Try to navigate to another page
   50 |     await page.goto('/marketing-dashboard');
   51 |     await page.waitForTimeout(3000);
   52 |     
   53 |     // Should show offline message or handle gracefully
   54 |     const offlineIndicator = page.locator('text=/offline|network error|connection failed/i');
   55 |     const isOfflineHandled = await offlineIndicator.isVisible();
   56 |     
   57 |     console.log(`Offline handling: ${isOfflineHandled ? 'Detected' : 'Not detected'}`);
   58 |     await takeScreenshot(page, 'network-offline');
   59 |     
   60 |     // Restore network
   61 |     await page.context().setOffline(false);
   62 |     await page.waitForTimeout(2000);
   63 |     
   64 |     // Should recover when network is restored
   65 |     await page.reload();
   66 |     await waitForPageLoad(page);
   67 |     await takeScreenshot(page, 'network-restored');
   68 |   });
   69 |
   70 |   test('should handle API errors gracefully', async ({ page }) => {
   71 |     // Intercept API calls and return errors
   72 |     await page.route('**/api/dashboard/**', route => {
   73 |       route.fulfill({
   74 |         status: 500,
   75 |         contentType: 'application/json',
   76 |         body: JSON.stringify({ error: 'Internal Server Error' })
   77 |       });
   78 |     });
   79 |     
   80 |     await page.goto('/dashboard');
   81 |     await waitForPageLoad(page);
   82 |     await waitForLoadingToComplete(page);
   83 |     
   84 |     // Should show error message or fallback content
   85 |     const errorMessage = page.locator('text=/error|failed|something went wrong/i');
   86 |     const hasErrorHandling = await errorMessage.isVisible();
   87 |     
   88 |     console.log(`API error handling: ${hasErrorHandling ? 'Detected' : 'Not detected'}`);
   89 |     await takeScreenshot(page, 'api-error-500');
   90 |   });
   91 |
   92 |   test('should handle authentication errors', async ({ page }) => {
   93 |     // Test without authentication
   94 |     const unauthenticatedPage = await page.context().newPage();
   95 |     
   96 |     const protectedPages = [
   97 |       '/dashboard',
   98 |       '/marketing-dashboard',
   99 |       '/brand-deep-dive',
  100 |       '/executive-summary',
  101 |       '/budget',
  102 |       '/ai-assistant',
  103 |       '/admin'
  104 |     ];
  105 |     
  106 |     for (const url of protectedPages) {
  107 |       await unauthenticatedPage.goto(url);
  108 |       await waitForPageLoad(unauthenticatedPage);
  109 |       
  110 |       // Should redirect to sign-in page
  111 |       const currentUrl = unauthenticatedPage.url();
> 112 |       expect(currentUrl).toContain('/auth/signin');
      |                          ^ Error: expect(received).toContain(expected) // indexOf
  113 |       
  114 |       console.log(`${url} -> redirected to sign-in`);
  115 |     }
  116 |     
  117 |     await takeScreenshot(unauthenticatedPage, 'auth-redirect');
  118 |     await unauthenticatedPage.close();
  119 |   });
  120 |
  121 |   test('should handle form validation errors', async ({ page }) => {
  122 |     await page.goto('/admin/users');
  123 |     await waitForPageLoad(page);
  124 |     
  125 |     // Look for add user button
  126 |     const addButton = page.locator('button:has-text("Add"), button:has-text("Create")');
  127 |     
  128 |     if (await addButton.isVisible()) {
  129 |       await addButton.click();
  130 |       await page.waitForTimeout(1000);
  131 |       
  132 |       // Look for form
  133 |       const form = page.locator('form');
  134 |       
  135 |       if (await form.isVisible()) {
  136 |         // Try to submit empty form
  137 |         const submitButton = form.locator('button[type="submit"], button:has-text("Save"), button:has-text("Create")');
  138 |         
  139 |         if (await submitButton.isVisible()) {
  140 |           await submitButton.click();
  141 |           await page.waitForTimeout(1000);
  142 |           
  143 |           // Should show validation errors
  144 |           const validationErrors = page.locator('.error, .invalid, text=/required|invalid/i');
  145 |           const hasValidation = await validationErrors.count() > 0;
  146 |           
  147 |           console.log(`Form validation: ${hasValidation ? 'Working' : 'Not detected'}`);
  148 |           await takeScreenshot(page, 'form-validation-errors');
  149 |         }
  150 |       }
  151 |     }
  152 |   });
  153 |
  154 |   test('should handle large data sets without crashing', async ({ page }) => {
  155 |     // Test with potentially large datasets
  156 |     await page.goto('/marketing-dashboard');
  157 |     await waitForPageLoad(page);
  158 |     
  159 |     // Set a very large date range to potentially load lots of data
  160 |     const startDateInput = page.locator('input[type="date"]').first();
  161 |     const endDateInput = page.locator('input[type="date"]').last();
  162 |     
  163 |     if (await startDateInput.isVisible() && await endDateInput.isVisible()) {
  164 |       await startDateInput.fill('2020-01-01');
  165 |       await endDateInput.fill('2024-12-31');
  166 |       await page.waitForTimeout(5000);
  167 |       await waitForLoadingToComplete(page);
  168 |       
  169 |       // Page should still be responsive
  170 |       const isResponsive = await page.locator('main').isVisible();
  171 |       expect(isResponsive).toBe(true);
  172 |       
  173 |       console.log('Large dataset handling: Page remained responsive');
  174 |       await takeScreenshot(page, 'large-dataset-handling');
  175 |     }
  176 |   });
  177 |
  178 |   test('should handle browser back/forward navigation', async ({ page }) => {
  179 |     // Navigate through several pages
  180 |     await page.goto('/dashboard');
  181 |     await waitForPageLoad(page);
  182 |     
  183 |     await page.goto('/marketing-dashboard');
  184 |     await waitForPageLoad(page);
  185 |     
  186 |     await page.goto('/brand-deep-dive');
  187 |     await waitForPageLoad(page);
  188 |     
  189 |     // Test back navigation
  190 |     await page.goBack();
  191 |     await waitForPageLoad(page);
  192 |     expect(page.url()).toContain('/marketing-dashboard');
  193 |     
  194 |     await page.goBack();
  195 |     await waitForPageLoad(page);
  196 |     expect(page.url()).toContain('/dashboard');
  197 |     
  198 |     // Test forward navigation
  199 |     await page.goForward();
  200 |     await waitForPageLoad(page);
  201 |     expect(page.url()).toContain('/marketing-dashboard');
  202 |     
  203 |     console.log('Browser navigation: Working correctly');
  204 |     await takeScreenshot(page, 'browser-navigation');
  205 |   });
  206 |
  207 |   test('should handle page refresh and state preservation', async ({ page }) => {
  208 |     await page.goto('/dashboard');
  209 |     await waitForPageLoad(page);
  210 |     
  211 |     // Apply some filters if available
  212 |     const brandFilter = page.locator('select[name*="brand"]');
```