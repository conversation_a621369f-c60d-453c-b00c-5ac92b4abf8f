# Test info

- Name: Comprehensive AI Assistant Testing >> should have proper accessibility features
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:330:7

# Error details

```
Error: expect(received).toBe<PERSON><PERSON><PERSON><PERSON><PERSON>(expected)

Expected: > 0
Received:   0
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:338:41
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Ai Assistant":
      - /url: /ai-assistant
  - main:
    - heading "AI Assistant" [level=1]
    - heading "Get instant answers and insights from your data" [level=2]
    - text: AI Assistant Get instant answers and insights from your data
    - paragraph: Ask questions about your business data, generate reports, or get recommendations based on your e-commerce performance.
    - text: Chat with NOLK AI Your AI-powered business assistant
    - paragraph: Hello! I'm your NOLK AI assistant. How can I help you today? You can ask me about your business data, such as 'Show me the gross revenue for the last 30 days' or 'Compare net revenue across brands for the last quarter'.
    - textbox "Ask a question..."
    - button "Send" [disabled]
    - button "Show gross revenue"
    - button "Compare brands"
    - button "Margin trends"
    - button "Adspend analysis"
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
  238 |       'button:has-text("Export"), [data-testid*="export"]', // Export conversation
  239 |       '.suggestions, [data-testid*="suggestion"]', // Suggested questions
  240 |       '.templates, [data-testid*="template"]' // Message templates
  241 |     ];
  242 |
  243 |     let foundFeatures = 0;
  244 |     for (const selector of aiFeatures) {
  245 |       const element = page.locator(selector);
  246 |       if (await element.isVisible()) {
  247 |         foundFeatures++;
  248 |       }
  249 |     }
  250 |
  251 |     console.log(`Found ${foundFeatures} AI assistant features`);
  252 |     
  253 |     // Test clear chat if available
  254 |     const clearButton = page.locator('button:has-text("Clear"), [data-testid*="clear"]');
  255 |     if (await clearButton.isVisible()) {
  256 |       await clearButton.click();
  257 |       await page.waitForTimeout(1000);
  258 |       await takeScreenshot(page, 'ai-assistant-cleared');
  259 |     }
  260 |   });
  261 |
  262 |   test('should test AI assistant API endpoint', async ({ page }) => {
  263 |     await page.goto('/ai-assistant');
  264 |     await waitForPageLoad(page);
  265 |
  266 |     // Test AI assistant API
  267 |     try {
  268 |       const response = await testApiEndpoint(page, '/api/ai-assistant');
  269 |       console.log(`AI Assistant API response: ${response.status()}`);
  270 |     } catch (error) {
  271 |       console.log(`AI Assistant API failed: ${error}`);
  272 |     }
  273 |   });
  274 |
  275 |   test('should handle suggested questions or templates', async ({ page }) => {
  276 |     await page.goto('/ai-assistant');
  277 |     await waitForPageLoad(page);
  278 |     await waitForLoadingToComplete(page);
  279 |
  280 |     // Look for suggested questions
  281 |     const suggestions = page.locator('.suggestion, [data-testid*="suggestion"], button:has-text("?")');
  282 |     const suggestionCount = await suggestions.count();
  283 |     
  284 |     if (suggestionCount > 0) {
  285 |       console.log(`Found ${suggestionCount} suggested questions`);
  286 |       
  287 |       // Click on first suggestion
  288 |       await suggestions.first().click();
  289 |       await page.waitForTimeout(1000);
  290 |       
  291 |       // Check if message input was populated
  292 |       const messageInput = page.locator('textarea, input[type="text"]');
  293 |       if (await messageInput.isVisible()) {
  294 |         const inputValue = await messageInput.inputValue();
  295 |         if (inputValue.length > 0) {
  296 |           console.log(`Suggestion populated input: ${inputValue}`);
  297 |         }
  298 |       }
  299 |       
  300 |       await takeScreenshot(page, 'ai-assistant-suggestion-used');
  301 |     }
  302 |   });
  303 |
  304 |   test('should be responsive on different screen sizes', async ({ page }) => {
  305 |     await page.goto('/ai-assistant');
  306 |     await waitForPageLoad(page);
  307 |
  308 |     const viewports = [
  309 |       { width: 1920, height: 1080, name: 'desktop' },
  310 |       { width: 1024, height: 768, name: 'tablet' },
  311 |       { width: 375, height: 667, name: 'mobile' }
  312 |     ];
  313 |
  314 |     for (const viewport of viewports) {
  315 |       await page.setViewportSize(viewport);
  316 |       await page.waitForTimeout(1000);
  317 |       
  318 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
  319 |       
  320 |       // Check chat interface on different screens
  321 |       const chatContainer = page.locator('.chat, [data-testid*="chat"]');
  322 |       if (await chatContainer.isVisible()) {
  323 |         console.log(`Chat interface visible on ${viewport.name}`);
  324 |       }
  325 |       
  326 |       await takeScreenshot(page, `ai-assistant-responsive-${viewport.name}`);
  327 |     }
  328 |   });
  329 |
  330 |   test('should have proper accessibility features', async ({ page }) => {
  331 |     await page.goto('/ai-assistant');
  332 |     await waitForPageLoad(page);
  333 |
  334 |     const accessibilityInfo = await testAccessibility(page);
  335 |     
  336 |     // Verify minimum accessibility requirements
  337 |     expect(accessibilityInfo.headings).toBeGreaterThan(0);
> 338 |     expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
      |                                         ^ Error: expect(received).toBeGreaterThan(expected)
  339 |     
  340 |     console.log('AI Assistant accessibility info:', accessibilityInfo);
  341 |     await takeScreenshot(page, 'ai-assistant-accessibility');
  342 |   });
  343 |
  344 |   test('should load within performance thresholds', async ({ page }) => {
  345 |     const startTime = Date.now();
  346 |     
  347 |     await page.goto('/ai-assistant');
  348 |     await waitForPageLoad(page);
  349 |     await waitForLoadingToComplete(page);
  350 |     
  351 |     const loadTime = Date.now() - startTime;
  352 |     
  353 |     // AI Assistant should load within 8 seconds
  354 |     expect(loadTime).toBeLessThan(8000);
  355 |     
  356 |     console.log(`AI Assistant loaded in ${loadTime}ms`);
  357 |   });
  358 | });
  359 |
```