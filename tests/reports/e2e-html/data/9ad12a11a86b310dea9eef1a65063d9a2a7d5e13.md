# Test info

- Name: Comprehensive Error Handling and Edge Cases >> should handle authentication errors
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:98:7

# Error details

```
Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:122:43
```

# Page snapshot

```yaml
- alert
- button "Open Next.js Dev Tools":
  - img
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Dashboard":
      - /url: /dashboard
  - main:
    - heading "Dashboard" [level=1]
    - heading "Overview of your business performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Countries"
    - button "Sales Channels"
    - text: Master KPI Comparison Compare 'Gross Revenue' across selected brands using a line chart.
    - combobox "Select KPI": Gross Revenue
    - img: 2/28/2025 3/31/2025 4/30/2025 0 550K 1M 2M 2M
    - text: Brands Click to toggle. Sorted by latest KPI.
    - list:
      - button "Toggle visibility for Revant" [pressed]: Revant
      - button "Toggle visibility for Qalo" [pressed]: Qalo
      - button "Toggle visibility for Ergonofis" [pressed]: Ergonofis
      - button "Toggle visibility for Opposite Wall" [pressed]: Opposite Wall
      - button "Toggle visibility for Homesick" [pressed]: Homesick
      - button "Toggle visibility for Gravity" [pressed]: Gravity
      - button "Toggle visibility for Freakmount" [pressed]: Freakmount
      - button "Toggle visibility for MiHIGH" [pressed]: MiHIGH
      - button "Toggle visibility for Rose Boreal" [pressed]: Rose Boreal
      - button "Toggle visibility for Rachel" [pressed]: Rachel
      - button "Toggle visibility for Kana" [pressed]: Kana
      - button "Toggle visibility for Arctic Tumblers" [pressed]: Arctic Tumblers
      - button "Toggle visibility for Loctote" [pressed]: Loctote
      - button "Toggle visibility for Proper Pour" [pressed]: Proper Pour
      - button "Toggle visibility for Love Your Melon" [pressed]: Love Your Melon
      - button "Toggle visibility for Wolf & Grizzly" [pressed]: Wolf & Grizzly
      - button "Toggle visibility for Go Green" [pressed]: Go Green
      - button "Toggle visibility for Corretto" [pressed]: Corretto
      - button "Toggle visibility for Alex Bottle" [pressed]: Alex Bottle
    - heading "Key Performance Indicators" [level=3]
    - text: Revenue
    - button "KPI Definition"
    - text: $17,245,998.18
    - img: 2/28/2025 4/30/2025 0 2,000,000 4,000,000 6,000,000 8,000,000
    - text: Discount
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,916,224.18
    - img: 2/28/2025 4/30/2025 0 200,000 400,000 600,000 800,000
    - text: Refunds
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $614,536.13
    - img: 2/28/2025 4/30/2025 0 70,000 140,000 210,000 280,000
    - text: Net Revenue
    - button "KPI Definition"
    - text: $14,715,237.88
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
    - text: Landed Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,709,657.76
    - img: 2/28/2025 4/30/2025 0 300,000 600,000 900,000 1,200,000
    - text: Fulfillment Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,167,088.33
    - img: 2/28/2025 4/30/2025 0 250,000 500,000 750,000 1,000,000
    - text: Transaction Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,034,384.19
    - img: 2/28/2025 4/30/2025 0 95,000 190,000 285,000 380,000
    - text: Gross Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $9,520,150.47
    - img: 2/28/2025 4/30/2025 0 900,000 1,800,000 2,700,000 3,600,000
    - text: Adspend
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $3,424,904.20
    - img: 2/28/2025 4/30/2025 0 350,000 700,000 1,050,000 1,400,000
    - text: Contribution Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $12,945,054.68
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
```

# Test source

```ts
   22 |     ];
   23 |
   24 |     for (const url of invalidUrls) {
   25 |       await page.goto(url);
   26 |       await waitForPageLoad(page);
   27 |
   28 |       // Should either show 404 page or redirect to valid page
   29 |       const currentUrl = page.url();
   30 |       console.log(`${url} -> ${currentUrl}`);
   31 |
   32 |       // Check for 404 page elements or redirect
   33 |       const is404 = currentUrl.includes('404') ||
   34 |                    await page.locator('text=/404|not found|page not found/i').isVisible();
   35 |       const isRedirect = !currentUrl.includes(url.split('/')[1]);
   36 |       const isValidPage = currentUrl.includes('/dashboard') || currentUrl.includes('/auth');
   37 |
   38 |       // Either shows 404, redirects, or shows a valid page (some routes might be valid)
   39 |       expect(is404 || isRedirect || isValidPage).toBe(true);
   40 |       await takeScreenshot(page, `error-404-${url.replace(/\//g, '-')}`);
   41 |     }
   42 |   });
   43 |
   44 |   test('should handle network errors and offline scenarios', async ({ page }) => {
   45 |     await page.goto('/dashboard');
   46 |     await waitForPageLoad(page);
   47 |
   48 |     // Simulate network failure
   49 |     await page.context().setOffline(true);
   50 |
   51 |     // Try to navigate to another page
   52 |     try {
   53 |       await page.goto('/marketing-dashboard');
   54 |       await page.waitForTimeout(3000);
   55 |     } catch (error) {
   56 |       console.log('Expected network error:', error.message);
   57 |     }
   58 |
   59 |     // Should show offline message or handle gracefully
   60 |     const offlineIndicator = page.locator('text=/offline|network error|connection failed/i');
   61 |     const isOfflineHandled = await offlineIndicator.isVisible();
   62 |
   63 |     console.log(`Offline handling: ${isOfflineHandled ? 'Detected' : 'Not detected'}`);
   64 |     await takeScreenshot(page, 'network-offline');
   65 |
   66 |     // Restore network
   67 |     await page.context().setOffline(false);
   68 |     await page.waitForTimeout(2000);
   69 |
   70 |     // Should recover when network is restored
   71 |     await page.reload();
   72 |     await waitForPageLoad(page);
   73 |     await takeScreenshot(page, 'network-restored');
   74 |   });
   75 |
   76 |   test('should handle API errors gracefully', async ({ page }) => {
   77 |     // Intercept API calls and return errors
   78 |     await page.route('**/api/dashboard/**', route => {
   79 |       route.fulfill({
   80 |         status: 500,
   81 |         contentType: 'application/json',
   82 |         body: JSON.stringify({ error: 'Internal Server Error' })
   83 |       });
   84 |     });
   85 |
   86 |     await page.goto('/dashboard');
   87 |     await waitForPageLoad(page);
   88 |     await waitForLoadingToComplete(page);
   89 |
   90 |     // Should show error message or fallback content
   91 |     const errorMessage = page.locator('text=/error|failed|something went wrong/i');
   92 |     const hasErrorHandling = await errorMessage.isVisible();
   93 |
   94 |     console.log(`API error handling: ${hasErrorHandling ? 'Detected' : 'Not detected'}`);
   95 |     await takeScreenshot(page, 'api-error-500');
   96 |   });
   97 |
   98 |   test('should handle authentication errors', async ({ page }) => {
   99 |     // Test without authentication
  100 |     const unauthenticatedPage = await page.context().newPage();
  101 |
  102 |     const protectedPages = [
  103 |       '/dashboard',
  104 |       '/marketing-dashboard',
  105 |       '/brand-deep-dive',
  106 |       '/executive-summary',
  107 |       '/budget',
  108 |       '/ai-assistant',
  109 |       '/admin'
  110 |     ];
  111 |
  112 |     for (const url of protectedPages) {
  113 |       await unauthenticatedPage.goto(url);
  114 |       await waitForPageLoad(unauthenticatedPage);
  115 |
  116 |       // Should redirect to sign-in page or stay on dashboard if already authenticated
  117 |       const currentUrl = unauthenticatedPage.url();
  118 |       const isOnSignIn = currentUrl.includes('/auth/signin');
  119 |       const isOnDashboard = currentUrl.includes('/dashboard');
  120 |
  121 |       // Either redirected to sign-in or stayed on dashboard (if session is still valid)
> 122 |       expect(isOnSignIn || isOnDashboard).toBe(true);
      |                                           ^ Error: expect(received).toBe(expected) // Object.is equality
  123 |
  124 |       console.log(`${url} -> redirected to sign-in`);
  125 |     }
  126 |
  127 |     await takeScreenshot(unauthenticatedPage, 'auth-redirect');
  128 |     await unauthenticatedPage.close();
  129 |   });
  130 |
  131 |   test('should handle form validation errors', async ({ page }) => {
  132 |     await page.goto('/admin/users');
  133 |     await waitForPageLoad(page);
  134 |
  135 |     // Look for add user button
  136 |     const addButton = page.locator('button:has-text("Add"), button:has-text("Create")');
  137 |
  138 |     if (await addButton.isVisible()) {
  139 |       await addButton.click();
  140 |       await page.waitForTimeout(1000);
  141 |
  142 |       // Look for form
  143 |       const form = page.locator('form');
  144 |
  145 |       if (await form.isVisible()) {
  146 |         // Try to submit empty form
  147 |         const submitButton = form.locator('button[type="submit"], button:has-text("Save"), button:has-text("Create")');
  148 |
  149 |         if (await submitButton.isVisible()) {
  150 |           await submitButton.click();
  151 |           await page.waitForTimeout(1000);
  152 |
  153 |           // Should show validation errors
  154 |           const validationErrors = page.locator('.error, .invalid, text=/required|invalid/i');
  155 |           const hasValidation = await validationErrors.count() > 0;
  156 |
  157 |           console.log(`Form validation: ${hasValidation ? 'Working' : 'Not detected'}`);
  158 |           await takeScreenshot(page, 'form-validation-errors');
  159 |         }
  160 |       }
  161 |     }
  162 |   });
  163 |
  164 |   test('should handle large data sets without crashing', async ({ page }) => {
  165 |     // Test with potentially large datasets
  166 |     await page.goto('/marketing-dashboard');
  167 |     await waitForPageLoad(page);
  168 |
  169 |     // Set a very large date range to potentially load lots of data
  170 |     const startDateInput = page.locator('input[type="date"]').first();
  171 |     const endDateInput = page.locator('input[type="date"]').last();
  172 |
  173 |     if (await startDateInput.isVisible() && await endDateInput.isVisible()) {
  174 |       await startDateInput.fill('2020-01-01');
  175 |       await endDateInput.fill('2024-12-31');
  176 |       await page.waitForTimeout(5000);
  177 |       await waitForLoadingToComplete(page);
  178 |
  179 |       // Page should still be responsive
  180 |       const mainContent = await getMainContent(page);
  181 |       const isResponsive = await mainContent.isVisible();
  182 |       expect(isResponsive).toBe(true);
  183 |
  184 |       console.log('Large dataset handling: Page remained responsive');
  185 |       await takeScreenshot(page, 'large-dataset-handling');
  186 |     }
  187 |   });
  188 |
  189 |   test('should handle browser back/forward navigation', async ({ page }) => {
  190 |     // Navigate through several pages
  191 |     await page.goto('/dashboard');
  192 |     await waitForPageLoad(page);
  193 |
  194 |     await page.goto('/marketing-dashboard');
  195 |     await waitForPageLoad(page);
  196 |
  197 |     await page.goto('/brand-deep-dive');
  198 |     await waitForPageLoad(page);
  199 |
  200 |     // Test back navigation
  201 |     await page.goBack();
  202 |     await waitForPageLoad(page);
  203 |     expect(page.url()).toContain('/marketing-dashboard');
  204 |
  205 |     await page.goBack();
  206 |     await waitForPageLoad(page);
  207 |     expect(page.url()).toContain('/dashboard');
  208 |
  209 |     // Test forward navigation
  210 |     await page.goForward();
  211 |     await waitForPageLoad(page);
  212 |     expect(page.url()).toContain('/marketing-dashboard');
  213 |
  214 |     console.log('Browser navigation: Working correctly');
  215 |     await takeScreenshot(page, 'browser-navigation');
  216 |   });
  217 |
  218 |   test('should handle page refresh and state preservation', async ({ page }) => {
  219 |     await page.goto('/dashboard');
  220 |     await waitForPageLoad(page);
  221 |
  222 |     // Apply some filters if available
```