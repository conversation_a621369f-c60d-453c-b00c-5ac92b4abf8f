# Test info

- Name: Comprehensive Brand Deep Dive Testing >> should display marketing campaigns for selected brand
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:115:7

# Error details

```
Error: locator.isVisible: Unexpected token "=" while parsing css selector "[data-testid*="campaign"], .campaign, text=/campaign/i". Did you mean to CSS.escape it?
Call log:
    - checking visibility of [data-testid*="campaign"], .campaign, text=/campaign/i >> nth=0

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:123:40
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
  - main:
    - heading "Brand Deep Dive" [level=1]
    - heading "Detailed analysis of brand performance metrics" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Week
    - text: Data Filters
    - button "Brand"
    - button "Countries"
    - button "Sales Channels"
    - text: 📊
    - heading "No Brand Selected" [level=2]
    - paragraph: Please select a brand from the dropdown above to view detailed performance metrics and analysis.
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   23 |     await waitForLoadingToComplete(page);
   24 |
   25 |     // Verify page loaded (skip title check as it may be generic)
   26 |     console.log(`Page title: ${await page.title()}`);
   27 |
   28 |     // Check for main components
   29 |     const expectedElements = [
   30 |       'main, [role="main"]',
   31 |       'nav, [role="navigation"]',
   32 |       'h1, h2, [data-testid*="title"]'
   33 |     ];
   34 |
   35 |     await verifyPageElements(page, expectedElements);
   36 |     await takeScreenshot(page, 'brand-deep-dive-main');
   37 |   });
   38 |
   39 |   test('should display brand selector and allow brand selection', async ({ page }) => {
   40 |     await page.goto('/brand-deep-dive');
   41 |     await waitForPageLoad(page);
   42 |     await waitForLoadingToComplete(page);
   43 |
   44 |     // Look for brand selector
   45 |     const brandSelector = page.locator('[data-testid*="brand-selector"], select[name*="brand"], .brand-selector');
   46 |
   47 |     if (await brandSelector.isVisible()) {
   48 |       await brandSelector.click();
   49 |       await page.waitForTimeout(1000);
   50 |
   51 |       // Check for brand options
   52 |       const brandOptions = page.locator('option, [role="option"], [data-testid*="brand-option"]');
   53 |       const optionCount = await brandOptions.count();
   54 |
   55 |       if (optionCount > 1) {
   56 |         await brandOptions.nth(1).click();
   57 |         await page.waitForTimeout(2000);
   58 |         await waitForLoadingToComplete(page);
   59 |
   60 |         console.log(`Selected brand from ${optionCount} available options`);
   61 |         await takeScreenshot(page, 'brand-deep-dive-brand-selected');
   62 |       }
   63 |     }
   64 |   });
   65 |
   66 |   test('should display KPI cards specific to selected brand', async ({ page }) => {
   67 |     await page.goto('/brand-deep-dive');
   68 |     await waitForPageLoad(page);
   69 |     await waitForLoadingToComplete(page);
   70 |
   71 |     // Look for brand-specific KPI cards
   72 |     const kpiCards = page.locator('[data-testid*="kpi"], .kpi-card, [class*="kpi"]');
   73 |     const cardCount = await kpiCards.count();
   74 |
   75 |     if (cardCount > 0) {
   76 |       // Verify KPI cards are visible
   77 |       await expect(kpiCards.first()).toBeVisible();
   78 |
   79 |       // Check for brand-specific metrics
   80 |       const brandMetrics = page.locator('text=/revenue|margin|sales|units/i');
   81 |       expect(await brandMetrics.count()).toBeGreaterThan(0);
   82 |
   83 |       console.log(`Found ${cardCount} brand KPI cards`);
   84 |     }
   85 |
   86 |     await takeScreenshot(page, 'brand-deep-dive-kpi-cards');
   87 |   });
   88 |
   89 |   test('should render brand performance charts', async ({ page }) => {
   90 |     await page.goto('/brand-deep-dive');
   91 |     await waitForPageLoad(page);
   92 |     await waitForLoadingToComplete(page);
   93 |
   94 |     // Test chart rendering for brand data
   95 |     const charts = await testChartRendering(page);
   96 |
   97 |     // Look for specific chart types
   98 |     const chartTypes = [
   99 |       'svg', // Recharts
  100 |       'canvas', // Chart.js
  101 |       '.recharts-wrapper',
  102 |       '[data-testid*="chart"]'
  103 |     ];
  104 |
  105 |     let chartCount = 0;
  106 |     for (const chartType of chartTypes) {
  107 |       const elements = page.locator(chartType);
  108 |       chartCount += await elements.count();
  109 |     }
  110 |
  111 |     console.log(`Found ${chartCount} charts on brand deep dive page`);
  112 |     await takeScreenshot(page, 'brand-deep-dive-charts');
  113 |   });
  114 |
  115 |   test('should display marketing campaigns for selected brand', async ({ page }) => {
  116 |     await page.goto('/brand-deep-dive');
  117 |     await waitForPageLoad(page);
  118 |     await waitForLoadingToComplete(page);
  119 |
  120 |     // Look for marketing campaigns section
  121 |     const campaignsSection = page.locator('[data-testid*="campaign"], .campaign, text=/campaign/i');
  122 |
> 123 |     if (await campaignsSection.first().isVisible()) {
      |                                        ^ Error: locator.isVisible: Unexpected token "=" while parsing css selector "[data-testid*="campaign"], .campaign, text=/campaign/i". Did you mean to CSS.escape it?
  124 |       // Check for campaign data
  125 |       const campaignItems = page.locator('[data-testid*="campaign-item"], .campaign-item, tr');
  126 |       const campaignCount = await campaignItems.count();
  127 |
  128 |       console.log(`Found ${campaignCount} campaign items`);
  129 |
  130 |       if (campaignCount > 0) {
  131 |         // Test clicking on a campaign if clickable
  132 |         const firstCampaign = campaignItems.first();
  133 |         if (await firstCampaign.isVisible()) {
  134 |           await takeScreenshot(page, 'brand-deep-dive-campaigns');
  135 |         }
  136 |       }
  137 |     }
  138 |   });
  139 |
  140 |   test('should have functional brand-specific filters', async ({ page }) => {
  141 |     await page.goto('/brand-deep-dive');
  142 |     await waitForPageLoad(page);
  143 |     await waitForLoadingToComplete(page);
  144 |
  145 |     // Test filter functionality
  146 |     const activeFilters = await testFilters(page);
  147 |     console.log(`Found ${activeFilters.length} active filters`);
  148 |
  149 |     // Test date range filter
  150 |     const dateInputs = page.locator('input[type="date"]');
  151 |     const dateCount = await dateInputs.count();
  152 |
  153 |     if (dateCount > 0) {
  154 |       await dateInputs.first().fill('2024-01-01');
  155 |       if (dateCount > 1) {
  156 |         await dateInputs.nth(1).fill('2024-12-31');
  157 |       }
  158 |       await page.waitForTimeout(2000);
  159 |       await waitForLoadingToComplete(page);
  160 |       await takeScreenshot(page, 'brand-deep-dive-date-filter');
  161 |     }
  162 |
  163 |     // Test currency filter if present
  164 |     const currencyFilter = page.locator('select[name*="currency"], [data-testid*="currency"]');
  165 |     if (await currencyFilter.isVisible()) {
  166 |       await currencyFilter.click();
  167 |       await page.waitForTimeout(500);
  168 |
  169 |       const currencyOptions = page.locator('option, [role="option"]');
  170 |       const optionCount = await currencyOptions.count();
  171 |
  172 |       if (optionCount > 1) {
  173 |         await currencyOptions.nth(1).click();
  174 |         await page.waitForTimeout(2000);
  175 |         await waitForLoadingToComplete(page);
  176 |         await takeScreenshot(page, 'brand-deep-dive-currency-filter');
  177 |       }
  178 |     }
  179 |   });
  180 |
  181 |   test('should display tabs and allow tab navigation', async ({ page }) => {
  182 |     await page.goto('/brand-deep-dive');
  183 |     await waitForPageLoad(page);
  184 |     await waitForLoadingToComplete(page);
  185 |
  186 |     // Look for tab navigation
  187 |     const tabs = page.locator('[role="tab"], .tab, [data-testid*="tab"]');
  188 |     const tabCount = await tabs.count();
  189 |
  190 |     if (tabCount > 1) {
  191 |       console.log(`Found ${tabCount} tabs`);
  192 |
  193 |       // Test clicking through tabs
  194 |       for (let i = 0; i < Math.min(tabCount, 3); i++) {
  195 |         const tab = tabs.nth(i);
  196 |         if (await tab.isVisible()) {
  197 |           await tab.click();
  198 |           await page.waitForTimeout(1000);
  199 |           await waitForLoadingToComplete(page);
  200 |
  201 |           const tabText = await tab.textContent();
  202 |           await takeScreenshot(page, `brand-deep-dive-tab-${i}-${tabText?.toLowerCase().replace(/\s+/g, '-')}`);
  203 |         }
  204 |       }
  205 |     }
  206 |   });
  207 |
  208 |   test('should handle brand comparison if available', async ({ page }) => {
  209 |     await page.goto('/brand-deep-dive');
  210 |     await waitForPageLoad(page);
  211 |     await waitForLoadingToComplete(page);
  212 |
  213 |     // Look for brand comparison features
  214 |     const compareButton = page.locator('button:has-text("Compare"), [data-testid*="compare"]');
  215 |
  216 |     if (await compareButton.isVisible()) {
  217 |       await compareButton.click();
  218 |       await page.waitForTimeout(1000);
  219 |
  220 |       // Look for comparison interface
  221 |       const comparisonInterface = page.locator('[data-testid*="comparison"], .comparison');
  222 |       if (await comparisonInterface.isVisible()) {
  223 |         await takeScreenshot(page, 'brand-deep-dive-comparison');
```