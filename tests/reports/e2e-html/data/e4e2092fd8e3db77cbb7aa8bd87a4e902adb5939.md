# Test info

- Name: Comprehensive Performance Testing >> should test accessibility performance
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-performance.spec.ts:341:7

# Error details

```
Error: expect(received).toBe<PERSON>reater<PERSON><PERSON>(expected)

Expected: > 0
Received:   0
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-performance.spec.ts:361:43
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Dashboard":
      - /url: /dashboard
  - main:
    - heading "Dashboard" [level=1]
    - heading "Overview of your business performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Countries"
    - button "Sales Channels"
    - text: Master KPI Comparison Compare 'Gross Revenue' across selected brands using a line chart.
    - combobox "Select KPI": Gross Revenue
    - img: 2/28/2025 3/31/2025 4/30/2025 0 550K 1M 2M 2M
    - text: Brands Click to toggle. Sorted by latest KPI.
    - list:
      - button "Toggle visibility for Revant" [pressed]: Revant
      - button "Toggle visibility for Qalo" [pressed]: Qalo
      - button "Toggle visibility for Ergonofis" [pressed]: Ergonofis
      - button "Toggle visibility for Opposite Wall" [pressed]: Opposite Wall
      - button "Toggle visibility for Homesick" [pressed]: Homesick
      - button "Toggle visibility for Gravity" [pressed]: Gravity
      - button "Toggle visibility for Freakmount" [pressed]: Freakmount
      - button "Toggle visibility for MiHIGH" [pressed]: MiHIGH
      - button "Toggle visibility for Rose Boreal" [pressed]: Rose Boreal
      - button "Toggle visibility for Rachel" [pressed]: Rachel
      - button "Toggle visibility for Kana" [pressed]: Kana
      - button "Toggle visibility for Arctic Tumblers" [pressed]: Arctic Tumblers
      - button "Toggle visibility for Loctote" [pressed]: Loctote
      - button "Toggle visibility for Proper Pour" [pressed]: Proper Pour
      - button "Toggle visibility for Love Your Melon" [pressed]: Love Your Melon
      - button "Toggle visibility for Wolf & Grizzly" [pressed]: Wolf & Grizzly
      - button "Toggle visibility for Go Green" [pressed]: Go Green
      - button "Toggle visibility for Corretto" [pressed]: Corretto
      - button "Toggle visibility for Alex Bottle" [pressed]: Alex Bottle
    - heading "Key Performance Indicators" [level=3]
    - text: Revenue
    - button "KPI Definition"
    - text: N/A
    - img
    - text: Discount
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Refunds
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Net Revenue
    - button "KPI Definition"
    - text: N/A
    - img
    - text: Landed Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Fulfillment Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Transaction Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Gross Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Adspend
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Contribution Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
  261 |       '/brand-deep-dive'
  262 |     ];
  263 |     
  264 |     for (const url of dataHeavyPages) {
  265 |       const startTime = Date.now();
  266 |       
  267 |       await page.goto(url);
  268 |       await waitForPageLoad(page);
  269 |       await waitForLoadingToComplete(page);
  270 |       
  271 |       // Wait for charts and tables to render
  272 |       await page.waitForTimeout(3000);
  273 |       
  274 |       const loadTime = Date.now() - startTime;
  275 |       console.log(`${url} with data rendering: ${loadTime}ms`);
  276 |       
  277 |       // Data-heavy pages should load within 20 seconds
  278 |       expect(loadTime).toBeLessThan(20000);
  279 |     }
  280 |   });
  281 |
  282 |   test('should test scroll performance on long pages', async ({ page }) => {
  283 |     await page.goto('/marketing-dashboard');
  284 |     await waitForPageLoad(page);
  285 |     await waitForLoadingToComplete(page);
  286 |     
  287 |     // Measure scroll performance
  288 |     const scrollStartTime = Date.now();
  289 |     
  290 |     // Scroll to bottom of page
  291 |     await page.evaluate(() => {
  292 |       window.scrollTo(0, document.body.scrollHeight);
  293 |     });
  294 |     
  295 |     await page.waitForTimeout(1000);
  296 |     
  297 |     // Scroll back to top
  298 |     await page.evaluate(() => {
  299 |       window.scrollTo(0, 0);
  300 |     });
  301 |     
  302 |     const scrollTime = Date.now() - scrollStartTime;
  303 |     console.log(`Scroll performance: ${scrollTime}ms`);
  304 |     
  305 |     // Scrolling should be smooth and fast
  306 |     expect(scrollTime).toBeLessThan(2000);
  307 |   });
  308 |
  309 |   test('should test chart rendering performance', async ({ page }) => {
  310 |     await page.goto('/dashboard');
  311 |     await waitForPageLoad(page);
  312 |     
  313 |     // Measure chart rendering time
  314 |     const chartRenderTime = await page.evaluate(() => {
  315 |       const startTime = Date.now();
  316 |       
  317 |       return new Promise((resolve) => {
  318 |         const checkCharts = () => {
  319 |           const charts = document.querySelectorAll('svg, canvas, .recharts-wrapper');
  320 |           if (charts.length > 0) {
  321 |             resolve(Date.now() - startTime);
  322 |           } else {
  323 |             setTimeout(checkCharts, 100);
  324 |           }
  325 |         };
  326 |         
  327 |         checkCharts();
  328 |         
  329 |         // Timeout after 10 seconds
  330 |         setTimeout(() => resolve(-1), 10000);
  331 |       });
  332 |     });
  333 |     
  334 |     if (chartRenderTime > 0) {
  335 |       console.log(`Chart rendering time: ${chartRenderTime}ms`);
  336 |       // Charts should render within 5 seconds
  337 |       expect(chartRenderTime).toBeLessThan(5000);
  338 |     }
  339 |   });
  340 |
  341 |   test('should test accessibility performance', async ({ page }) => {
  342 |     const pages = ['/dashboard', '/marketing-dashboard', '/admin'];
  343 |     
  344 |     for (const url of pages) {
  345 |       const startTime = Date.now();
  346 |       
  347 |       await page.goto(url);
  348 |       await waitForPageLoad(page);
  349 |       
  350 |       const accessibilityInfo = await testAccessibility(page);
  351 |       const accessibilityTime = Date.now() - startTime;
  352 |       
  353 |       console.log(`${url} accessibility scan: ${accessibilityTime}ms`);
  354 |       console.log(`Accessibility elements found:`, accessibilityInfo);
  355 |       
  356 |       // Accessibility scan should complete quickly
  357 |       expect(accessibilityTime).toBeLessThan(3000);
  358 |       
  359 |       // Verify minimum accessibility requirements
  360 |       expect(accessibilityInfo.headings).toBeGreaterThan(0);
> 361 |       expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
      |                                           ^ Error: expect(received).toBeGreaterThan(expected)
  362 |     }
  363 |   });
  364 | });
  365 |
```