# Test info

- Name: Comprehensive Brand Deep Dive Testing >> should load brand deep dive page with all components
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:20:7

# Error details

```
Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)

Locator: locator(':root')
Expected pattern: /Brand Deep Dive|NOLK/
Received string:  "Create Next App"
Call log:
  - expect.toHaveTitle with timeout 15000ms
  - waiting for locator(':root')
    19 × locator resolved to <html lang="en">…</html>
       - unexpected value "Create Next App"

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:26:24
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
  - main:
    - heading "Brand Deep Dive" [level=1]
    - heading "Detailed analysis of brand performance metrics" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Week
    - text: Data Filters
    - button "Brand"
    - button "Countries"
    - button "Sales Channels"
    - text: 📊
    - heading "No Brand Selected" [level=2]
    - paragraph: Please select a brand from the dropdown above to view detailed performance metrics and analysis.
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 | import {
   3 |   waitForPageLoad,
   4 |   signInAsAdmin,
   5 |   takeScreenshot,
   6 |   testChartRendering,
   7 |   testFilters,
   8 |   waitForLoadingToComplete,
   9 |   testAccessibility,
   10 |   verifyPageElements,
   11 |   testModal
   12 | } from './utils/test-helpers';
   13 |
   14 | test.describe('Comprehensive Brand Deep Dive Testing', () => {
   15 |   test.beforeEach(async ({ page }) => {
   16 |     // Sign in as admin for all tests
   17 |     await signInAsAdmin(page);
   18 |   });
   19 |
   20 |   test('should load brand deep dive page with all components', async ({ page }) => {
   21 |     await page.goto('/brand-deep-dive');
   22 |     await waitForPageLoad(page);
   23 |     await waitForLoadingToComplete(page);
   24 |
   25 |     // Verify page title and main elements
>  26 |     await expect(page).toHaveTitle(/Brand Deep Dive|NOLK/);
      |                        ^ Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)
   27 |     
   28 |     // Check for main components
   29 |     const expectedElements = [
   30 |       'main, [role="main"]',
   31 |       'nav, [role="navigation"]',
   32 |       'h1, h2, [data-testid*="title"]'
   33 |     ];
   34 |     
   35 |     await verifyPageElements(page, expectedElements);
   36 |     await takeScreenshot(page, 'brand-deep-dive-main');
   37 |   });
   38 |
   39 |   test('should display brand selector and allow brand selection', async ({ page }) => {
   40 |     await page.goto('/brand-deep-dive');
   41 |     await waitForPageLoad(page);
   42 |     await waitForLoadingToComplete(page);
   43 |
   44 |     // Look for brand selector
   45 |     const brandSelector = page.locator('[data-testid*="brand-selector"], select[name*="brand"], .brand-selector');
   46 |     
   47 |     if (await brandSelector.isVisible()) {
   48 |       await brandSelector.click();
   49 |       await page.waitForTimeout(1000);
   50 |       
   51 |       // Check for brand options
   52 |       const brandOptions = page.locator('option, [role="option"], [data-testid*="brand-option"]');
   53 |       const optionCount = await brandOptions.count();
   54 |       
   55 |       if (optionCount > 1) {
   56 |         await brandOptions.nth(1).click();
   57 |         await page.waitForTimeout(2000);
   58 |         await waitForLoadingToComplete(page);
   59 |         
   60 |         console.log(`Selected brand from ${optionCount} available options`);
   61 |         await takeScreenshot(page, 'brand-deep-dive-brand-selected');
   62 |       }
   63 |     }
   64 |   });
   65 |
   66 |   test('should display KPI cards specific to selected brand', async ({ page }) => {
   67 |     await page.goto('/brand-deep-dive');
   68 |     await waitForPageLoad(page);
   69 |     await waitForLoadingToComplete(page);
   70 |
   71 |     // Look for brand-specific KPI cards
   72 |     const kpiCards = page.locator('[data-testid*="kpi"], .kpi-card, [class*="kpi"]');
   73 |     const cardCount = await kpiCards.count();
   74 |
   75 |     if (cardCount > 0) {
   76 |       // Verify KPI cards are visible
   77 |       await expect(kpiCards.first()).toBeVisible();
   78 |       
   79 |       // Check for brand-specific metrics
   80 |       const brandMetrics = page.locator('text=/revenue|margin|sales|units/i');
   81 |       expect(await brandMetrics.count()).toBeGreaterThan(0);
   82 |       
   83 |       console.log(`Found ${cardCount} brand KPI cards`);
   84 |     }
   85 |
   86 |     await takeScreenshot(page, 'brand-deep-dive-kpi-cards');
   87 |   });
   88 |
   89 |   test('should render brand performance charts', async ({ page }) => {
   90 |     await page.goto('/brand-deep-dive');
   91 |     await waitForPageLoad(page);
   92 |     await waitForLoadingToComplete(page);
   93 |
   94 |     // Test chart rendering for brand data
   95 |     const charts = await testChartRendering(page);
   96 |     
   97 |     // Look for specific chart types
   98 |     const chartTypes = [
   99 |       'svg', // Recharts
  100 |       'canvas', // Chart.js
  101 |       '.recharts-wrapper',
  102 |       '[data-testid*="chart"]'
  103 |     ];
  104 |
  105 |     let chartCount = 0;
  106 |     for (const chartType of chartTypes) {
  107 |       const elements = page.locator(chartType);
  108 |       chartCount += await elements.count();
  109 |     }
  110 |
  111 |     console.log(`Found ${chartCount} charts on brand deep dive page`);
  112 |     await takeScreenshot(page, 'brand-deep-dive-charts');
  113 |   });
  114 |
  115 |   test('should display marketing campaigns for selected brand', async ({ page }) => {
  116 |     await page.goto('/brand-deep-dive');
  117 |     await waitForPageLoad(page);
  118 |     await waitForLoadingToComplete(page);
  119 |
  120 |     // Look for marketing campaigns section
  121 |     const campaignsSection = page.locator('[data-testid*="campaign"], .campaign, text=/campaign/i');
  122 |     
  123 |     if (await campaignsSection.first().isVisible()) {
  124 |       // Check for campaign data
  125 |       const campaignItems = page.locator('[data-testid*="campaign-item"], .campaign-item, tr');
  126 |       const campaignCount = await campaignItems.count();
```