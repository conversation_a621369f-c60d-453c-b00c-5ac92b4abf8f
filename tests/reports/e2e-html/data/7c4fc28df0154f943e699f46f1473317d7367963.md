# Test info

- Name: Comprehensive Dashboard Testing >> should have functional filters
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts:79:7

# Error details

```
Error: locator.isVisible: Error: strict mode violation: locator('input[type="date"]') resolved to 2 elements:
    1) <input type="date" id="start-date" data-slot="input" value="2025-03-01" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 …/> aka getByRole('textbox', { name: 'Start Date' })
    2) <input type="date" id="end-date" data-slot="input" value="2025-05-30" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md…/> aka getByRole('textbox', { name: 'End Date' })

Call log:
    - checking visibility of locator('input[type="date"]')

    at testFilters (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:411:23)
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts:85:27
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Dashboard":
      - /url: /dashboard
  - main:
    - heading "Dashboard" [level=1]
    - heading "Overview of your business performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Countries"
    - button "Sales Channels"
    - text: Master KPI Comparison Compare 'Gross Revenue' across selected brands using a line chart.
    - combobox "Select KPI": Gross Revenue
    - img: 2/28/2025 3/31/2025 4/30/2025 0 550K 1M 2M 2M
    - text: Brands Click to toggle. Sorted by latest KPI.
    - list:
      - button "Toggle visibility for Revant" [pressed]: Revant
      - button "Toggle visibility for Qalo" [pressed]: Qalo
      - button "Toggle visibility for Ergonofis" [pressed]: Ergonofis
      - button "Toggle visibility for Opposite Wall" [pressed]: Opposite Wall
      - button "Toggle visibility for Homesick" [pressed]: Homesick
      - button "Toggle visibility for Gravity" [pressed]: Gravity
      - button "Toggle visibility for Freakmount" [pressed]: Freakmount
      - button "Toggle visibility for MiHIGH" [pressed]: MiHIGH
      - button "Toggle visibility for Rose Boreal" [pressed]: Rose Boreal
      - button "Toggle visibility for Rachel" [pressed]: Rachel
      - button "Toggle visibility for Kana" [pressed]: Kana
      - button "Toggle visibility for Arctic Tumblers" [pressed]: Arctic Tumblers
      - button "Toggle visibility for Loctote" [pressed]: Loctote
      - button "Toggle visibility for Proper Pour" [pressed]: Proper Pour
      - button "Toggle visibility for Love Your Melon" [pressed]: Love Your Melon
      - button "Toggle visibility for Wolf & Grizzly" [pressed]: Wolf & Grizzly
      - button "Toggle visibility for Go Green" [pressed]: Go Green
      - button "Toggle visibility for Corretto" [pressed]: Corretto
      - button "Toggle visibility for Alex Bottle" [pressed]: Alex Bottle
    - heading "Key Performance Indicators" [level=3]
    - text: Revenue
    - button "KPI Definition"
    - text: $17,245,998.18
    - img: 2/28/2025 4/30/2025 0 2,000,000 4,000,000 6,000,000 8,000,000
    - text: Discount
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,916,224.18
    - img: 2/28/2025 4/30/2025 0 200,000 400,000 600,000 800,000
    - text: Refunds
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $614,536.13
    - img: 2/28/2025 4/30/2025 0 70,000 140,000 210,000 280,000
    - text: Net Revenue
    - button "KPI Definition"
    - text: $14,715,237.88
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
    - text: Landed Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,709,657.76
    - img: 2/28/2025 4/30/2025 0 300,000 600,000 900,000 1,200,000
    - text: Fulfillment Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,167,088.33
    - img: 2/28/2025 4/30/2025 0 250,000 500,000 750,000 1,000,000
    - text: Transaction Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,034,384.19
    - img: 2/28/2025 4/30/2025 0 95,000 190,000 285,000 380,000
    - text: Gross Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $9,520,150.47
    - img: 2/28/2025 4/30/2025 0 900,000 1,800,000 2,700,000 3,600,000
    - text: Adspend
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $3,424,904.20
    - img: 2/28/2025 4/30/2025 0 350,000 700,000 1,050,000 1,400,000
    - text: Contribution Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $12,945,054.68
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
  311 |     const links = page.locator(selector);
  312 |     const count = await links.count();
  313 |
  314 |     if (count === 1) {
  315 |       return links.first();
  316 |     } else if (count > 1) {
  317 |       // If multiple links, try to find the most relevant one
  318 |       for (let i = 0; i < count; i++) {
  319 |         const link = links.nth(i);
  320 |         const href = await link.getAttribute('href');
  321 |         const title = await link.getAttribute('title');
  322 |
  323 |         // Prefer links with exact href match or title match
  324 |         if (href?.includes(linkText.toLowerCase().replace(' ', '-')) || title === linkText) {
  325 |           return link;
  326 |         }
  327 |       }
  328 |       // Return first if no exact match
  329 |       return links.first();
  330 |     }
  331 |   }
  332 |
  333 |   // Fallback to first matching link
  334 |   return page.locator(`nav a:has-text("${linkText}")`).first();
  335 | }
  336 |
  337 | /**
  338 |  * Test API endpoint response
  339 |  */
  340 | export async function testApiEndpoint(page: Page, endpoint: string, expectedStatus = 200) {
  341 |   const response = await page.request.get(endpoint);
  342 |   expect(response.status()).toBe(expectedStatus);
  343 |   return response;
  344 | }
  345 |
  346 | /**
  347 |  * Test form submission
  348 |  */
  349 | export async function testFormSubmission(page: Page, formSelector: string, formData: Record<string, string>) {
  350 |   const form = page.locator(formSelector);
  351 |   await expect(form).toBeVisible();
  352 |
  353 |   // Fill form fields
  354 |   for (const [field, value] of Object.entries(formData)) {
  355 |     await page.fill(`${formSelector} input[name="${field}"], ${formSelector} select[name="${field}"]`, value);
  356 |   }
  357 |
  358 |   // Submit form
  359 |   await page.click(`${formSelector} button[type="submit"]`);
  360 | }
  361 |
  362 | /**
  363 |  * Test data table functionality
  364 |  */
  365 | export async function testDataTable(page: Page, tableSelector = 'table') {
  366 |   const table = page.locator(tableSelector);
  367 |   await expect(table).toBeVisible();
  368 |
  369 |   // Check for headers
  370 |   const headers = table.locator('thead th');
  371 |   const headerCount = await headers.count();
  372 |   expect(headerCount).toBeGreaterThan(0);
  373 |
  374 |   // Check for data rows
  375 |   const rows = table.locator('tbody tr');
  376 |   const rowCount = await rows.count();
  377 |
  378 |   return { headerCount, rowCount };
  379 | }
  380 |
  381 | /**
  382 |  * Test chart rendering
  383 |  */
  384 | export async function testChartRendering(page: Page, chartSelector = 'svg, canvas, .recharts-wrapper') {
  385 |   const chart = page.locator(chartSelector);
  386 |   await expect(chart).toBeVisible({ timeout: 10000 });
  387 |
  388 |   // Wait for chart to fully render
  389 |   await page.waitForTimeout(2000);
  390 |
  391 |   return chart;
  392 | }
  393 |
  394 | /**
  395 |  * Test filter functionality
  396 |  */
  397 | export async function testFilters(page: Page) {
  398 |   // Look for common filter elements
  399 |   const filterElements = [
  400 |     'select[name*="brand"]',
  401 |     'input[type="date"]',
  402 |     'select[name*="currency"]',
  403 |     'select[name*="country"]',
  404 |     '[data-testid*="filter"]'
  405 |   ];
  406 |
  407 |   const activeFilters = [];
  408 |
  409 |   for (const selector of filterElements) {
  410 |     const element = page.locator(selector);
> 411 |     if (await element.isVisible()) {
      |                       ^ Error: locator.isVisible: Error: strict mode violation: locator('input[type="date"]') resolved to 2 elements:
  412 |       activeFilters.push(selector);
  413 |     }
  414 |   }
  415 |
  416 |   return activeFilters;
  417 | }
  418 |
  419 | /**
  420 |  * Test pagination if present
  421 |  */
  422 | export async function testPagination(page: Page) {
  423 |   const pagination = page.locator('[data-testid="pagination"], .pagination, nav[aria-label*="pagination"]');
  424 |
  425 |   if (await pagination.isVisible()) {
  426 |     const nextButton = pagination.locator('button:has-text("Next"), button[aria-label*="next"]');
  427 |     const prevButton = pagination.locator('button:has-text("Previous"), button[aria-label*="previous"]');
  428 |
  429 |     return {
  430 |       hasPagination: true,
  431 |       hasNext: await nextButton.isVisible(),
  432 |       hasPrevious: await prevButton.isVisible()
  433 |     };
  434 |   }
  435 |
  436 |   return { hasPagination: false };
  437 | }
  438 |
  439 | /**
  440 |  * Test search functionality
  441 |  */
  442 | export async function testSearch(page: Page, searchTerm: string) {
  443 |   const searchInput = page.locator('input[type="search"], input[placeholder*="search"], input[name*="search"]');
  444 |
  445 |   if (await searchInput.isVisible()) {
  446 |     await searchInput.fill(searchTerm);
  447 |     await page.keyboard.press('Enter');
  448 |     await page.waitForTimeout(1000);
  449 |     return true;
  450 |   }
  451 |
  452 |   return false;
  453 | }
  454 |
  455 | /**
  456 |  * Test modal/dialog functionality
  457 |  */
  458 | export async function testModal(page: Page, triggerSelector: string) {
  459 |   await page.click(triggerSelector);
  460 |
  461 |   const modal = page.locator('[role="dialog"], .modal, [data-testid*="modal"]');
  462 |   await expect(modal).toBeVisible();
  463 |
  464 |   // Test close functionality
  465 |   const closeButton = modal.locator('button[aria-label*="close"], button:has-text("Close"), [data-testid*="close"]');
  466 |   if (await closeButton.isVisible()) {
  467 |     await closeButton.click();
  468 |     await expect(modal).not.toBeVisible();
  469 |   }
  470 | }
  471 |
  472 | /**
  473 |  * Test loading states
  474 |  */
  475 | export async function waitForLoadingToComplete(page: Page) {
  476 |   // Wait for common loading indicators to disappear
  477 |   const loadingSelectors = [
  478 |     '.loading',
  479 |     '.spinner',
  480 |     '[data-testid*="loading"]',
  481 |     '.animate-spin',
  482 |     'text="Loading"'
  483 |   ];
  484 |
  485 |   for (const selector of loadingSelectors) {
  486 |     try {
  487 |       await page.waitForSelector(selector, { state: 'hidden', timeout: 5000 });
  488 |     } catch {
  489 |       // Ignore if selector doesn't exist
  490 |     }
  491 |   }
  492 | }
  493 |
  494 | /**
  495 |  * Test accessibility features
  496 |  */
  497 | export async function testAccessibility(page: Page) {
  498 |   // Check for basic accessibility attributes
  499 |   const elementsWithAriaLabels = await page.locator('[aria-label]').count();
  500 |   const elementsWithRoles = await page.locator('[role]').count();
  501 |   const headings = await page.locator('h1, h2, h3, h4, h5, h6').count();
  502 |   const landmarks = await page.locator('[role="main"], [role="navigation"], [role="banner"], [role="contentinfo"]').count();
  503 |
  504 |   return {
  505 |     ariaLabels: elementsWithAriaLabels,
  506 |     roles: elementsWithRoles,
  507 |     headings,
  508 |     landmarks
  509 |   };
  510 | }
  511 |
```