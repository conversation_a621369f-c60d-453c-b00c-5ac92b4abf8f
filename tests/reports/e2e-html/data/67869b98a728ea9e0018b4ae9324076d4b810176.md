# Test info

- Name: Credentials Authentication >> should successfully sign in with farbour/admin credentials
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:178:7

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('h1, h2, [data-testid="dashboard-title"]') resolved to 2 elements:
    1) <h1 class="text-2xl sm:text-3xl font-bold tracking-tight">Dashboard</h1> aka getByRole('heading', { name: 'Dashboard' })
    2) <h2 class="text-sm sm:text-lg text-muted-foreground mt-1">Overview of your business performance</h2> aka getByRole('heading', { name: 'Overview of your business' })

Call log:
  - expect.toBeVisible with timeout 10000ms
  - waiting for locator('h1, h2, [data-testid="dashboard-title"]')

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:190:36
```

# Page snapshot

```yaml
- alert
- button "Open Next.js Dev Tools":
  - img
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Dashboard":
      - /url: /dashboard
  - main:
    - heading "Dashboard" [level=1]
    - heading "Overview of your business performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Countries"
    - button "Sales Channels"
    - text: Master KPI Comparison Compare 'Gross Revenue' across selected brands using a line chart.
    - combobox "Select KPI": Gross Revenue
    - text: No data available for the selected KPI and filters. Brands Click to toggle. Sorted by latest KPI. No brands found or data available.
    - heading "Key Performance Indicators" [level=3]
    - text: Revenue
    - button "KPI Definition"
    - text: N/A
    - img
    - text: Discount
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Refunds
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Net Revenue
    - button "KPI Definition"
    - text: N/A
    - img
    - text: Landed Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Fulfillment Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Transaction Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Gross Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Adspend
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
    - text: Contribution Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: N/A
    - img
```

# Test source

```ts
   90 |
   91 |   test('should handle authentication errors gracefully', async ({ page }) => {
   92 |     await page.goto('/auth/signin?error=OAuthSignin');
   93 |     await waitForPageLoad(page);
   94 |
   95 |     // Should still show the sign-in page
   96 |     await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
   97 |     await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();
   98 |
   99 |     // Could check for error message if implemented
  100 |     // await expect(page.locator('.error-message')).toBeVisible();
  101 |   });
  102 |
  103 |   test('should handle callback URL parameter', async ({ page }) => {
  104 |     const callbackUrl = encodeURIComponent('/brand-deep-dive');
  105 |     await page.goto(`/auth/signin?callbackUrl=${callbackUrl}`);
  106 |     await waitForPageLoad(page);
  107 |
  108 |     // Should show sign-in page with callback URL preserved
  109 |     await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
  110 |
  111 |     // URL should contain the callback parameter
  112 |     expect(page.url()).toContain(`callbackUrl=${callbackUrl}`);
  113 |   });
  114 |
  115 |   test('should be responsive on different screen sizes', async ({ page }) => {
  116 |     await page.goto('/auth/signin');
  117 |     await waitForPageLoad(page);
  118 |
  119 |     // Test mobile viewport
  120 |     await page.setViewportSize({ width: 375, height: 667 });
  121 |     await page.waitForTimeout(500);
  122 |
  123 |     // Elements should still be visible and properly arranged
  124 |     await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
  125 |     await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();
  126 |
  127 |     await takeScreenshot(page, 'sign-in-mobile');
  128 |
  129 |     // Test tablet viewport
  130 |     await page.setViewportSize({ width: 768, height: 1024 });
  131 |     await page.waitForTimeout(500);
  132 |
  133 |     await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
  134 |     await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();
  135 |
  136 |     await takeScreenshot(page, 'sign-in-tablet');
  137 |
  138 |     // Test desktop viewport
  139 |     await page.setViewportSize({ width: 1920, height: 1080 });
  140 |     await page.waitForTimeout(500);
  141 |
  142 |     await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
  143 |     await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();
  144 |
  145 |     await takeScreenshot(page, 'sign-in-desktop');
  146 |   });
  147 |
  148 |   test('should not have console errors on sign-in page', async ({ page }) => {
  149 |     const consoleErrors: string[] = [];
  150 |
  151 |     page.on('console', (msg) => {
  152 |       if (msg.type() === 'error') {
  153 |         consoleErrors.push(msg.text());
  154 |       }
  155 |     });
  156 |
  157 |     await page.goto('/auth/signin');
  158 |     await waitForPageLoad(page);
  159 |
  160 |     // Filter out known acceptable errors (if any)
  161 |     const criticalErrors = consoleErrors.filter(error =>
  162 |       !error.includes('favicon') &&
  163 |       !error.includes('404') &&
  164 |       !error.includes('net::ERR_FAILED')
  165 |     );
  166 |
  167 |     expect(criticalErrors).toHaveLength(0);
  168 |   });
  169 | });
  170 |
  171 | test.describe('Credentials Authentication', () => {
  172 |   test.beforeEach(async ({ page }) => {
  173 |     // Ensure we start from a clean state
  174 |     await page.goto('/auth/signin');
  175 |     await waitForPageLoad(page);
  176 |   });
  177 |
  178 |   test('should successfully sign in with farbour/admin credentials', async ({ page }) => {
  179 |     // Take screenshot of sign-in page
  180 |     await takeScreenshot(page, 'signin-page-before-credentials');
  181 |
  182 |     // Sign in with admin credentials
  183 |     await signInAsAdmin(page);
  184 |
  185 |     // Verify we're on the dashboard
  186 |     expect(page.url()).toContain('/dashboard');
  187 |
  188 |     // Verify dashboard elements are present
  189 |     const dashboardHeading = page.locator('h1, h2, [data-testid="dashboard-title"]');
> 190 |     await expect(dashboardHeading).toBeVisible({ timeout: 10000 });
      |                                    ^ Error: expect.toBeVisible: Error: strict mode violation: locator('h1, h2, [data-testid="dashboard-title"]') resolved to 2 elements:
  191 |
  192 |     // Take screenshot of successful login
  193 |     await takeScreenshot(page, 'successful-admin-login');
  194 |   });
  195 |
  196 |   test('should reject invalid credentials', async ({ page }) => {
  197 |     // Navigate to sign-in page
  198 |     await page.goto('/auth/signin');
  199 |     await waitForPageLoad(page);
  200 |
  201 |     // Switch to credentials tab if available
  202 |     const credentialsTab = page.locator('button:has-text("Username/Password")');
  203 |     if (await credentialsTab.isVisible()) {
  204 |       await credentialsTab.click();
  205 |       await page.waitForTimeout(500);
  206 |     }
  207 |
  208 |     // Fill in invalid credentials
  209 |     await page.fill('input[name="username"]', 'invalid');
  210 |     await page.fill('input[name="password"]', 'wrongpassword');
  211 |
  212 |     // Click sign in button
  213 |     await page.click('button[type="submit"]:has-text("Sign In")');
  214 |
  215 |     // Wait a moment for the response
  216 |     await page.waitForTimeout(2000);
  217 |
  218 |     // Verify we're still on sign-in page
  219 |     expect(page.url()).toContain('/auth/signin');
  220 |
  221 |     // Look for error message
  222 |     const errorMessage = page.locator('text=/invalid|error|failed|incorrect/i');
  223 |     if (await errorMessage.isVisible()) {
  224 |       await takeScreenshot(page, 'invalid-credentials-error');
  225 |     }
  226 |   });
  227 |
  228 |   test('should verify admin access after login', async ({ page }) => {
  229 |     // Sign in as admin
  230 |     await signInAsAdmin(page);
  231 |
  232 |     // Verify admin access
  233 |     const hasAdminAccess = await verifyAdminAccess(page);
  234 |     expect(hasAdminAccess).toBe(true);
  235 |
  236 |     // Navigate to admin dashboard
  237 |     await navigateToAdminDashboard(page);
  238 |
  239 |     // Take screenshot of admin dashboard
  240 |     await takeScreenshot(page, 'admin-dashboard-access');
  241 |   });
  242 |
  243 |   test('should maintain session across page navigation', async ({ page }) => {
  244 |     // Sign in as admin
  245 |     await signInAsAdmin(page);
  246 |
  247 |     // Navigate to different pages and verify we stay authenticated
  248 |     const pages = ['/dashboard', '/marketing', '/admin'];
  249 |
  250 |     for (const pagePath of pages) {
  251 |       await page.goto(pagePath);
  252 |       await waitForPageLoad(page);
  253 |
  254 |       // Verify we're not redirected to sign-in
  255 |       expect(page.url()).not.toContain('/auth/signin');
  256 |       expect(page.url()).toContain(pagePath);
  257 |
  258 |       // Take screenshot
  259 |       const pageName = pagePath.replace('/', '') || 'root';
  260 |       await takeScreenshot(page, `authenticated-${pageName}`);
  261 |     }
  262 |   });
  263 |
  264 |   test('should successfully sign out', async ({ page }) => {
  265 |     // Sign in as admin
  266 |     await signInAsAdmin(page);
  267 |
  268 |     // Sign out
  269 |     await signOut(page);
  270 |
  271 |     // Verify we're redirected to sign-in page
  272 |     await page.waitForURL('**/auth/signin', { timeout: 10000 });
  273 |
  274 |     // Verify sign-in page elements are visible
  275 |     await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
  276 |
  277 |     // Take screenshot of sign-out state
  278 |     await takeScreenshot(page, 'after-signout');
  279 |   });
  280 | });
  281 |
```