# Test info

- Name: Comprehensive Error Handling and Edge Cases >> should handle 404 errors gracefully
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:15:7

# Error details

```
Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:37:35
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
    - link "invalid-campaign-id":
      - /url: /marketing-dashboard/invalid-campaign-id
  - main:
    - link "Back to Dashboard":
      - /url: /marketing-dashboard
      - button "Back to Dashboard"
    - heading "Campaign Details" [level=1]
    - heading "Unable to Load Campaign" [level=3]
    - paragraph: "Could not load campaign details: Failed to fetch campaign details"
- alert
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 1 Issue
- button "Collapse issues badge":
  - img
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 | import {
   3 |   waitForPageLoad,
   4 |   signInAsAdmin,
   5 |   takeScreenshot,
   6 |   waitForLoadingToComplete
   7 | } from './utils/test-helpers';
   8 |
   9 | test.describe('Comprehensive Error Handling and Edge Cases', () => {
   10 |   test.beforeEach(async ({ page }) => {
   11 |     // Sign in as admin for most tests
   12 |     await signInAsAdmin(page);
   13 |   });
   14 |
   15 |   test('should handle 404 errors gracefully', async ({ page }) => {
   16 |     const invalidUrls = [
   17 |       '/nonexistent-page',
   18 |       '/dashboard/invalid',
   19 |       '/admin/nonexistent',
   20 |       '/marketing-dashboard/invalid-campaign-id',
   21 |       '/brand-deep-dive/invalid'
   22 |     ];
   23 |
   24 |     for (const url of invalidUrls) {
   25 |       await page.goto(url);
   26 |       await waitForPageLoad(page);
   27 |       
   28 |       // Should either show 404 page or redirect to valid page
   29 |       const currentUrl = page.url();
   30 |       console.log(`${url} -> ${currentUrl}`);
   31 |       
   32 |       // Check for 404 page elements or redirect
   33 |       const is404 = currentUrl.includes('404') || 
   34 |                    await page.locator('text=/404|not found|page not found/i').isVisible();
   35 |       const isRedirect = !currentUrl.includes(url.split('/')[1]);
   36 |       
>  37 |       expect(is404 || isRedirect).toBe(true);
      |                                   ^ Error: expect(received).toBe(expected) // Object.is equality
   38 |       await takeScreenshot(page, `error-404-${url.replace(/\//g, '-')}`);
   39 |     }
   40 |   });
   41 |
   42 |   test('should handle network errors and offline scenarios', async ({ page }) => {
   43 |     await page.goto('/dashboard');
   44 |     await waitForPageLoad(page);
   45 |     
   46 |     // Simulate network failure
   47 |     await page.context().setOffline(true);
   48 |     
   49 |     // Try to navigate to another page
   50 |     await page.goto('/marketing-dashboard');
   51 |     await page.waitForTimeout(3000);
   52 |     
   53 |     // Should show offline message or handle gracefully
   54 |     const offlineIndicator = page.locator('text=/offline|network error|connection failed/i');
   55 |     const isOfflineHandled = await offlineIndicator.isVisible();
   56 |     
   57 |     console.log(`Offline handling: ${isOfflineHandled ? 'Detected' : 'Not detected'}`);
   58 |     await takeScreenshot(page, 'network-offline');
   59 |     
   60 |     // Restore network
   61 |     await page.context().setOffline(false);
   62 |     await page.waitForTimeout(2000);
   63 |     
   64 |     // Should recover when network is restored
   65 |     await page.reload();
   66 |     await waitForPageLoad(page);
   67 |     await takeScreenshot(page, 'network-restored');
   68 |   });
   69 |
   70 |   test('should handle API errors gracefully', async ({ page }) => {
   71 |     // Intercept API calls and return errors
   72 |     await page.route('**/api/dashboard/**', route => {
   73 |       route.fulfill({
   74 |         status: 500,
   75 |         contentType: 'application/json',
   76 |         body: JSON.stringify({ error: 'Internal Server Error' })
   77 |       });
   78 |     });
   79 |     
   80 |     await page.goto('/dashboard');
   81 |     await waitForPageLoad(page);
   82 |     await waitForLoadingToComplete(page);
   83 |     
   84 |     // Should show error message or fallback content
   85 |     const errorMessage = page.locator('text=/error|failed|something went wrong/i');
   86 |     const hasErrorHandling = await errorMessage.isVisible();
   87 |     
   88 |     console.log(`API error handling: ${hasErrorHandling ? 'Detected' : 'Not detected'}`);
   89 |     await takeScreenshot(page, 'api-error-500');
   90 |   });
   91 |
   92 |   test('should handle authentication errors', async ({ page }) => {
   93 |     // Test without authentication
   94 |     const unauthenticatedPage = await page.context().newPage();
   95 |     
   96 |     const protectedPages = [
   97 |       '/dashboard',
   98 |       '/marketing-dashboard',
   99 |       '/brand-deep-dive',
  100 |       '/executive-summary',
  101 |       '/budget',
  102 |       '/ai-assistant',
  103 |       '/admin'
  104 |     ];
  105 |     
  106 |     for (const url of protectedPages) {
  107 |       await unauthenticatedPage.goto(url);
  108 |       await waitForPageLoad(unauthenticatedPage);
  109 |       
  110 |       // Should redirect to sign-in page
  111 |       const currentUrl = unauthenticatedPage.url();
  112 |       expect(currentUrl).toContain('/auth/signin');
  113 |       
  114 |       console.log(`${url} -> redirected to sign-in`);
  115 |     }
  116 |     
  117 |     await takeScreenshot(unauthenticatedPage, 'auth-redirect');
  118 |     await unauthenticatedPage.close();
  119 |   });
  120 |
  121 |   test('should handle form validation errors', async ({ page }) => {
  122 |     await page.goto('/admin/users');
  123 |     await waitForPageLoad(page);
  124 |     
  125 |     // Look for add user button
  126 |     const addButton = page.locator('button:has-text("Add"), button:has-text("Create")');
  127 |     
  128 |     if (await addButton.isVisible()) {
  129 |       await addButton.click();
  130 |       await page.waitForTimeout(1000);
  131 |       
  132 |       // Look for form
  133 |       const form = page.locator('form');
  134 |       
  135 |       if (await form.isVisible()) {
  136 |         // Try to submit empty form
  137 |         const submitButton = form.locator('button[type="submit"], button:has-text("Save"), button:has-text("Create")');
```