# Test info

- Name: Comprehensive Executive Summary Testing >> should handle PDF export functionality
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:176:7

# Error details

```
Error: locator.isVisible: Unexpected token "=" while parsing css selector "[data-testid*="progress"], .progress, text=/generating|exporting/i". Did you mean to CSS.escape it?
Call log:
    - checking visibility of [data-testid*="progress"], .progress, text=/generating|exporting/i

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:194:32
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Executive Summary":
      - /url: /executive-summary
  - main:
    - heading "Executive Summary" [level=1]
    - paragraph: Performance highlights for All Brands
    - group:
      - radio "Slide View" [checked]: Slides
      - radio "Grid View": Grid
    - button "Export PDF"
    - text: "Brand:"
    - button "Brand"
    - text: "Period:"
    - button "Month"
    - button "Quarter"
    - button "Year"
    - text: "Month:"
    - combobox: April 2025
    - text: "Currency:"
    - combobox: CAD
    - paragraph: PDF generated successfully!
    - region "KPI Slides":
      - 'article "Slide 1: Total Revenue"':
        - heading "Total Revenue" [level=2]
        - paragraph: April 2025 • Slide 1 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$5,323,953 Current month value 30.6% vs Budget Budget comparison -12.8% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$21,554,437 Last Updated: 09:27 PM"
          - region "Chart Visualization":
            - text: 📊
            - heading "Chart Data Unavailable" [level=3]
            - paragraph: Time series data is not available for this KPI. The current value and comparisons are shown in the metrics panel.
        - text: Current period data only Scroll down for next KPI
        - img
      - 'article "Slide 2: Net Revenue"':
        - heading "Net Revenue" [level=2]
        - paragraph: April 2025 • Slide 2 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$4,454,872 Current month value 20.3% vs Budget Budget comparison -17.5% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$18,506,104 Last Updated: 09:27 PM"
          - region "Chart Visualization":
            - heading "Net Revenue Trend" [level=3]
            - paragraph: April 2025 • 30 data points
            - img: Apr 1 Apr 5 Apr 9 Apr 13 Apr 17 Apr 21 Apr 25 Apr 29 CA$0 CA$50,000 CA$100,000 CA$150,000 CA$200,000
            - text: "Data aggregated by day Total: CA$4,454,872"
        - text: 30 data points available Scroll down for next KPI
        - img
      - 'article "Slide 3: Gross Profit"':
        - heading "Gross Profit" [level=2]
        - paragraph: April 2025 • Slide 3 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$2,934,228 Current month value 56.4% vs Budget Budget comparison -15.2% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$11,848,239 Last Updated: 09:27 PM"
          - region "Chart Visualization":
            - text: 📊
            - heading "Chart Data Unavailable" [level=3]
            - paragraph: Time series data is not available for this KPI. The current value and comparisons are shown in the metrics panel.
        - text: Current period data only Scroll down for next KPI
        - img
      - 'article "Slide 4: Contribution Margin"':
        - heading "Contribution Margin" [level=2]
        - paragraph: April 2025 • Slide 4 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$4,032,120 Current month value 233.9% vs Budget Budget comparison -7.7% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$16,292,596 Last Updated: 09:27 PM"
          - region "Chart Visualization":
            - heading "Contribution Margin Trend" [level=3]
            - paragraph: April 2025 • 30 data points
            - img: Apr 1 Apr 5 Apr 9 Apr 13 Apr 17 Apr 21 Apr 25 Apr 29 CA$0 CA$40,000 CA$80,000 CA$120,000 CA$160,000
            - text: "Data aggregated by day Total: CA$4,032,120"
        - text: 30 data points available Scroll down for next KPI
        - img
      - 'article "Slide 5: Adspend"':
        - heading "Adspend" [level=2]
        - paragraph: April 2025 • Slide 5 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$1,097,892 Current month value 16.8% vs Budget Budget comparison 20.7% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$4,444,358 Last Updated: 09:27 PM"
          - region "Chart Visualization":
            - heading "Adspend Trend" [level=3]
            - paragraph: April 2025 • 30 data points
            - img: Apr 1 Apr 5 Apr 9 Apr 13 Apr 17 Apr 21 Apr 25 Apr 29 CA$0 CA$15,000 CA$30,000 CA$45,000 CA$60,000
            - text: "Data aggregated by day Total: CA$1,097,892"
        - text: 30 data points available
      - 'button "Go to slide 1: Total Revenue"'
      - 'button "Go to slide 2: Net Revenue"'
      - 'button "Go to slide 3: Gross Profit"'
      - 'button "Go to slide 4: Contribution Margin"'
      - 'button "Go to slide 5: Adspend"'
      - text: Scrollable slide view with 5 KPI slides
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   94 |       await expect(kpiCards.first()).toBeVisible();
   95 |       
   96 |       // Check for executive-level metrics
   97 |       const executiveMetrics = page.locator('text=/revenue|profit|margin|growth|performance/i');
   98 |       const metricsCount = await executiveMetrics.count();
   99 |       
  100 |       // Check for proper number formatting
  101 |       const currencyValues = page.locator('text=/\\$[0-9,]+|€[0-9,]+|£[0-9,]+/');
  102 |       const percentageValues = page.locator('text=/[0-9]+\\.?[0-9]*%/');
  103 |       
  104 |       const currencyCount = await currencyValues.count();
  105 |       const percentageCount = await percentageValues.count();
  106 |       
  107 |       console.log(`Found ${cardCount} KPI cards, ${metricsCount} metrics, ${currencyCount} currency values, ${percentageCount} percentages`);
  108 |     }
  109 |
  110 |     await takeScreenshot(page, 'executive-summary-kpi-cards');
  111 |   });
  112 |
  113 |   test('should switch between cards and slides view', async ({ page }) => {
  114 |     await page.goto('/executive-summary');
  115 |     await waitForPageLoad(page);
  116 |     await waitForLoadingToComplete(page);
  117 |
  118 |     // Look for view toggle buttons
  119 |     const cardsViewButton = page.locator('button:has-text("Cards"), [data-testid*="cards-view"]');
  120 |     const slidesViewButton = page.locator('button:has-text("Slides"), [data-testid*="slides-view"]');
  121 |
  122 |     // Test cards view
  123 |     if (await cardsViewButton.isVisible()) {
  124 |       await cardsViewButton.click();
  125 |       await page.waitForTimeout(1000);
  126 |       await waitForLoadingToComplete(page);
  127 |       await takeScreenshot(page, 'executive-summary-cards-view');
  128 |     }
  129 |
  130 |     // Test slides view
  131 |     if (await slidesViewButton.isVisible()) {
  132 |       await slidesViewButton.click();
  133 |       await page.waitForTimeout(1000);
  134 |       await waitForLoadingToComplete(page);
  135 |       
  136 |       // Look for slide navigation
  137 |       const slideNavigation = page.locator('[data-testid*="slide"], .slide, [class*="slide"]');
  138 |       const slideCount = await slideNavigation.count();
  139 |       
  140 |       if (slideCount > 0) {
  141 |         console.log(`Found ${slideCount} slides`);
  142 |         
  143 |         // Test slide navigation
  144 |         const nextButton = page.locator('button:has-text("Next"), [data-testid*="next"]');
  145 |         if (await nextButton.isVisible()) {
  146 |           await nextButton.click();
  147 |           await page.waitForTimeout(1000);
  148 |           await takeScreenshot(page, 'executive-summary-slides-navigation');
  149 |         }
  150 |       }
  151 |       
  152 |       await takeScreenshot(page, 'executive-summary-slides-view');
  153 |     }
  154 |   });
  155 |
  156 |   test('should display trend charts and analysis', async ({ page }) => {
  157 |     await page.goto('/executive-summary');
  158 |     await waitForPageLoad(page);
  159 |     await waitForLoadingToComplete(page);
  160 |
  161 |     // Test chart rendering for trend analysis
  162 |     const charts = await testChartRendering(page);
  163 |     
  164 |     // Look for trend-specific elements
  165 |     const trendElements = page.locator('[data-testid*="trend"], .trend, text=/trend|growth|change/i');
  166 |     const trendCount = await trendElements.count();
  167 |     
  168 |     // Look for trend indicators (up/down arrows, colors)
  169 |     const trendIndicators = page.locator('.trend-up, .trend-down, .positive, .negative, [class*="increase"], [class*="decrease"]');
  170 |     const indicatorCount = await trendIndicators.count();
  171 |     
  172 |     console.log(`Found ${trendCount} trend elements and ${indicatorCount} trend indicators`);
  173 |     await takeScreenshot(page, 'executive-summary-trends');
  174 |   });
  175 |
  176 |   test('should handle PDF export functionality', async ({ page }) => {
  177 |     await page.goto('/executive-summary');
  178 |     await waitForPageLoad(page);
  179 |     await waitForLoadingToComplete(page);
  180 |
  181 |     // Look for PDF export button
  182 |     const pdfExportButton = page.locator('button:has-text("PDF"), button:has-text("Export"), [data-testid*="pdf"], [data-testid*="export"]');
  183 |     
  184 |     if (await pdfExportButton.isVisible()) {
  185 |       // Test PDF export button (don't actually download)
  186 |       await expect(pdfExportButton).toBeEnabled();
  187 |       
  188 |       // Click to test the export process initiation
  189 |       await pdfExportButton.click();
  190 |       await page.waitForTimeout(2000);
  191 |       
  192 |       // Look for export progress or confirmation
  193 |       const exportProgress = page.locator('[data-testid*="progress"], .progress, text=/generating|exporting/i');
> 194 |       if (await exportProgress.isVisible()) {
      |                                ^ Error: locator.isVisible: Unexpected token "=" while parsing css selector "[data-testid*="progress"], .progress, text=/generating|exporting/i". Did you mean to CSS.escape it?
  195 |         console.log('PDF export process initiated');
  196 |         await takeScreenshot(page, 'executive-summary-pdf-export-progress');
  197 |       }
  198 |       
  199 |       await takeScreenshot(page, 'executive-summary-pdf-export');
  200 |     }
  201 |   });
  202 |
  203 |   test('should display period comparison data', async ({ page }) => {
  204 |     await page.goto('/executive-summary');
  205 |     await waitForPageLoad(page);
  206 |     await waitForLoadingToComplete(page);
  207 |
  208 |     // Look for comparison data (YoY, MoM, etc.)
  209 |     const comparisonElements = page.locator('text=/vs|compared|previous|last year|year over year|yoy|mom/i');
  210 |     const comparisonCount = await comparisonElements.count();
  211 |     
  212 |     // Look for comparison values and percentages
  213 |     const comparisonValues = page.locator('text=/[+-][0-9]+%|[+-]\\$[0-9,]+/');
  214 |     const comparisonValueCount = await comparisonValues.count();
  215 |     
  216 |     console.log(`Found ${comparisonCount} comparison elements and ${comparisonValueCount} comparison values`);
  217 |     
  218 |     if (comparisonCount > 0) {
  219 |       await takeScreenshot(page, 'executive-summary-comparisons');
  220 |     }
  221 |   });
  222 |
  223 |   test('should handle currency switching', async ({ page }) => {
  224 |     await page.goto('/executive-summary');
  225 |     await waitForPageLoad(page);
  226 |     await waitForLoadingToComplete(page);
  227 |
  228 |     // Test currency selector
  229 |     const currencySelector = page.locator('select[name*="currency"], [data-testid*="currency"]');
  230 |     
  231 |     if (await currencySelector.isVisible()) {
  232 |       await currencySelector.click();
  233 |       await page.waitForTimeout(500);
  234 |       
  235 |       const currencyOptions = page.locator('option, [role="option"]');
  236 |       const optionCount = await currencyOptions.count();
  237 |       
  238 |       if (optionCount > 1) {
  239 |         // Get current currency values before switching
  240 |         const beforeValues = await page.locator('text=/\\$[0-9,]+|€[0-9,]+|£[0-9,]+/').allTextContents();
  241 |         
  242 |         // Switch currency
  243 |         await currencyOptions.nth(1).click();
  244 |         await page.waitForTimeout(3000);
  245 |         await waitForLoadingToComplete(page);
  246 |         
  247 |         // Get currency values after switching
  248 |         const afterValues = await page.locator('text=/\\$[0-9,]+|€[0-9,]+|£[0-9,]+/').allTextContents();
  249 |         
  250 |         console.log(`Currency switched - Before: ${beforeValues.length} values, After: ${afterValues.length} values`);
  251 |         await takeScreenshot(page, 'executive-summary-currency-switched');
  252 |       }
  253 |     }
  254 |   });
  255 |
  256 |   test('should display methodology and notes if available', async ({ page }) => {
  257 |     await page.goto('/executive-summary');
  258 |     await waitForPageLoad(page);
  259 |     await waitForLoadingToComplete(page);
  260 |
  261 |     // Look for methodology or notes sections
  262 |     const methodologySection = page.locator('text=/methodology|notes|calculation|formula/i');
  263 |     const methodologyCount = await methodologySection.count();
  264 |     
  265 |     if (methodologyCount > 0) {
  266 |       // Look for expandable sections or help buttons
  267 |       const helpButtons = page.locator('button:has-text("?"), [data-testid*="help"], .help-button');
  268 |       const helpCount = await helpButtons.count();
  269 |       
  270 |       if (helpCount > 0) {
  271 |         await helpButtons.first().click();
  272 |         await page.waitForTimeout(1000);
  273 |         await takeScreenshot(page, 'executive-summary-methodology');
  274 |       }
  275 |       
  276 |       console.log(`Found ${methodologyCount} methodology elements and ${helpCount} help buttons`);
  277 |     }
  278 |   });
  279 |
  280 |   test('should be responsive across different screen sizes', async ({ page }) => {
  281 |     await page.goto('/executive-summary');
  282 |     await waitForPageLoad(page);
  283 |
  284 |     const viewports = [
  285 |       { width: 1920, height: 1080, name: 'desktop' },
  286 |       { width: 1024, height: 768, name: 'tablet' },
  287 |       { width: 375, height: 667, name: 'mobile' }
  288 |     ];
  289 |
  290 |     for (const viewport of viewports) {
  291 |       await page.setViewportSize(viewport);
  292 |       await page.waitForTimeout(1000);
  293 |       
  294 |       // Verify main elements are still visible
```