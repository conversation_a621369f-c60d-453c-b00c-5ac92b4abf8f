# Test info

- Name: Comprehensive Executive Summary Testing >> should display trend charts and analysis
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:156:7

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('svg, canvas, .recharts-wrapper') resolved to 37 elements:
    1) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-inner-shadow-top !size-5">…</svg> aka getByRole('link', { name: 'NOLK' })
    2) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dashboard ">…</svg> aka getByRole('listitem').filter({ hasText: /^Dashboard$/ }).getByRole('link')
    3) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-list-details ">…</svg> aka getByRole('link', { name: 'Brand Deep Dive' })
    4) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-chart-bar ">…</svg> aka getByRole('link', { name: 'Marketing Dashboard' })
    5) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-report ">…</svg> aka getByRole('listitem').filter({ hasText: 'Executive Summary' }).getByRole('link')
    6) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-wallet ">…</svg> aka getByRole('link', { name: 'Budget' })
    7) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-file-ai ">…</svg> aka getByRole('link', { name: 'AI Assistant' })
    8) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-settings ">…</svg> aka getByRole('link', { name: 'Settings' })
    9) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dots-vertical ml-auto size-4">…</svg> aka getByRole('button', { name: 'F François Arbour farbour' })
    10) <svg width="24" height="24" fill="none" stroke-width="2" aria-hidden="true" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-panel-left" xmlns="http://www.w3.org/2000/svg">…</svg> aka getByRole('button', { name: 'Toggle Sidebar' })
    ...

Call log:
  - expect.toBeVisible with timeout 10000ms
  - waiting for locator('svg, canvas, .recharts-wrapper')

    at testChartRendering (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:386:23)
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-executive-summary.spec.ts:162:44
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Executive Summary":
      - /url: /executive-summary
  - main:
    - heading "Executive Summary" [level=1]
    - paragraph: Performance highlights for All Brands
    - group:
      - radio "Slide View" [checked]: Slides
      - radio "Grid View": Grid
    - button "Export PDF"
    - text: "Brand:"
    - button "Brand"
    - text: "Period:"
    - button "Month"
    - button "Quarter"
    - button "Year"
    - text: "Month:"
    - combobox: April 2025
    - text: "Currency:"
    - combobox: CAD
    - region "KPI Slides":
      - 'article "Slide 1: Total Revenue"':
        - heading "Total Revenue" [level=2]
        - paragraph: April 2025 • Slide 1 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$5,323,953 Current month value 30.6% vs Budget Budget comparison -12.8% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$21,554,437 Last Updated: 10:06 PM"
          - region "Chart Visualization":
            - text: 📊
            - heading "Chart Data Unavailable" [level=3]
            - paragraph: Time series data is not available for this KPI. The current value and comparisons are shown in the metrics panel.
        - text: Current period data only Scroll down for next KPI
        - img
      - 'article "Slide 2: Net Revenue"':
        - heading "Net Revenue" [level=2]
        - paragraph: April 2025 • Slide 2 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$4,454,872 Current month value 20.3% vs Budget Budget comparison -17.5% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$18,506,104 Last Updated: 10:06 PM"
          - region "Chart Visualization":
            - heading "Net Revenue Trend" [level=3]
            - paragraph: April 2025 • 30 data points
            - img: Apr 1 Apr 5 Apr 9 Apr 13 Apr 17 Apr 21 Apr 25 Apr 29 CA$0 CA$50,000 CA$100,000 CA$150,000 CA$200,000
            - text: "Data aggregated by day Total: CA$4,454,872"
        - text: 30 data points available Scroll down for next KPI
        - img
      - 'article "Slide 3: Gross Profit"':
        - heading "Gross Profit" [level=2]
        - paragraph: April 2025 • Slide 3 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$2,934,228 Current month value 56.4% vs Budget Budget comparison -15.2% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$11,848,239 Last Updated: 10:06 PM"
          - region "Chart Visualization":
            - text: 📊
            - heading "Chart Data Unavailable" [level=3]
            - paragraph: Time series data is not available for this KPI. The current value and comparisons are shown in the metrics panel.
        - text: Current period data only Scroll down for next KPI
        - img
      - 'article "Slide 4: Contribution Margin"':
        - heading "Contribution Margin" [level=2]
        - paragraph: April 2025 • Slide 4 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$4,032,120 Current month value 233.9% vs Budget Budget comparison -7.7% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$16,292,596 Last Updated: 10:06 PM"
          - region "Chart Visualization":
            - heading "Contribution Margin Trend" [level=3]
            - paragraph: April 2025 • 30 data points
            - img: Apr 1 Apr 5 Apr 9 Apr 13 Apr 17 Apr 21 Apr 25 Apr 29 CA$0 CA$40,000 CA$80,000 CA$120,000 CA$160,000
            - text: "Data aggregated by day Total: CA$4,032,120"
        - text: 30 data points available Scroll down for next KPI
        - img
      - 'article "Slide 5: Adspend"':
        - heading "Adspend" [level=2]
        - paragraph: April 2025 • Slide 5 of 5
        - text: Live Data
        - main "KPI Content":
          - region "Primary Metrics": "CA$1,097,892 Current month value 16.8% vs Budget Budget comparison 20.7% vs Last Year Year-over-year growth Period: April 2025 YTD: CA$4,444,358 Last Updated: 10:06 PM"
          - region "Chart Visualization":
            - heading "Adspend Trend" [level=3]
            - paragraph: April 2025 • 30 data points
            - img: Apr 1 Apr 5 Apr 9 Apr 13 Apr 17 Apr 21 Apr 25 Apr 29 CA$0 CA$15,000 CA$30,000 CA$45,000 CA$60,000
            - text: "Data aggregated by day Total: CA$1,097,892"
        - text: 30 data points available
      - 'button "Go to slide 1: Total Revenue"'
      - 'button "Go to slide 2: Net Revenue"'
      - 'button "Go to slide 3: Gross Profit"'
      - 'button "Go to slide 4: Contribution Margin"'
      - 'button "Go to slide 5: Adspend"'
      - text: Scrollable slide view with 5 KPI slides
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
  286 |
  287 |   for (const selector of mainSelectors) {
  288 |     const element = page.locator(selector).first();
  289 |     if (await element.isVisible()) {
  290 |       return element;
  291 |     }
  292 |   }
  293 |
  294 |   // If none found, return the first main element
  295 |   return page.locator('main').first();
  296 | }
  297 |
  298 | /**
  299 |  * Get navigation link with specific text (avoiding duplicates)
  300 |  */
  301 | export async function getNavLink(page: Page, linkText: string) {
  302 |   // Try different navigation selectors to find the right link
  303 |   const navSelectors = [
  304 |     `nav a[href*="${linkText.toLowerCase().replace(' ', '-')}"]`,
  305 |     `nav a[title="${linkText}"]`,
  306 |     `nav a:has-text("${linkText}")`,
  307 |     `a[href*="${linkText.toLowerCase().replace(' ', '-')}"]`
  308 |   ];
  309 |
  310 |   for (const selector of navSelectors) {
  311 |     const links = page.locator(selector);
  312 |     const count = await links.count();
  313 |
  314 |     if (count === 1) {
  315 |       return links.first();
  316 |     } else if (count > 1) {
  317 |       // If multiple links, try to find the most relevant one
  318 |       for (let i = 0; i < count; i++) {
  319 |         const link = links.nth(i);
  320 |         const href = await link.getAttribute('href');
  321 |         const title = await link.getAttribute('title');
  322 |
  323 |         // Prefer links with exact href match or title match
  324 |         if (href?.includes(linkText.toLowerCase().replace(' ', '-')) || title === linkText) {
  325 |           return link;
  326 |         }
  327 |       }
  328 |       // Return first if no exact match
  329 |       return links.first();
  330 |     }
  331 |   }
  332 |
  333 |   // Fallback to first matching link
  334 |   return page.locator(`nav a:has-text("${linkText}")`).first();
  335 | }
  336 |
  337 | /**
  338 |  * Test API endpoint response
  339 |  */
  340 | export async function testApiEndpoint(page: Page, endpoint: string, expectedStatus = 200) {
  341 |   const response = await page.request.get(endpoint);
  342 |   expect(response.status()).toBe(expectedStatus);
  343 |   return response;
  344 | }
  345 |
  346 | /**
  347 |  * Test form submission
  348 |  */
  349 | export async function testFormSubmission(page: Page, formSelector: string, formData: Record<string, string>) {
  350 |   const form = page.locator(formSelector);
  351 |   await expect(form).toBeVisible();
  352 |
  353 |   // Fill form fields
  354 |   for (const [field, value] of Object.entries(formData)) {
  355 |     await page.fill(`${formSelector} input[name="${field}"], ${formSelector} select[name="${field}"]`, value);
  356 |   }
  357 |
  358 |   // Submit form
  359 |   await page.click(`${formSelector} button[type="submit"]`);
  360 | }
  361 |
  362 | /**
  363 |  * Test data table functionality
  364 |  */
  365 | export async function testDataTable(page: Page, tableSelector = 'table') {
  366 |   const table = page.locator(tableSelector);
  367 |   await expect(table).toBeVisible();
  368 |
  369 |   // Check for headers
  370 |   const headers = table.locator('thead th');
  371 |   const headerCount = await headers.count();
  372 |   expect(headerCount).toBeGreaterThan(0);
  373 |
  374 |   // Check for data rows
  375 |   const rows = table.locator('tbody tr');
  376 |   const rowCount = await rows.count();
  377 |
  378 |   return { headerCount, rowCount };
  379 | }
  380 |
  381 | /**
  382 |  * Test chart rendering
  383 |  */
  384 | export async function testChartRendering(page: Page, chartSelector = 'svg, canvas, .recharts-wrapper') {
  385 |   const chart = page.locator(chartSelector);
> 386 |   await expect(chart).toBeVisible({ timeout: 10000 });
      |                       ^ Error: expect.toBeVisible: Error: strict mode violation: locator('svg, canvas, .recharts-wrapper') resolved to 37 elements:
  387 |
  388 |   // Wait for chart to fully render
  389 |   await page.waitForTimeout(2000);
  390 |
  391 |   return chart;
  392 | }
  393 |
  394 | /**
  395 |  * Test filter functionality
  396 |  */
  397 | export async function testFilters(page: Page) {
  398 |   // Look for common filter elements
  399 |   const filterElements = [
  400 |     'select[name*="brand"]',
  401 |     'input[type="date"]',
  402 |     'select[name*="currency"]',
  403 |     'select[name*="country"]',
  404 |     '[data-testid*="filter"]'
  405 |   ];
  406 |
  407 |   const activeFilters = [];
  408 |
  409 |   for (const selector of filterElements) {
  410 |     const element = page.locator(selector);
  411 |     if (await element.isVisible()) {
  412 |       activeFilters.push(selector);
  413 |     }
  414 |   }
  415 |
  416 |   return activeFilters;
  417 | }
  418 |
  419 | /**
  420 |  * Test pagination if present
  421 |  */
  422 | export async function testPagination(page: Page) {
  423 |   const pagination = page.locator('[data-testid="pagination"], .pagination, nav[aria-label*="pagination"]');
  424 |
  425 |   if (await pagination.isVisible()) {
  426 |     const nextButton = pagination.locator('button:has-text("Next"), button[aria-label*="next"]');
  427 |     const prevButton = pagination.locator('button:has-text("Previous"), button[aria-label*="previous"]');
  428 |
  429 |     return {
  430 |       hasPagination: true,
  431 |       hasNext: await nextButton.isVisible(),
  432 |       hasPrevious: await prevButton.isVisible()
  433 |     };
  434 |   }
  435 |
  436 |   return { hasPagination: false };
  437 | }
  438 |
  439 | /**
  440 |  * Test search functionality
  441 |  */
  442 | export async function testSearch(page: Page, searchTerm: string) {
  443 |   const searchInput = page.locator('input[type="search"], input[placeholder*="search"], input[name*="search"]');
  444 |
  445 |   if (await searchInput.isVisible()) {
  446 |     await searchInput.fill(searchTerm);
  447 |     await page.keyboard.press('Enter');
  448 |     await page.waitForTimeout(1000);
  449 |     return true;
  450 |   }
  451 |
  452 |   return false;
  453 | }
  454 |
  455 | /**
  456 |  * Test modal/dialog functionality
  457 |  */
  458 | export async function testModal(page: Page, triggerSelector: string) {
  459 |   await page.click(triggerSelector);
  460 |
  461 |   const modal = page.locator('[role="dialog"], .modal, [data-testid*="modal"]');
  462 |   await expect(modal).toBeVisible();
  463 |
  464 |   // Test close functionality
  465 |   const closeButton = modal.locator('button[aria-label*="close"], button:has-text("Close"), [data-testid*="close"]');
  466 |   if (await closeButton.isVisible()) {
  467 |     await closeButton.click();
  468 |     await expect(modal).not.toBeVisible();
  469 |   }
  470 | }
  471 |
  472 | /**
  473 |  * Test loading states
  474 |  */
  475 | export async function waitForLoadingToComplete(page: Page) {
  476 |   // Wait for common loading indicators to disappear
  477 |   const loadingSelectors = [
  478 |     '.loading',
  479 |     '.spinner',
  480 |     '[data-testid*="loading"]',
  481 |     '.animate-spin',
  482 |     'text="Loading"'
  483 |   ];
  484 |
  485 |   for (const selector of loadingSelectors) {
  486 |     try {
```