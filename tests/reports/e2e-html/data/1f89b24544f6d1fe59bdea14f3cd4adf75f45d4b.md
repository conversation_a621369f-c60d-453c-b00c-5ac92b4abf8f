# Test info

- Name: Comprehensive Brand Deep Dive Testing >> should be responsive across different screen sizes
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:246:7

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role="main"]') resolved to 2 elements:
    1) <main data-slot="sidebar-inset" class="bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2">…</main> aka getByText('Toggle SidebarDashboardBrand Deep DiveBrand Deep DiveDetailed analysis of brand')
    2) <main class="flex-1 overflow-auto">…</main> aka getByRole('main').filter({ hasText: 'Toggle SidebarDashboardBrand' }).getByRole('main')

Call log:
  - expect.toBeVisible with timeout 15000ms
  - waiting for locator('main, [role="main"]')

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-brand-deep-dive.spec.ts:261:57
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
  - main:
    - heading "Brand Deep Dive" [level=1]
    - heading "Detailed analysis of brand performance metrics" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Week
    - text: Data Filters
    - button "Brand"
    - button "Countries"
    - button "Sales Channels"
    - text: 📊
    - heading "No Brand Selected" [level=2]
    - paragraph: Please select a brand from the dropdown above to view detailed performance metrics and analysis.
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
  161 |     }
  162 |
  163 |     // Test currency filter if present
  164 |     const currencyFilter = page.locator('select[name*="currency"], [data-testid*="currency"]');
  165 |     if (await currencyFilter.isVisible()) {
  166 |       await currencyFilter.click();
  167 |       await page.waitForTimeout(500);
  168 |       
  169 |       const currencyOptions = page.locator('option, [role="option"]');
  170 |       const optionCount = await currencyOptions.count();
  171 |       
  172 |       if (optionCount > 1) {
  173 |         await currencyOptions.nth(1).click();
  174 |         await page.waitForTimeout(2000);
  175 |         await waitForLoadingToComplete(page);
  176 |         await takeScreenshot(page, 'brand-deep-dive-currency-filter');
  177 |       }
  178 |     }
  179 |   });
  180 |
  181 |   test('should display tabs and allow tab navigation', async ({ page }) => {
  182 |     await page.goto('/brand-deep-dive');
  183 |     await waitForPageLoad(page);
  184 |     await waitForLoadingToComplete(page);
  185 |
  186 |     // Look for tab navigation
  187 |     const tabs = page.locator('[role="tab"], .tab, [data-testid*="tab"]');
  188 |     const tabCount = await tabs.count();
  189 |
  190 |     if (tabCount > 1) {
  191 |       console.log(`Found ${tabCount} tabs`);
  192 |       
  193 |       // Test clicking through tabs
  194 |       for (let i = 0; i < Math.min(tabCount, 3); i++) {
  195 |         const tab = tabs.nth(i);
  196 |         if (await tab.isVisible()) {
  197 |           await tab.click();
  198 |           await page.waitForTimeout(1000);
  199 |           await waitForLoadingToComplete(page);
  200 |           
  201 |           const tabText = await tab.textContent();
  202 |           await takeScreenshot(page, `brand-deep-dive-tab-${i}-${tabText?.toLowerCase().replace(/\s+/g, '-')}`);
  203 |         }
  204 |       }
  205 |     }
  206 |   });
  207 |
  208 |   test('should handle brand comparison if available', async ({ page }) => {
  209 |     await page.goto('/brand-deep-dive');
  210 |     await waitForPageLoad(page);
  211 |     await waitForLoadingToComplete(page);
  212 |
  213 |     // Look for brand comparison features
  214 |     const compareButton = page.locator('button:has-text("Compare"), [data-testid*="compare"]');
  215 |     
  216 |     if (await compareButton.isVisible()) {
  217 |       await compareButton.click();
  218 |       await page.waitForTimeout(1000);
  219 |       
  220 |       // Look for comparison interface
  221 |       const comparisonInterface = page.locator('[data-testid*="comparison"], .comparison');
  222 |       if (await comparisonInterface.isVisible()) {
  223 |         await takeScreenshot(page, 'brand-deep-dive-comparison');
  224 |       }
  225 |     }
  226 |   });
  227 |
  228 |   test('should handle export functionality if available', async ({ page }) => {
  229 |     await page.goto('/brand-deep-dive');
  230 |     await waitForPageLoad(page);
  231 |     await waitForLoadingToComplete(page);
  232 |
  233 |     // Look for export buttons
  234 |     const exportButtons = page.locator('button:has-text("Export"), button:has-text("Download"), [data-testid*="export"]');
  235 |     const exportCount = await exportButtons.count();
  236 |
  237 |     if (exportCount > 0) {
  238 |       console.log(`Found ${exportCount} export options`);
  239 |       
  240 |       // Test export button visibility (don't actually download)
  241 |       await expect(exportButtons.first()).toBeVisible();
  242 |       await takeScreenshot(page, 'brand-deep-dive-export-options');
  243 |     }
  244 |   });
  245 |
  246 |   test('should be responsive across different screen sizes', async ({ page }) => {
  247 |     await page.goto('/brand-deep-dive');
  248 |     await waitForPageLoad(page);
  249 |
  250 |     const viewports = [
  251 |       { width: 1920, height: 1080, name: 'desktop' },
  252 |       { width: 1024, height: 768, name: 'tablet' },
  253 |       { width: 375, height: 667, name: 'mobile' }
  254 |     ];
  255 |
  256 |     for (const viewport of viewports) {
  257 |       await page.setViewportSize(viewport);
  258 |       await page.waitForTimeout(1000);
  259 |       
  260 |       // Verify main elements are still visible and properly arranged
> 261 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
      |                                                         ^ Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role="main"]') resolved to 2 elements:
  262 |       
  263 |       // Check if mobile menu appears on small screens
  264 |       if (viewport.width <= 768) {
  265 |         const mobileMenu = page.locator('[data-testid*="mobile-menu"], .mobile-menu, button[aria-label*="menu"]');
  266 |         if (await mobileMenu.isVisible()) {
  267 |           await mobileMenu.click();
  268 |           await page.waitForTimeout(500);
  269 |           await takeScreenshot(page, `brand-deep-dive-mobile-menu-${viewport.name}`);
  270 |         }
  271 |       }
  272 |       
  273 |       await takeScreenshot(page, `brand-deep-dive-responsive-${viewport.name}`);
  274 |     }
  275 |   });
  276 |
  277 |   test('should have proper accessibility features', async ({ page }) => {
  278 |     await page.goto('/brand-deep-dive');
  279 |     await waitForPageLoad(page);
  280 |
  281 |     const accessibilityInfo = await testAccessibility(page);
  282 |     
  283 |     // Verify minimum accessibility requirements
  284 |     expect(accessibilityInfo.headings).toBeGreaterThan(0);
  285 |     expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
  286 |     
  287 |     console.log('Brand Deep Dive accessibility info:', accessibilityInfo);
  288 |     await takeScreenshot(page, 'brand-deep-dive-accessibility');
  289 |   });
  290 |
  291 |   test('should load within performance thresholds', async ({ page }) => {
  292 |     const startTime = Date.now();
  293 |     
  294 |     await page.goto('/brand-deep-dive');
  295 |     await waitForPageLoad(page);
  296 |     await waitForLoadingToComplete(page);
  297 |     
  298 |     const loadTime = Date.now() - startTime;
  299 |     
  300 |     // Brand Deep Dive should load within 15 seconds (more complex page)
  301 |     expect(loadTime).toBeLessThan(15000);
  302 |     
  303 |     console.log(`Brand Deep Dive loaded in ${loadTime}ms`);
  304 |   });
  305 | });
  306 |
```