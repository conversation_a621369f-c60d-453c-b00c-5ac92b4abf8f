# Test info

- Name: Comprehensive Dashboard Testing >> should render charts correctly
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts:65:7

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('svg, canvas, .recharts-wrapper') resolved to 72 elements:
    1) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-inner-shadow-top !size-5">…</svg> aka getByRole('link', { name: 'NOLK' })
    2) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dashboard ">…</svg> aka getByRole('listitem').filter({ hasText: /^Dashboard$/ }).getByRole('link')
    3) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-list-details ">…</svg> aka getByRole('link', { name: 'Brand Deep Dive' })
    4) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-chart-bar ">…</svg> aka getByRole('link', { name: 'Marketing Dashboard' })
    5) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-report ">…</svg> aka getByRole('link', { name: 'Executive Summary' })
    6) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-wallet ">…</svg> aka getByRole('link', { name: 'Budget' })
    7) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-file-ai ">…</svg> aka getByRole('link', { name: 'AI Assistant' })
    8) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-settings ">…</svg> aka getByRole('link', { name: 'Settings' })
    9) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dots-vertical ml-auto size-4">…</svg> aka getByRole('button', { name: 'F François Arbour farbour' })
    10) <svg width="24" height="24" fill="none" stroke-width="2" aria-hidden="true" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-panel-left" xmlns="http://www.w3.org/2000/svg">…</svg> aka getByRole('button', { name: 'Toggle Sidebar' })
    ...

Call log:
  - expect.toBeVisible with timeout 10000ms
  - waiting for locator('svg, canvas, .recharts-wrapper')

    at testChartRendering (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:313:23)
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-dashboard.spec.ts:71:44
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Dashboard":
      - /url: /dashboard
  - main:
    - heading "Dashboard" [level=1]
    - heading "Overview of your business performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Countries"
    - button "Sales Channels"
    - text: Master KPI Comparison Compare 'Gross Revenue' across selected brands using a line chart.
    - combobox "Select KPI": Gross Revenue
    - img: 2/28/2025 3/31/2025 4/30/2025 0 550K 1M 2M 2M
    - text: Brands Click to toggle. Sorted by latest KPI.
    - list:
      - button "Toggle visibility for Revant" [pressed]: Revant
      - button "Toggle visibility for Qalo" [pressed]: Qalo
      - button "Toggle visibility for Ergonofis" [pressed]: Ergonofis
      - button "Toggle visibility for Opposite Wall" [pressed]: Opposite Wall
      - button "Toggle visibility for Homesick" [pressed]: Homesick
      - button "Toggle visibility for Gravity" [pressed]: Gravity
      - button "Toggle visibility for Freakmount" [pressed]: Freakmount
      - button "Toggle visibility for MiHIGH" [pressed]: MiHIGH
      - button "Toggle visibility for Rose Boreal" [pressed]: Rose Boreal
      - button "Toggle visibility for Rachel" [pressed]: Rachel
      - button "Toggle visibility for Kana" [pressed]: Kana
      - button "Toggle visibility for Arctic Tumblers" [pressed]: Arctic Tumblers
      - button "Toggle visibility for Loctote" [pressed]: Loctote
      - button "Toggle visibility for Proper Pour" [pressed]: Proper Pour
      - button "Toggle visibility for Love Your Melon" [pressed]: Love Your Melon
      - button "Toggle visibility for Wolf & Grizzly" [pressed]: Wolf & Grizzly
      - button "Toggle visibility for Go Green" [pressed]: Go Green
      - button "Toggle visibility for Corretto" [pressed]: Corretto
      - button "Toggle visibility for Alex Bottle" [pressed]: Alex Bottle
    - heading "Key Performance Indicators" [level=3]
    - text: Revenue
    - button "KPI Definition"
    - text: Discount
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: Refunds
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: Net Revenue
    - button "KPI Definition"
    - text: Landed Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: Fulfillment Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: Transaction Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: Gross Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: Adspend
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: Contribution Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
  213 | }
  214 |
  215 | /**
  216 |  * Verify user has admin privileges
  217 |  */
  218 | export async function verifyAdminAccess(page: Page): Promise<boolean> {
  219 |   try {
  220 |     await page.goto('/admin');
  221 |     await waitForPageLoad(page);
  222 |
  223 |     // Check if we're still on admin page (not redirected to sign-in)
  224 |     const currentUrl = page.url();
  225 |     return currentUrl.includes('/admin') && !currentUrl.includes('/auth/signin');
  226 |   } catch {
  227 |     return false;
  228 |   }
  229 | }
  230 |
  231 | /**
  232 |  * Test navigation through all main sections
  233 |  */
  234 | export async function testMainNavigation(page: Page) {
  235 |   const sections = [
  236 |     { path: '/dashboard', name: 'Dashboard' },
  237 |     { path: '/marketing', name: 'Marketing' },
  238 |     { path: '/admin', name: 'Admin' }
  239 |   ];
  240 |
  241 |   for (const section of sections) {
  242 |     await page.goto(section.path);
  243 |     await waitForPageLoad(page);
  244 |
  245 |     // Verify we reached the intended page
  246 |     const currentUrl = page.url();
  247 |     expect(currentUrl).toContain(section.path);
  248 |
  249 |     // Take screenshot for visual verification
  250 |     await takeScreenshot(page, `navigation-${section.name.toLowerCase()}`);
  251 |   }
  252 | }
  253 |
  254 | /**
  255 |  * Verify all expected UI elements are present on a page
  256 |  */
  257 | export async function verifyPageElements(page: Page, expectedElements: string[]) {
  258 |   for (const element of expectedElements) {
  259 |     const locator = page.locator(element).first();
  260 |     await expect(locator).toBeVisible({ timeout: 5000 });
  261 |   }
  262 | }
  263 |
  264 | /**
  265 |  * Test API endpoint response
  266 |  */
  267 | export async function testApiEndpoint(page: Page, endpoint: string, expectedStatus = 200) {
  268 |   const response = await page.request.get(endpoint);
  269 |   expect(response.status()).toBe(expectedStatus);
  270 |   return response;
  271 | }
  272 |
  273 | /**
  274 |  * Test form submission
  275 |  */
  276 | export async function testFormSubmission(page: Page, formSelector: string, formData: Record<string, string>) {
  277 |   const form = page.locator(formSelector);
  278 |   await expect(form).toBeVisible();
  279 |
  280 |   // Fill form fields
  281 |   for (const [field, value] of Object.entries(formData)) {
  282 |     await page.fill(`${formSelector} input[name="${field}"], ${formSelector} select[name="${field}"]`, value);
  283 |   }
  284 |
  285 |   // Submit form
  286 |   await page.click(`${formSelector} button[type="submit"]`);
  287 | }
  288 |
  289 | /**
  290 |  * Test data table functionality
  291 |  */
  292 | export async function testDataTable(page: Page, tableSelector = 'table') {
  293 |   const table = page.locator(tableSelector);
  294 |   await expect(table).toBeVisible();
  295 |
  296 |   // Check for headers
  297 |   const headers = table.locator('thead th');
  298 |   const headerCount = await headers.count();
  299 |   expect(headerCount).toBeGreaterThan(0);
  300 |
  301 |   // Check for data rows
  302 |   const rows = table.locator('tbody tr');
  303 |   const rowCount = await rows.count();
  304 |
  305 |   return { headerCount, rowCount };
  306 | }
  307 |
  308 | /**
  309 |  * Test chart rendering
  310 |  */
  311 | export async function testChartRendering(page: Page, chartSelector = 'svg, canvas, .recharts-wrapper') {
  312 |   const chart = page.locator(chartSelector);
> 313 |   await expect(chart).toBeVisible({ timeout: 10000 });
      |                       ^ Error: expect.toBeVisible: Error: strict mode violation: locator('svg, canvas, .recharts-wrapper') resolved to 72 elements:
  314 |
  315 |   // Wait for chart to fully render
  316 |   await page.waitForTimeout(2000);
  317 |
  318 |   return chart;
  319 | }
  320 |
  321 | /**
  322 |  * Test filter functionality
  323 |  */
  324 | export async function testFilters(page: Page) {
  325 |   // Look for common filter elements
  326 |   const filterElements = [
  327 |     'select[name*="brand"]',
  328 |     'input[type="date"]',
  329 |     'select[name*="currency"]',
  330 |     'select[name*="country"]',
  331 |     '[data-testid*="filter"]'
  332 |   ];
  333 |
  334 |   const activeFilters = [];
  335 |
  336 |   for (const selector of filterElements) {
  337 |     const element = page.locator(selector);
  338 |     if (await element.isVisible()) {
  339 |       activeFilters.push(selector);
  340 |     }
  341 |   }
  342 |
  343 |   return activeFilters;
  344 | }
  345 |
  346 | /**
  347 |  * Test pagination if present
  348 |  */
  349 | export async function testPagination(page: Page) {
  350 |   const pagination = page.locator('[data-testid="pagination"], .pagination, nav[aria-label*="pagination"]');
  351 |
  352 |   if (await pagination.isVisible()) {
  353 |     const nextButton = pagination.locator('button:has-text("Next"), button[aria-label*="next"]');
  354 |     const prevButton = pagination.locator('button:has-text("Previous"), button[aria-label*="previous"]');
  355 |
  356 |     return {
  357 |       hasPagination: true,
  358 |       hasNext: await nextButton.isVisible(),
  359 |       hasPrevious: await prevButton.isVisible()
  360 |     };
  361 |   }
  362 |
  363 |   return { hasPagination: false };
  364 | }
  365 |
  366 | /**
  367 |  * Test search functionality
  368 |  */
  369 | export async function testSearch(page: Page, searchTerm: string) {
  370 |   const searchInput = page.locator('input[type="search"], input[placeholder*="search"], input[name*="search"]');
  371 |
  372 |   if (await searchInput.isVisible()) {
  373 |     await searchInput.fill(searchTerm);
  374 |     await page.keyboard.press('Enter');
  375 |     await page.waitForTimeout(1000);
  376 |     return true;
  377 |   }
  378 |
  379 |   return false;
  380 | }
  381 |
  382 | /**
  383 |  * Test modal/dialog functionality
  384 |  */
  385 | export async function testModal(page: Page, triggerSelector: string) {
  386 |   await page.click(triggerSelector);
  387 |
  388 |   const modal = page.locator('[role="dialog"], .modal, [data-testid*="modal"]');
  389 |   await expect(modal).toBeVisible();
  390 |
  391 |   // Test close functionality
  392 |   const closeButton = modal.locator('button[aria-label*="close"], button:has-text("Close"), [data-testid*="close"]');
  393 |   if (await closeButton.isVisible()) {
  394 |     await closeButton.click();
  395 |     await expect(modal).not.toBeVisible();
  396 |   }
  397 | }
  398 |
  399 | /**
  400 |  * Test loading states
  401 |  */
  402 | export async function waitForLoadingToComplete(page: Page) {
  403 |   // Wait for common loading indicators to disappear
  404 |   const loadingSelectors = [
  405 |     '.loading',
  406 |     '.spinner',
  407 |     '[data-testid*="loading"]',
  408 |     '.animate-spin',
  409 |     'text="Loading"'
  410 |   ];
  411 |
  412 |   for (const selector of loadingSelectors) {
  413 |     try {
```