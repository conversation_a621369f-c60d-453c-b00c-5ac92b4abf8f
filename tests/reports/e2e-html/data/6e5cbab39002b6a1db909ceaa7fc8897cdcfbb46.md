# Test info

- Name: Comprehensive Application Test Suite >> should run final comprehensive validation
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:317:7

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role="main"]') resolved to 2 elements:
    1) <main data-slot="sidebar-inset" class="bg-background relative flex w-full flex-1 flex-col md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2">…</main> aka getByText('Toggle SidebarDashboardBrand Deep DiveBrand Deep DiveDetailed analysis of brand')
    2) <main class="flex-1 overflow-auto">…</main> aka getByRole('main').filter({ hasText: 'Toggle SidebarDashboardBrand' }).getByRole('main')

Call log:
  - expect.toBeVisible with timeout 15000ms
  - waiting for locator('main, [role="main"]')

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:339:57
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
  - main:
    - heading "Brand Deep Dive" [level=1]
    - heading "Detailed analysis of brand performance metrics" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Week
    - text: Data Filters
    - button "Brand"
    - button "Countries"
    - button "Sales Channels"
    - text: 📊
    - heading "No Brand Selected" [level=2]
    - paragraph: Please select a brand from the dropdown above to view detailed performance metrics and analysis.
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
  239 |     await page.goto('/dashboard');
  240 |     await waitForPageLoad(page);
  241 |     
  242 |     for (const viewport of viewports) {
  243 |       await page.setViewportSize(viewport);
  244 |       await page.waitForTimeout(1000);
  245 |       
  246 |       // Verify main content is visible
  247 |       const mainContent = page.locator('main, [role="main"]');
  248 |       const isVisible = await mainContent.isVisible();
  249 |       
  250 |       if (isVisible) {
  251 |         console.log(`✅ ${viewport.name} (${viewport.width}x${viewport.height}): Layout working`);
  252 |       } else {
  253 |         console.log(`❌ ${viewport.name}: Layout broken`);
  254 |       }
  255 |       
  256 |       await takeScreenshot(page, `responsive-${viewport.name.toLowerCase()}`);
  257 |     }
  258 |   });
  259 |
  260 |   test('should verify accessibility standards', async ({ page }) => {
  261 |     console.log('♿ Testing accessibility standards...');
  262 |     
  263 |     const accessibilityPages = ['/dashboard', '/marketing-dashboard', '/admin'];
  264 |     
  265 |     for (const url of accessibilityPages) {
  266 |       await page.goto(url);
  267 |       await waitForPageLoad(page);
  268 |       
  269 |       const accessibilityInfo = await testAccessibility(page);
  270 |       
  271 |       console.log(`🔍 ${url} accessibility:`);
  272 |       console.log(`  - Headings: ${accessibilityInfo.headings}`);
  273 |       console.log(`  - ARIA labels: ${accessibilityInfo.ariaLabels}`);
  274 |       console.log(`  - Landmarks: ${accessibilityInfo.landmarks}`);
  275 |       
  276 |       // Verify minimum accessibility requirements
  277 |       expect(accessibilityInfo.headings).toBeGreaterThan(0);
  278 |       expect(accessibilityInfo.landmarks).toBeGreaterThan(0);
  279 |       
  280 |       console.log(`✅ ${url}: Accessibility standards met`);
  281 |     }
  282 |     
  283 |     await takeScreenshot(page, 'accessibility-test');
  284 |   });
  285 |
  286 |   test('should verify performance benchmarks', async ({ page }) => {
  287 |     console.log('⚡ Testing performance benchmarks...');
  288 |     
  289 |     const performancePages = [
  290 |       { name: 'Dashboard', url: '/dashboard', maxTime: 8000 },
  291 |       { name: 'Marketing Dashboard', url: '/marketing-dashboard', maxTime: 15000 },
  292 |       { name: 'Admin Dashboard', url: '/admin', maxTime: 8000 }
  293 |     ];
  294 |     
  295 |     for (const perfPage of performancePages) {
  296 |       const startTime = Date.now();
  297 |       
  298 |       await page.goto(perfPage.url);
  299 |       await waitForPageLoad(page);
  300 |       await waitForLoadingToComplete(page);
  301 |       
  302 |       const loadTime = Date.now() - startTime;
  303 |       
  304 |       if (loadTime < perfPage.maxTime) {
  305 |         console.log(`✅ ${perfPage.name}: ${loadTime}ms (under ${perfPage.maxTime}ms limit)`);
  306 |       } else {
  307 |         console.log(`⚠️ ${perfPage.name}: ${loadTime}ms (over ${perfPage.maxTime}ms limit)`);
  308 |       }
  309 |       
  310 |       // Don't fail the test for performance, just log
  311 |       // expect(loadTime).toBeLessThan(perfPage.maxTime);
  312 |     }
  313 |     
  314 |     await takeScreenshot(page, 'performance-test');
  315 |   });
  316 |
  317 |   test('should run final comprehensive validation', async ({ page }) => {
  318 |     console.log('🎯 Running final comprehensive validation...');
  319 |     
  320 |     // Test critical user journey
  321 |     await page.goto('/dashboard');
  322 |     await waitForPageLoad(page);
  323 |     await waitForLoadingToComplete(page);
  324 |     
  325 |     // Navigate through key pages
  326 |     const journey = [
  327 |       '/brand-deep-dive',
  328 |       '/marketing-dashboard', 
  329 |       '/executive-summary',
  330 |       '/admin'
  331 |     ];
  332 |     
  333 |     for (const url of journey) {
  334 |       await page.goto(url);
  335 |       await waitForPageLoad(page);
  336 |       await waitForLoadingToComplete(page);
  337 |       
  338 |       // Verify page loaded without errors
> 339 |       await expect(page.locator('main, [role="main"]')).toBeVisible();
      |                                                         ^ Error: expect.toBeVisible: Error: strict mode violation: locator('main, [role="main"]') resolved to 2 elements:
  340 |       
  341 |       console.log(`✅ Journey step: ${url} completed successfully`);
  342 |     }
  343 |     
  344 |     // Return to dashboard
  345 |     await page.goto('/dashboard');
  346 |     await waitForPageLoad(page);
  347 |     
  348 |     console.log('🎉 Comprehensive test suite completed successfully!');
  349 |     await takeScreenshot(page, 'comprehensive-test-completed');
  350 |   });
  351 | });
  352 |
```