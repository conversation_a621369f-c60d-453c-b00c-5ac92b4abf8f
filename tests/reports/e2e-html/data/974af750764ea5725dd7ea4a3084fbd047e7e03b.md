# Test info

- Name: Comprehensive Budget Page Testing >> should display budget data and charts
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:41:7

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('svg, canvas, .recharts-wrapper') resolved to 13 elements:
    1) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-inner-shadow-top !size-5">…</svg> aka getByRole('link', { name: 'NOLK' })
    2) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dashboard ">…</svg> aka getByRole('listitem').filter({ hasText: /^Dashboard$/ }).getByRole('link')
    3) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-list-details ">…</svg> aka getByRole('link', { name: 'Brand Deep Dive' })
    4) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-chart-bar ">…</svg> aka getByRole('link', { name: 'Marketing Dashboard' })
    5) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-report ">…</svg> aka getByRole('link', { name: 'Executive Summary' })
    6) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-wallet ">…</svg> aka getByRole('listitem').filter({ hasText: 'Budget' }).getByRole('link')
    7) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-file-ai ">…</svg> aka getByRole('link', { name: 'AI Assistant' })
    8) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-settings ">…</svg> aka getByRole('link', { name: 'Settings' })
    9) <svg width="24" height="24" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" class="tabler-icon tabler-icon-dots-vertical ml-auto size-4">…</svg> aka getByRole('button', { name: 'F François Arbour farbour' })
    10) <svg width="24" height="24" fill="none" stroke-width="2" aria-hidden="true" viewBox="0 0 24 24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-panel-left" xmlns="http://www.w3.org/2000/svg">…</svg> aka getByRole('button', { name: 'Toggle Sidebar' })
    ...

Call log:
  - expect.toBeVisible with timeout 10000ms
  - waiting for locator('svg, canvas, .recharts-wrapper')

    at testChartRendering (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:313:23)
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-budget-ai.spec.ts:53:44
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Budget":
      - /url: /budget
  - main:
    - heading "Budget Data" [level=1]
    - heading "Manage and view budget allocations" [level=2]
    - text: Budget Data
    - combobox: Select Brand
    - button "Import from CSV"
    - table:
      - rowgroup:
        - row "Month B2B Content Contribution Margin D2C Marketing Deferred Revenue Direct to Customer Fulfillment Fees Gross Margin Gross Revenue Landed Costs Marketplace Net Revenue Other Revenue Paid Marketing Marketplaces Pay-Per-Click Transaction Fees":
          - cell "Month"
          - cell "B2B"
          - cell "Content"
          - cell "Contribution Margin"
          - cell "D2C Marketing"
          - cell "Deferred Revenue"
          - cell "Direct to Customer"
          - cell "Fulfillment Fees"
          - cell "Gross Margin"
          - cell "Gross Revenue"
          - cell "Landed Costs"
          - cell "Marketplace"
          - cell "Net Revenue"
          - cell "Other Revenue"
          - cell "Paid Marketing Marketplaces"
          - cell "Pay-Per-Click"
          - cell "Transaction Fees"
      - rowgroup:
        - row "Jan-25 $112,325.84 $75,518.36 $783,333.61 $138,920.66 $12,608.16 $2,307,787.18 $617,188.37 $1,324,380.72 $3,428,989.67 $860,263.91 $996,268.49 $3,027,928.02 - $85,613.86 $379,914.88 $226,095.03":
          - cell "Jan-25"
          - cell "$112,325.84"
          - cell "$75,518.36"
          - cell "$783,333.61"
          - cell "$138,920.66"
          - cell "$12,608.16"
          - cell "$2,307,787.18"
          - cell "$617,188.37"
          - cell "$1,324,380.72"
          - cell "$3,428,989.67"
          - cell "$860,263.91"
          - cell "$996,268.49"
          - cell "$3,027,928.02"
          - cell "-"
          - cell "$85,613.86"
          - cell "$379,914.88"
          - cell "$226,095.03"
        - row "Feb-25 $115,493.38 $98,895.29 $690,342.85 $170,073.97 $108,093.02 $2,006,121.06 $462,780.30 $1,226,691.56 $3,043,169.21 $793,026.84 $811,821.86 $2,668,832.30 $1,639.89 $96,427.86 $341,025.55 $186,333.59":
          - cell "Feb-25"
          - cell "$115,493.38"
          - cell "$98,895.29"
          - cell "$690,342.85"
          - cell "$170,073.97"
          - cell "$108,093.02"
          - cell "$2,006,121.06"
          - cell "$462,780.30"
          - cell "$1,226,691.56"
          - cell "$3,043,169.21"
          - cell "$793,026.84"
          - cell "$811,821.86"
          - cell "$2,668,832.30"
          - cell "$1,639.89"
          - cell "$96,427.86"
          - cell "$341,025.55"
          - cell "$186,333.59"
        - row "Mar-25 $70,622.87 $134,650.79 $916,948.79 $232,856.18 -$138,993.00 $2,665,060.15 $542,930.94 $1,567,558.89 $3,591,946.88 $889,279.02 $995,256.85 $3,226,473.38 - $96,094.25 $419,865.06 $226,204.52":
          - cell "Mar-25"
          - cell "$70,622.87"
          - cell "$134,650.79"
          - cell "$916,948.79"
          - cell "$232,856.18"
          - cell "-$138,993.00"
          - cell "$2,665,060.15"
          - cell "$542,930.94"
          - cell "$1,567,558.89"
          - cell "$3,591,946.88"
          - cell "$889,279.02"
          - cell "$995,256.85"
          - cell "$3,226,473.38"
          - cell "-"
          - cell "$96,094.25"
          - cell "$419,865.06"
          - cell "$226,204.52"
        - row "Apr-25 $66,882.93 $100,364.92 $1,207,656.08 $272,263.07 $51,919.00 $2,885,619.40 $600,355.92 $1,875,743.43 $4,077,007.95 $972,456.73 $1,072,586.61 $3,703,230.33 - $104,910.91 $462,811.53 $254,174.25":
          - cell "Apr-25"
          - cell "$66,882.93"
          - cell "$100,364.92"
          - cell "$1,207,656.08"
          - cell "$272,263.07"
          - cell "$51,919.00"
          - cell "$2,885,619.40"
          - cell "$600,355.92"
          - cell "$1,875,743.43"
          - cell "$4,077,007.95"
          - cell "$972,456.73"
          - cell "$1,072,586.61"
          - cell "$3,703,230.33"
          - cell "-"
          - cell "$104,910.91"
          - cell "$462,811.53"
          - cell "$254,174.25"
        - row "May-25 $79,499.10 $101,791.79 $1,433,805.32 $354,449.75 $25,221.00 $3,370,088.55 $696,336.49 $2,202,337.75 $4,773,975.91 $1,088,181.04 $1,299,167.24 $4,293,683.40 - $129,206.08 $537,534.56 $306,328.11":
          - cell "May-25"
          - cell "$79,499.10"
          - cell "$101,791.79"
          - cell "$1,433,805.32"
          - cell "$354,449.75"
          - cell "$25,221.00"
          - cell "$3,370,088.55"
          - cell "$696,336.49"
          - cell "$2,202,337.75"
          - cell "$4,773,975.91"
          - cell "$1,088,181.04"
          - cell "$1,299,167.24"
          - cell "$4,293,683.40"
          - cell "-"
          - cell "$129,206.08"
          - cell "$537,534.56"
          - cell "$306,328.11"
        - row "Jun-25 $137,253.65 $91,039.34 $1,525,134.26 $336,638.12 -$147,869.00 $3,670,863.00 $696,947.37 $2,288,363.13 $4,927,494.95 $1,176,024.81 $1,267,247.30 $4,471,309.83 - $127,993.18 $544,196.36 $309,474.54":
          - cell "Jun-25"
          - cell "$137,253.65"
          - cell "$91,039.34"
          - cell "$1,525,134.26"
          - cell "$336,638.12"
          - cell "-$147,869.00"
          - cell "$3,670,863.00"
          - cell "$696,947.37"
          - cell "$2,288,363.13"
          - cell "$4,927,494.95"
          - cell "$1,176,024.81"
          - cell "$1,267,247.30"
          - cell "$4,471,309.83"
          - cell "-"
          - cell "$127,993.18"
          - cell "$544,196.36"
          - cell "$309,474.54"
        - row "Jul-25 $152,148.84 $126,828.09 $1,605,563.03 $375,857.72 $133,944.00 $3,738,402.04 $792,559.36 $2,535,722.86 $5,531,104.09 $1,243,991.11 $1,506,609.21 $4,912,098.64 - $165,607.29 $637,724.43 $339,325.31":
          - cell "Jul-25"
          - cell "$152,148.84"
          - cell "$126,828.09"
          - cell "$1,605,563.03"
          - cell "$375,857.72"
          - cell "$133,944.00"
          - cell "$3,738,402.04"
          - cell "$792,559.36"
          - cell "$2,535,722.86"
          - cell "$5,531,104.09"
          - cell "$1,243,991.11"
          - cell "$1,506,609.21"
          - cell "$4,912,098.64"
          - cell "-"
          - cell "$165,607.29"
          - cell "$637,724.43"
          - cell "$339,325.31"
        - row "Aug-25 $180,217.41 $93,860.28 $1,444,033.90 $298,404.95 -$13,321.00 $3,387,485.06 $697,631.67 $2,214,830.85 $4,943,949.94 $1,098,229.91 $1,389,568.47 $4,318,160.93 - $137,582.49 $539,354.18 $306,968.51":
          - cell "Aug-25"
          - cell "$180,217.41"
          - cell "$93,860.28"
          - cell "$1,444,033.90"
          - cell "$298,404.95"
          - cell "-$13,321.00"
          - cell "$3,387,485.06"
          - cell "$697,631.67"
          - cell "$2,214,830.85"
          - cell "$4,943,949.94"
          - cell "$1,098,229.91"
          - cell "$1,389,568.47"
          - cell "$4,318,160.93"
          - cell "-"
          - cell "$137,582.49"
          - cell "$539,354.18"
          - cell "$306,968.51"
        - row "Sep-25 $192,123.18 $98,483.96 $1,425,470.19 $240,255.27 -$151,112.00 $3,608,495.47 $718,154.66 $2,171,276.33 $4,863,689.04 $1,206,708.93 $1,214,182.38 $4,389,146.22 - $116,221.18 $531,101.00 $292,506.31":
          - cell "Sep-25"
          - cell "$192,123.18"
          - cell "$98,483.96"
          - cell "$1,425,470.19"
          - cell "$240,255.27"
          - cell "-$151,112.00"
          - cell "$3,608,495.47"
          - cell "$718,154.66"
          - cell "$2,171,276.33"
          - cell "$4,863,689.04"
          - cell "$1,206,708.93"
          - cell "$1,214,182.38"
          - cell "$4,389,146.22"
          - cell "-"
          - cell "$116,221.18"
          - cell "$531,101.00"
          - cell "$292,506.31"
        - row "Oct-25 $190,012.66 $99,513.04 $1,377,936.99 $196,109.31 $139,663.00 $3,250,122.38 $733,554.01 $2,078,438.26 $4,727,406.69 $1,202,191.13 $1,147,608.67 $4,290,359.30 - $120,027.91 $480,960.32 $275,675.88":
          - cell "Oct-25"
          - cell "$190,012.66"
          - cell "$99,513.04"
          - cell "$1,377,936.99"
          - cell "$196,109.31"
          - cell "$139,663.00"
          - cell "$3,250,122.38"
          - cell "$733,554.01"
          - cell "$2,078,438.26"
          - cell "$4,727,406.69"
          - cell "$1,202,191.13"
          - cell "$1,147,608.67"
          - cell "$4,290,359.30"
          - cell "-"
          - cell "$120,027.91"
          - cell "$480,960.32"
          - cell "$275,675.88"
        - row "Nov-25 $319,822.88 $145,536.20 $1,514,738.71 $168,470.86 -$523,706.00 $5,335,278.24 $1,044,725.09 $2,598,607.22 $6,402,493.56 $1,743,014.03 $1,271,098.45 $5,747,305.85 - $131,727.74 $806,604.57 $360,459.54":
          - cell "Nov-25"
          - cell "$319,822.88"
          - cell "$145,536.20"
          - cell "$1,514,738.71"
          - cell "$168,470.86"
          - cell "-$523,706.00"
          - cell "$5,335,278.24"
          - cell "$1,044,725.09"
          - cell "$2,598,607.22"
          - cell "$6,402,493.56"
          - cell "$1,743,014.03"
          - cell "$1,271,098.45"
          - cell "$5,747,305.85"
          - cell "-"
          - cell "$131,727.74"
          - cell "$806,604.57"
          - cell "$360,459.54"
        - row "Dec-25 $230,575.99 $111,136.99 $1,528,135.15 $159,505.49 $190,926.00 $4,410,196.83 $924,503.66 $2,389,399.87 $5,870,082.83 $1,638,116.84 $1,038,384.04 $5,278,144.49 - $106,731.28 $643,396.46 $325,624.10":
          - cell "Dec-25"
          - cell "$230,575.99"
          - cell "$111,136.99"
          - cell "$1,528,135.15"
          - cell "$159,505.49"
          - cell "$190,926.00"
          - cell "$4,410,196.83"
          - cell "$924,503.66"
          - cell "$2,389,399.87"
          - cell "$5,870,082.83"
          - cell "$1,638,116.84"
          - cell "$1,038,384.04"
          - cell "$5,278,144.49"
          - cell "-"
          - cell "$106,731.28"
          - cell "$643,396.46"
          - cell "$325,624.10"
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
  213 | }
  214 |
  215 | /**
  216 |  * Verify user has admin privileges
  217 |  */
  218 | export async function verifyAdminAccess(page: Page): Promise<boolean> {
  219 |   try {
  220 |     await page.goto('/admin');
  221 |     await waitForPageLoad(page);
  222 |
  223 |     // Check if we're still on admin page (not redirected to sign-in)
  224 |     const currentUrl = page.url();
  225 |     return currentUrl.includes('/admin') && !currentUrl.includes('/auth/signin');
  226 |   } catch {
  227 |     return false;
  228 |   }
  229 | }
  230 |
  231 | /**
  232 |  * Test navigation through all main sections
  233 |  */
  234 | export async function testMainNavigation(page: Page) {
  235 |   const sections = [
  236 |     { path: '/dashboard', name: 'Dashboard' },
  237 |     { path: '/marketing', name: 'Marketing' },
  238 |     { path: '/admin', name: 'Admin' }
  239 |   ];
  240 |
  241 |   for (const section of sections) {
  242 |     await page.goto(section.path);
  243 |     await waitForPageLoad(page);
  244 |
  245 |     // Verify we reached the intended page
  246 |     const currentUrl = page.url();
  247 |     expect(currentUrl).toContain(section.path);
  248 |
  249 |     // Take screenshot for visual verification
  250 |     await takeScreenshot(page, `navigation-${section.name.toLowerCase()}`);
  251 |   }
  252 | }
  253 |
  254 | /**
  255 |  * Verify all expected UI elements are present on a page
  256 |  */
  257 | export async function verifyPageElements(page: Page, expectedElements: string[]) {
  258 |   for (const element of expectedElements) {
  259 |     const locator = page.locator(element).first();
  260 |     await expect(locator).toBeVisible({ timeout: 5000 });
  261 |   }
  262 | }
  263 |
  264 | /**
  265 |  * Test API endpoint response
  266 |  */
  267 | export async function testApiEndpoint(page: Page, endpoint: string, expectedStatus = 200) {
  268 |   const response = await page.request.get(endpoint);
  269 |   expect(response.status()).toBe(expectedStatus);
  270 |   return response;
  271 | }
  272 |
  273 | /**
  274 |  * Test form submission
  275 |  */
  276 | export async function testFormSubmission(page: Page, formSelector: string, formData: Record<string, string>) {
  277 |   const form = page.locator(formSelector);
  278 |   await expect(form).toBeVisible();
  279 |
  280 |   // Fill form fields
  281 |   for (const [field, value] of Object.entries(formData)) {
  282 |     await page.fill(`${formSelector} input[name="${field}"], ${formSelector} select[name="${field}"]`, value);
  283 |   }
  284 |
  285 |   // Submit form
  286 |   await page.click(`${formSelector} button[type="submit"]`);
  287 | }
  288 |
  289 | /**
  290 |  * Test data table functionality
  291 |  */
  292 | export async function testDataTable(page: Page, tableSelector = 'table') {
  293 |   const table = page.locator(tableSelector);
  294 |   await expect(table).toBeVisible();
  295 |
  296 |   // Check for headers
  297 |   const headers = table.locator('thead th');
  298 |   const headerCount = await headers.count();
  299 |   expect(headerCount).toBeGreaterThan(0);
  300 |
  301 |   // Check for data rows
  302 |   const rows = table.locator('tbody tr');
  303 |   const rowCount = await rows.count();
  304 |
  305 |   return { headerCount, rowCount };
  306 | }
  307 |
  308 | /**
  309 |  * Test chart rendering
  310 |  */
  311 | export async function testChartRendering(page: Page, chartSelector = 'svg, canvas, .recharts-wrapper') {
  312 |   const chart = page.locator(chartSelector);
> 313 |   await expect(chart).toBeVisible({ timeout: 10000 });
      |                       ^ Error: expect.toBeVisible: Error: strict mode violation: locator('svg, canvas, .recharts-wrapper') resolved to 13 elements:
  314 |
  315 |   // Wait for chart to fully render
  316 |   await page.waitForTimeout(2000);
  317 |
  318 |   return chart;
  319 | }
  320 |
  321 | /**
  322 |  * Test filter functionality
  323 |  */
  324 | export async function testFilters(page: Page) {
  325 |   // Look for common filter elements
  326 |   const filterElements = [
  327 |     'select[name*="brand"]',
  328 |     'input[type="date"]',
  329 |     'select[name*="currency"]',
  330 |     'select[name*="country"]',
  331 |     '[data-testid*="filter"]'
  332 |   ];
  333 |
  334 |   const activeFilters = [];
  335 |
  336 |   for (const selector of filterElements) {
  337 |     const element = page.locator(selector);
  338 |     if (await element.isVisible()) {
  339 |       activeFilters.push(selector);
  340 |     }
  341 |   }
  342 |
  343 |   return activeFilters;
  344 | }
  345 |
  346 | /**
  347 |  * Test pagination if present
  348 |  */
  349 | export async function testPagination(page: Page) {
  350 |   const pagination = page.locator('[data-testid="pagination"], .pagination, nav[aria-label*="pagination"]');
  351 |
  352 |   if (await pagination.isVisible()) {
  353 |     const nextButton = pagination.locator('button:has-text("Next"), button[aria-label*="next"]');
  354 |     const prevButton = pagination.locator('button:has-text("Previous"), button[aria-label*="previous"]');
  355 |
  356 |     return {
  357 |       hasPagination: true,
  358 |       hasNext: await nextButton.isVisible(),
  359 |       hasPrevious: await prevButton.isVisible()
  360 |     };
  361 |   }
  362 |
  363 |   return { hasPagination: false };
  364 | }
  365 |
  366 | /**
  367 |  * Test search functionality
  368 |  */
  369 | export async function testSearch(page: Page, searchTerm: string) {
  370 |   const searchInput = page.locator('input[type="search"], input[placeholder*="search"], input[name*="search"]');
  371 |
  372 |   if (await searchInput.isVisible()) {
  373 |     await searchInput.fill(searchTerm);
  374 |     await page.keyboard.press('Enter');
  375 |     await page.waitForTimeout(1000);
  376 |     return true;
  377 |   }
  378 |
  379 |   return false;
  380 | }
  381 |
  382 | /**
  383 |  * Test modal/dialog functionality
  384 |  */
  385 | export async function testModal(page: Page, triggerSelector: string) {
  386 |   await page.click(triggerSelector);
  387 |
  388 |   const modal = page.locator('[role="dialog"], .modal, [data-testid*="modal"]');
  389 |   await expect(modal).toBeVisible();
  390 |
  391 |   // Test close functionality
  392 |   const closeButton = modal.locator('button[aria-label*="close"], button:has-text("Close"), [data-testid*="close"]');
  393 |   if (await closeButton.isVisible()) {
  394 |     await closeButton.click();
  395 |     await expect(modal).not.toBeVisible();
  396 |   }
  397 | }
  398 |
  399 | /**
  400 |  * Test loading states
  401 |  */
  402 | export async function waitForLoadingToComplete(page: Page) {
  403 |   // Wait for common loading indicators to disappear
  404 |   const loadingSelectors = [
  405 |     '.loading',
  406 |     '.spinner',
  407 |     '[data-testid*="loading"]',
  408 |     '.animate-spin',
  409 |     'text="Loading"'
  410 |   ];
  411 |
  412 |   for (const selector of loadingSelectors) {
  413 |     try {
```