# Test info

- Name: Credentials Authentication >> should successfully sign out
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:264:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/auth/signin" until "load"
============================================================
    at signOut (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:90:14)
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:269:5
```

# Page snapshot

```yaml
- alert
- button "Open Next.js Dev Tools":
  - img
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Dashboard":
      - /url: /dashboard
  - main:
    - heading "Dashboard" [level=1]
    - heading "Overview of your business performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Countries"
    - button "Sales Channels"
    - text: Master KPI Comparison Compare 'Gross Revenue' across selected brands using a line chart.
    - combobox "Select KPI": Gross Revenue
    - img: 2/28/2025 3/31/2025 4/30/2025 0 550K 1M 2M 2M
    - text: Brands Click to toggle. Sorted by latest KPI.
    - list:
      - button "Toggle visibility for Revant" [pressed]: Revant
      - button "Toggle visibility for Qalo" [pressed]: Qalo
      - button "Toggle visibility for Ergonofis" [pressed]: Ergonofis
      - button "Toggle visibility for Opposite Wall" [pressed]: Opposite Wall
      - button "Toggle visibility for Homesick" [pressed]: Homesick
      - button "Toggle visibility for Gravity" [pressed]: Gravity
      - button "Toggle visibility for Freakmount" [pressed]: Freakmount
      - button "Toggle visibility for MiHIGH" [pressed]: MiHIGH
      - button "Toggle visibility for Rose Boreal" [pressed]: Rose Boreal
      - button "Toggle visibility for Rachel" [pressed]: Rachel
      - button "Toggle visibility for Kana" [pressed]: Kana
      - button "Toggle visibility for Arctic Tumblers" [pressed]: Arctic Tumblers
      - button "Toggle visibility for Loctote" [pressed]: Loctote
      - button "Toggle visibility for Proper Pour" [pressed]: Proper Pour
      - button "Toggle visibility for Love Your Melon" [pressed]: Love Your Melon
      - button "Toggle visibility for Wolf & Grizzly" [pressed]: Wolf & Grizzly
      - button "Toggle visibility for Go Green" [pressed]: Go Green
      - button "Toggle visibility for Corretto" [pressed]: Corretto
      - button "Toggle visibility for Alex Bottle" [pressed]: Alex Bottle
    - heading "Key Performance Indicators" [level=3]
    - text: Revenue
    - button "KPI Definition"
    - text: $17,245,998.18
    - img: 2/28/2025 4/30/2025 2,000,000 4,000,000 8,000,000
    - text: Discount
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,916,224.18
    - img: 2/28/2025 4/30/2025 200,000 400,000 800,000
    - text: Refunds
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $614,536.13
    - img: 2/28/2025 4/30/2025 70,000 140,000 280,000
    - text: Net Revenue
    - button "KPI Definition"
    - text: $14,715,237.88
    - img: 2/28/2025 4/30/2025 1,500,000 3,000,000 6,000,000
    - text: Landed Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,709,657.76
    - img: 2/28/2025 4/30/2025 300,000 600,000 1,200,000
    - text: Fulfillment Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,167,088.33
    - img: 2/28/2025 4/30/2025 250,000 500,000 1,000,000
    - text: Transaction Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,034,384.19
    - img: 2/28/2025 4/30/2025 95,000 190,000 380,000
    - text: Gross Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $9,520,150.47
    - img: 2/28/2025 4/30/2025 900,000 1,800,000 3,600,000
    - text: Adspend
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $3,424,904.20
    - img: 2/28/2025 4/30/2025 350,000 700,000 1,400,000
    - text: Contribution Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $12,945,054.68
    - img: 2/28/2025 4/30/2025 1,500,000 3,000,000 6,000,000
```

# Test source

```ts
   1 | import { Page, expect } from '@playwright/test';
   2 |
   3 | /**
   4 |  * Helper functions for E2E tests
   5 |  */
   6 |
   7 | /**
   8 |  * Wait for the page to be fully loaded
   9 |  */
   10 | export async function waitForPageLoad(page: Page) {
   11 |   await page.waitForLoadState('networkidle');
   12 |   await page.waitForLoadState('domcontentloaded');
   13 | }
   14 |
   15 | /**
   16 |  * Sign in with Google (mock for testing)
   17 |  */
   18 | export async function signInWithGoogle(page: Page) {
   19 |   // Navigate to sign-in page
   20 |   await page.goto('/auth/signin');
   21 |   await waitForPageLoad(page);
   22 |
   23 |   // Click the Google sign-in button
   24 |   await page.click('button:has-text("Continue with Google")');
   25 |
   26 |   // In a real test environment, you would handle OAuth flow
   27 |   // For now, we'll assume the sign-in is successful and we're redirected
   28 |   await page.waitForURL('/dashboard');
   29 |   await waitForPageLoad(page);
   30 | }
   31 |
   32 | /**
   33 |  * Sign in with username and password credentials
   34 |  */
   35 | export async function signInWithCredentials(page: Page, username: string, password: string) {
   36 |   // Navigate to sign-in page
   37 |   await page.goto('/auth/signin');
   38 |   await waitForPageLoad(page);
   39 |
   40 |   // Switch to credentials login
   41 |   const credentialsTab = page.locator('button:has-text("Username/Password")');
   42 |   await credentialsTab.click();
   43 |   await page.waitForTimeout(1000); // Wait for tab switch animation
   44 |
   45 |   // Wait for the form to be visible
   46 |   await page.waitForSelector('#username', { state: 'visible', timeout: 5000 });
   47 |
   48 |   // Fill in credentials using id selectors
   49 |   await page.fill('#username', username);
   50 |   await page.fill('#password', password);
   51 |
   52 |   // Click sign in button
   53 |   await page.click('button[type="submit"]:has-text("Sign In")');
   54 |
   55 |   // Wait for redirect to dashboard
   56 |   await page.waitForURL('/dashboard', { timeout: 15000 });
   57 |   await waitForPageLoad(page);
   58 | }
   59 |
   60 | /**
   61 |  * Sign in with the test admin user (farbour/admin)
   62 |  */
   63 | export async function signInAsAdmin(page: Page) {
   64 |   await signInWithCredentials(page, 'farbour', 'admin');
   65 | }
   66 |
   67 | /**
   68 |  * Sign out from the application
   69 |  */
   70 | export async function signOut(page: Page) {
   71 |   // Look for user menu or sign out button
   72 |   const userMenu = page.locator('[data-testid="user-menu"]').or(
   73 |     page.locator('button:has-text("Sign out")')
   74 |   );
   75 |
   76 |   if (await userMenu.isVisible()) {
   77 |     await userMenu.click();
   78 |
   79 |     // Look for sign out option
   80 |     const signOutButton = page.locator('button:has-text("Sign out")').or(
   81 |       page.locator('[data-testid="sign-out"]')
   82 |     );
   83 |
   84 |     if (await signOutButton.isVisible()) {
   85 |       await signOutButton.click();
   86 |     }
   87 |   }
   88 |
   89 |   // Wait for redirect to sign-in page
>  90 |   await page.waitForURL('/auth/signin');
      |              ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
   91 | }
   92 |
   93 | /**
   94 |  * Navigate to a specific page and wait for it to load
   95 |  */
   96 | export async function navigateToPage(page: Page, path: string) {
   97 |   await page.goto(path);
   98 |   await waitForPageLoad(page);
   99 | }
  100 |
  101 | /**
  102 |  * Check if user is authenticated
  103 |  */
  104 | export async function isAuthenticated(page: Page): Promise<boolean> {
  105 |   try {
  106 |     // Check if we're on the dashboard or any authenticated page
  107 |     const currentUrl = page.url();
  108 |     return !currentUrl.includes('/auth/signin');
  109 |   } catch {
  110 |     return false;
  111 |   }
  112 | }
  113 |
  114 | /**
  115 |  * Wait for a specific element to be visible
  116 |  */
  117 | export async function waitForElement(page: Page, selector: string, timeout = 10000) {
  118 |   await page.waitForSelector(selector, { state: 'visible', timeout });
  119 | }
  120 |
  121 | /**
  122 |  * Take a screenshot with a descriptive name
  123 |  */
  124 | export async function takeScreenshot(page: Page, name: string) {
  125 |   await page.screenshot({
  126 |     path: `tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`,
  127 |     fullPage: true
  128 |   });
  129 | }
  130 |
  131 | /**
  132 |  * Check if the sidebar navigation is working
  133 |  */
  134 | export async function testSidebarNavigation(page: Page) {
  135 |   // Test main navigation items
  136 |   const navItems = [
  137 |     { text: 'Dashboard', url: '/dashboard' },
  138 |     { text: 'Brand Deep Dive', url: '/brand-deep-dive' },
  139 |     { text: 'Marketing Dashboard', url: '/marketing-dashboard' },
  140 |     { text: 'Executive Summary', url: '/executive-summary' },
  141 |     { text: 'Budget', url: '/budget' },
  142 |     { text: 'AI Assistant', url: '/ai-assistant' }
  143 |   ];
  144 |
  145 |   for (const item of navItems) {
  146 |     const navLink = page.locator(`nav a:has-text("${item.text}")`);
  147 |     if (await navLink.isVisible()) {
  148 |       await navLink.click();
  149 |       await page.waitForURL(`**${item.url}`);
  150 |       await waitForPageLoad(page);
  151 |
  152 |       // Verify we're on the correct page
  153 |       expect(page.url()).toContain(item.url);
  154 |     }
  155 |   }
  156 | }
  157 |
  158 | /**
  159 |  * Test responsive design by changing viewport
  160 |  */
  161 | export async function testResponsiveDesign(page: Page) {
  162 |   const viewports = [
  163 |     { width: 1920, height: 1080, name: 'Desktop' },
  164 |     { width: 768, height: 1024, name: 'Tablet' },
  165 |     { width: 375, height: 667, name: 'Mobile' }
  166 |   ];
  167 |
  168 |   for (const viewport of viewports) {
  169 |     await page.setViewportSize({ width: viewport.width, height: viewport.height });
  170 |     await page.waitForTimeout(1000); // Allow time for responsive changes
  171 |
  172 |     // Take screenshot for visual verification
  173 |     await takeScreenshot(page, `responsive-${viewport.name.toLowerCase()}`);
  174 |   }
  175 | }
  176 |
  177 | /**
  178 |  * Check for console errors
  179 |  */
  180 | export async function checkConsoleErrors(page: Page): Promise<string[]> {
  181 |   const errors: string[] = [];
  182 |
  183 |   page.on('console', (msg) => {
  184 |     if (msg.type() === 'error') {
  185 |       errors.push(msg.text());
  186 |     }
  187 |   });
  188 |
  189 |   return errors;
  190 | }
```