# Test info

- Name: Comprehensive Admin Dashboard Testing >> should load admin dashboard with proper authorization
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:23:7

# Error details

```
Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)

Locator: locator(':root')
Expected pattern: /Admin|NOLK/
Received string:  "Create Next App"
Call log:
  - expect.toHaveTitle with timeout 15000ms
  - waiting for locator(':root')
    19 × locator resolved to <html lang="en">…</html>
       - unexpected value "Create Next App"

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-admin-dashboard.spec.ts:33:24
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "Admin":
      - /url: /admin
      - img
      - text: Admin
- list:
  - listitem:
    - link "Back to Dashboard":
      - /url: /dashboard
      - img
      - text: Back to Dashboard
  - listitem:
    - link "Dashboard":
      - /url: /admin
      - img
      - text: Dashboard
  - listitem:
    - link "Users":
      - /url: /admin/users
      - img
      - text: Users
  - listitem:
    - link "Roles":
      - /url: /admin/roles
      - img
      - text: Roles
  - listitem:
    - link "Permissions":
      - /url: /admin/permissions
      - img
      - text: Permissions
  - listitem:
    - link "Groups":
      - /url: /admin/groups
      - img
      - text: Groups
  - listitem:
    - link "Brands":
      - /url: /admin/brands
      - img
      - text: Brands
  - listitem:
    - link "DB Structure":
      - /url: /admin/db-structure
      - img
      - text: DB Structure
  - listitem:
    - link "Backups":
      - /url: /admin/backups
      - img
      - text: Backups
- list:
  - listitem:
    - link "Main Dashboard":
      - /url: /dashboard
      - img
      - text: Main Dashboard
  - listitem:
    - link "Settings":
      - /url: /admin/settings
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Admin":
      - /url: /admin
  - main:
    - heading "Admin Dashboard" [level=1]
    - paragraph: Manage users, roles, and system settings
    - text: System Active
    - heading "Key Metrics" [level=2]
    - text: Live Data Total Users 14
    - paragraph: Active system users
    - text: Total Brands 23
    - paragraph: Managed brands
    - text: Total Roles 4
    - paragraph: Permission roles
    - text: Total Groups 4
    - paragraph: User groups
    - text: Total Permissions 2
    - paragraph: System permissions
    - heading "Recent Activity" [level=2]
    - text: Latest User Additions
    - paragraph: François Arbour
    - paragraph: <EMAIL>
    - paragraph: 5/13/2025
    - paragraph: Marin Savignac
    - paragraph: <EMAIL>
    - paragraph: 5/13/2025
    - paragraph: Win brand user
    - paragraph: <EMAIL>
    - paragraph: 5/13/2025
    - paragraph: Francois Arbour
    - paragraph: <EMAIL>
    - paragraph: 12/31/1969
    - paragraph: arenaud
    - paragraph: <EMAIL>
    - paragraph: 12/31/1969
    - text: Recent Login Activities
    - paragraph: No recent login activities
    - paragraph: Login tracking will appear here once implemented
    - heading "Management Sections" [level=2]
    - link "User Management Manage users, roles, and access permissions 14 users":
      - /url: /admin/users
      - text: User Management
      - paragraph: Manage users, roles, and access permissions
      - text: 14 users
    - link "Role Management Configure user roles and permission sets 4 roles":
      - /url: /admin/roles
      - text: Role Management
      - paragraph: Configure user roles and permission sets
      - text: 4 roles
    - link "Permission Management Define and manage application permissions 2 permissions":
      - /url: /admin/permissions
      - text: Permission Management
      - paragraph: Define and manage application permissions
      - text: 2 permissions
    - link "Group Management Organize users into logical groups 4 groups":
      - /url: /admin/groups
      - text: Group Management
      - paragraph: Organize users into logical groups
      - text: 4 groups
    - link "Brand Management Manage brand information and settings 23 brands":
      - /url: /admin/brands
      - text: Brand Management
      - paragraph: Manage brand information and settings
      - text: 23 brands
    - link "Database Structure View and manage database schema Schema info":
      - /url: /admin/db-structure
      - text: Database Structure
      - paragraph: View and manage database schema
      - text: Schema info
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 | import {
   3 |   waitForPageLoad,
   4 |   signInAsAdmin,
   5 |   takeScreenshot,
   6 |   testDataTable,
   7 |   testFormSubmission,
   8 |   testModal,
   9 |   testSearch,
   10 |   testPagination,
   11 |   waitForLoadingToComplete,
   12 |   testAccessibility,
   13 |   verifyPageElements,
   14 |   testApiEndpoint
   15 | } from './utils/test-helpers';
   16 |
   17 | test.describe('Comprehensive Admin Dashboard Testing', () => {
   18 |   test.beforeEach(async ({ page }) => {
   19 |     // Sign in as admin for all tests
   20 |     await signInAsAdmin(page);
   21 |   });
   22 |
   23 |   test('should load admin dashboard with proper authorization', async ({ page }) => {
   24 |     await page.goto('/admin');
   25 |     await waitForPageLoad(page);
   26 |     await waitForLoadingToComplete(page);
   27 |
   28 |     // Verify we're on admin page and not redirected to sign-in
   29 |     expect(page.url()).toContain('/admin');
   30 |     expect(page.url()).not.toContain('/auth/signin');
   31 |
   32 |     // Verify page title and main elements
>  33 |     await expect(page).toHaveTitle(/Admin|NOLK/);
      |                        ^ Error: Timed out 15000ms waiting for expect(locator).toHaveTitle(expected)
   34 |     
   35 |     // Check for admin-specific elements
   36 |     const expectedElements = [
   37 |       'main, [role="main"]',
   38 |       'nav, [role="navigation"]',
   39 |       'h1, h2, [data-testid*="admin"]'
   40 |     ];
   41 |     
   42 |     await verifyPageElements(page, expectedElements);
   43 |     await takeScreenshot(page, 'admin-dashboard-main');
   44 |   });
   45 |
   46 |   test('should display admin navigation menu', async ({ page }) => {
   47 |     await page.goto('/admin');
   48 |     await waitForPageLoad(page);
   49 |
   50 |     // Look for admin navigation items
   51 |     const adminNavItems = [
   52 |       'Users', 'Roles', 'Permissions', 'Groups', 
   53 |       'Brands', 'Settings', 'Backups', 'DB Structure'
   54 |     ];
   55 |
   56 |     let foundNavItems = 0;
   57 |     for (const item of adminNavItems) {
   58 |       const navItem = page.locator(`nav a:has-text("${item}"), a[href*="${item.toLowerCase()}"]`);
   59 |       if (await navItem.isVisible()) {
   60 |         foundNavItems++;
   61 |       }
   62 |     }
   63 |
   64 |     console.log(`Found ${foundNavItems} admin navigation items out of ${adminNavItems.length} expected`);
   65 |     await takeScreenshot(page, 'admin-navigation-menu');
   66 |   });
   67 |
   68 |   test('should navigate to and test Users management', async ({ page }) => {
   69 |     await page.goto('/admin/users');
   70 |     await waitForPageLoad(page);
   71 |     await waitForLoadingToComplete(page);
   72 |
   73 |     // Verify users page loaded
   74 |     expect(page.url()).toContain('/admin/users');
   75 |     
   76 |     // Test users table
   77 |     const tableInfo = await testDataTable(page);
   78 |     if (tableInfo.headerCount > 0) {
   79 |       console.log(`Users table has ${tableInfo.headerCount} headers and ${tableInfo.rowCount} rows`);
   80 |     }
   81 |
   82 |     // Look for user management buttons
   83 |     const addUserButton = page.locator('button:has-text("Add"), button:has-text("Create"), [data-testid*="add-user"]');
   84 |     if (await addUserButton.isVisible()) {
   85 |       await takeScreenshot(page, 'admin-users-page');
   86 |     }
   87 |
   88 |     // Test search functionality
   89 |     const searchWorked = await testSearch(page, 'admin');
   90 |     if (searchWorked) {
   91 |       await takeScreenshot(page, 'admin-users-search');
   92 |     }
   93 |   });
   94 |
   95 |   test('should navigate to and test Roles management', async ({ page }) => {
   96 |     await page.goto('/admin/roles');
   97 |     await waitForPageLoad(page);
   98 |     await waitForLoadingToComplete(page);
   99 |
  100 |     // Verify roles page loaded
  101 |     expect(page.url()).toContain('/admin/roles');
  102 |     
  103 |     // Test roles table/list
  104 |     const tableInfo = await testDataTable(page);
  105 |     if (tableInfo.headerCount > 0) {
  106 |       console.log(`Roles table has ${tableInfo.headerCount} headers and ${tableInfo.rowCount} rows`);
  107 |     }
  108 |
  109 |     // Look for role management features
  110 |     const roleElements = page.locator('text=/admin|user|manager|viewer/i');
  111 |     const roleCount = await roleElements.count();
  112 |     console.log(`Found ${roleCount} role-related elements`);
  113 |
  114 |     await takeScreenshot(page, 'admin-roles-page');
  115 |   });
  116 |
  117 |   test('should navigate to and test Permissions management', async ({ page }) => {
  118 |     await page.goto('/admin/permissions');
  119 |     await waitForPageLoad(page);
  120 |     await waitForLoadingToComplete(page);
  121 |
  122 |     // Verify permissions page loaded
  123 |     expect(page.url()).toContain('/admin/permissions');
  124 |     
  125 |     // Test permissions table/list
  126 |     const tableInfo = await testDataTable(page);
  127 |     if (tableInfo.headerCount > 0) {
  128 |       console.log(`Permissions table has ${tableInfo.headerCount} headers and ${tableInfo.rowCount} rows`);
  129 |     }
  130 |
  131 |     // Look for permission-related elements
  132 |     const permissionElements = page.locator('text=/read|write|delete|create|update/i');
  133 |     const permissionCount = await permissionElements.count();
```